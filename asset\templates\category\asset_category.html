{% load assets_custom_filter %}
{% load static i18n %}
{% load i18n %}

<!-- start of messages -->
{% if messages %}
    <div class="oh-wrapper">
        {% for message in messages %}
        <div class="oh-alert-container">
            <div class="oh-alert oh-alert--animated {{message.tags}}">
                {{ message }}
            </div>
        </div>
        {% endfor %}
    </div>
{% endif %}
<!-- end of messages -->
<div class="d-flex" style="flex-direction: row-reverse;">
    <div id="successMessage" style="display:none;" class="oh-alert oh-alert--animated oh-alert--success w-25">
        {% trans "Report added successfully." %}
    </div>
</div>
<!-- new acordian  -->
{% include 'filter_tags.html' %}
<div class="oh-card" id="assetFiltered">
    <div class="oh-accordion-meta">
        <div class="">
            {% for asset_category in asset_categories %}
                <div class="oh-accordion-meta__header oh-accordion-meta__header--custom"
                    data-target="#assetCategory{{asset_category.id}}"
                    hx-get="{%url 'asset-list' cat_id=asset_category.id %}?category={{asset_category.pk}}&{{request.GET.urlencode}}"
                    hx-trigger="load" hx-target="#assetCategory{{asset_category.id}}"
                    {% if not perms.asset.add_asset or not perms.asset.change_assetcategory or not perms.asset.delete_assetcategory %}
                        style="height: 61px;"
                    {% endif %}>
                    <div class="d-flex">
                        <span class="oh-badge oh-badge--secondary oh-badge--small oh-badge--round ms-2 mr-2"
                            id="asset-count{{asset_category.id}}" data-category-id="{{asset_category.id}}"
                            title="{{asset_category.asset_set.count}} {% trans 'Assets' %}">
                            {{asset_category.asset_set.count}}
                        </span>
                        <span class="oh-accordion-meta__title">{{asset_category}}</span>
                    </div>
                    {% if perms.asset.add_asset or perms.asset.add_assetcategory or perms.asset.change_assetcategory or perms.asset.delete_assetcategory %}
                        <div class="oh-accordion-meta__actions" id="assetActions">
                            <div class="oh-dropdown" x-data="{open: false}">
                                <button class="oh-btn oh-stop-prop oh-accordion-meta__btn" @click="open = !open"
                                    @click.outside="open = false" onclick="event.stopPropagation()">
                                    {% trans "Actions" %}
                                    <ion-icon class="ms-2 oh-accordion-meta__btn-icon" name="caret-down-outline"></ion-icon>
                                </button>
                                <div class="oh-dropdown__menu oh-dropdown__menu--right" x-show="open" style="display: none;">
                                    <ul class="oh-dropdown__items">
                                        {% if perms.asset.add_asset %}
                                            <li class="oh-dropdown__item">
                                                <a href="#" class="oh-dropdown__link asset-create" data-toggle="oh-modal-toggle"
                                                    onclick="event.stopPropagation()" data-target="#objectCreateModal"
                                                    hx-get="{% url 'asset-creation' asset_category_id=asset_category.id %}?{{pg}}"
                                                    hx-target="#objectCreateModalTarget" data-category-id="{{asset_category.id}}">
                                                    {% trans "Create" %}</a>
                                            </li>
                                        {% endif %}
                                        {% if perms.asset.add_assetcategory %}
                                            <li class="oh-dropdown__item">
                                                <a href="#" class="oh-dropdown__link " data-toggle="oh-modal-toggle"
                                                    onclick="event.stopPropagation()" data-target="#objectCreateModal"
                                                    hx-get="{% url 'asset-category-duplicate' obj_id=asset_category.id %}?{{pg}}"
                                                    hx-target="#objectCreateModalTarget">{% trans "Duplicate" %}</a>
                                            </li>
                                        {% endif %}
                                        {% if perms.asset.change_assetcategory %}
                                            <li class="oh-dropdown__item">
                                                <a href="#" class="oh-dropdown__link " data-toggle="oh-modal-toggle"
                                                    onclick="event.stopPropagation()" data-target="#objectUpdateModal"
                                                    hx-get="{% url 'asset-category-update' cat_id=asset_category.id %}?{{pg}}"
                                                    hx-target="#objectUpdateModalTarget">{% trans "Edit" %}</a>
                                            </li>
                                        {% endif %}
                                        {% if perms.asset.delete_assetcategory %}
                                            <li class="oh-dropdown__item">
                                                <a hx-confirm="{% trans 'Do you want to delete this category?' %}"
                                                    hx-post="{% url 'asset-category-delete' asset_category.id %}?{{pg}}"
                                                    hx-target="#assetCategoryList"
                                                    class="oh-dropdown__menu--right oh-dropdown__link--danger"
                                                    style="text-decoration: none;" onclick="event.stopPropagation();">
                                                    {% trans "Delete" %}
                                                </a>
                                            </li>
                                        {% endif %}
                                    </ul>
                                </div>
                            </div>
                        </div>
                    {% endif %}
                </div>
                <div class="oh-accordion-meta__body d-none" id="assetCategory{{asset_category.id}}">
                    <!-- htmx asset list loading here -->
                    {% comment %}
                    <div class="oh-sticky-table oh-sticky-table--no-overflow mb-5"
                        id="assetList{{asset_category.id}}">
                    </div>
                    {% endcomment %}
                </div>
            {% endfor %}

            <!-- pagination start -->
            <div class="oh-pagination">
                <span class="oh-pagination__page" data-toggle="modal" data-target="#addEmployeeModal"></span>
                <nav class="oh-pagination__nav">
                    <div class="oh-pagination__input-container me-3">
                        <span class="oh-pagination__label me-1">{% trans "Page" %} </span>
                        <input type="number" name="page" class="oh-pagination__input"
                            value="{{asset_categories.number }}" min="1"
                            hx-get="{% url 'asset-category-view-search-filter' %}" hx-target="#assetCategoryList">
                        <span class="oh-pagination__label">{% trans "of" %} {{ asset_categories.paginator.num_pages }}</span>
                    </div>
                    <ul class="oh-pagination__items">
                        {% if asset_categories.has_previous %}
                            <li class="oh-pagination__item oh-pagination__item--wide">
                                <a hx-get="{% url 'asset-category-view-search-filter' %}?{{pg}}&page=1"
                                    class='oh-pagination__link' hx-target="#assetCategoryList">{% trans "First" %}</a>
                            </li>
                            <li class="oh-pagination__item oh-pagination__item--wide">
                                <a hx-get="{% url 'asset-category-view-search-filter' %}?{{pg}}&page={{ asset_categories.previous_page_number }}"
                                    class='oh-pagination__link' hx-target="#assetCategoryList">{% trans "Previous" %}</a>
                            </li>
                        {% endif %}
                        {% if asset_categories.has_next %}
                            <li class="oh-pagination__item oh-pagination__item--wide">
                                <a hx-get="{% url 'asset-category-view-search-filter' %}?{{pg}}&page={{ asset_categories.next_page_number}}"
                                    class='btn btn-outline-secondary' hx-target="#assetCategoryList">{% trans "Next" %}</a>
                            </li>
                            <li class="oh-pagination__item oh-pagination__item--wide">
                                <a hx-get="{% url 'asset-category-view-search-filter' %}?{{pg}}&page={{ asset_categories.paginator.num_pages }}"
                                    hx-target="#assetCategoryList" class="oh-pagination__link">{% trans "Last" %}</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
            <!-- end of pagination -->
        </div>
    </div>
</div>
</div>
{% if perms.asset.add_assetlot %}
    <span name="" id="dynamicBatchNoModal" style="display: none" data-toggle="oh-modal-toggle"
        data-target="#dynamicCreateModal" hx-get="{% url 'asset-batch-number-creation' %}"
        hx-target="#dynamicCreateModalTarget">
    </span>
{% endif %}
<script>
    // action button seperating from the acordion
    $('#assetActions').on('click', function (e) {
        e.stopPropagation();
    });
    function batchNoChange(selectElement) {
        var selectedBatchNo = selectElement.val();
        var parentForm = selectElement.parents().closest("form");
        var dataId = parentForm.data("id");
        if (selectedBatchNo == "create") {
            let dynamicBatchNo = $("#dynamicBatchNoModal");
            var view = parentForm.serialize()
            view += `&asset_category_id=${dataId}`;
            dynamicBatchNo.attr("hx-vals", `{"data":"${view}"}`);
            dynamicBatchNo.click();
        }
    }
</script>
