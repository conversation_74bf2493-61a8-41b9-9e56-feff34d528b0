{% load i18n %}
{% if messages %}
	<script>reloadMessage();</script>
	<span hx-get="{% url 'attendance-settings-view' %}" hx-target="#attendanceValidationConditions"
		hx-select="#attendanceValidationConditions" hx-swap="outerHTML" hx-trigger="load delay:1s"
		hx-on-htmx-after-request="setTimeout(() => { $('.oh-modal__close').click(); reloadMessage(); }, 300);">
	</span>
{% endif %}
<div class="oh-modal__dialog-header pb-0">
	<h2 class="oh-modal__dialog-title">
		{% if condition.id %}
			{% trans "Update Attendance condition " %}
		{% else %}
			{% trans "Create Attendance condition " %}
		{% endif %}
	</h2>
	<button class="oh-modal__close" aria-label="Close">
		<ion-icon name="close-outline"></ion-icon>
	</button>
</div>
<div class="oh-modal__dialog-body" id="createForm">
	<form class="oh-profile-section" {% if condition.id %} hx-post="{% url 'attendance-settings-update' condition.id %}"
		hx-target="#objectUpdateModalTarget" {% else %} hx-post="{% url 'attendance-settings-create' %}"
		hx-target="#objectCreateModalTarget" {% endif %}>
		{% csrf_token %}
		{{form.as_p}}
		<div class="oh-modal__dialog-footer p-0 mt-3">
			<button type="submit" class="oh-btn oh-btn--secondary oh-btn--shadow">
				{% trans "Save" %}
			</button>
		</div>
	</form>
</div>
