{% load i18n %}
{% if messages %}
    <div class="oh-alert-container">
      {% for message in messages %}
      <div class="oh-alert oh-alert--animated {{message.tags}}">{{ message }}</div>
      {% endfor %}
    </div>
<script>
    setTimeout(() => {
        $(".oh-modal__close").click();
    }, 1000);
</script>
{% endif %}
<div class="oh-modal__dialog-header">
    <h2 class="oh-modal__dialog-title" id="createModalTitle">
      {% trans "Add Comment" %}
    </h2>
    <button
      class="oh-modal__close"
      aria-label="Close"
      hx-get="{% url 'search-attendance-requests' %}?{{pd}}"
      hx-target="#view-container"
        >
        <ion-icon name="close-outline"></ion-icon>
    </button>
</div>
<div class="oh-modal__dialog-body">
    {% if form.errors %}
        <!-- form errors  -->
        <div class="oh-wrapper">
            <div class="oh-alert-container">
                {% for error in form.non_field_errors %}
                    <div class="oh-alert oh-alert--animated oh-alert--danger">
                        {{ error }}
                    </div>
                {% endfor %}
            </div>
        </div>
    {% endif %}
    <form
      hx-post="{% url 'attendance-request-add-comment' request_id %}?{{pd}}"
      hx-target="#shiftRequestCommentForm"
      method="post"
      hx-encoding="multipart/form-data"
     >
        {% csrf_token %} {{form.as_p}}
        <button
          type="submit"
          class="oh-btn oh-btn--secondary mt-2 mr-0 oh-btn--w-100-resp"
          >
          {% trans "Save" %}
        </button>
    </form>
</div>
