{% extends 'settings.html' %} {% load i18n %} {% block settings %}
{% load static %}

<!-- ******************* HISTORY TAGS ******************* -->

<div class="oh-inner-sidebar-content">
  {% if perms.eaglora_audit.view_audittag %}
  <div
    class="oh-inner-sidebar-content__header d-flex justify-content-between align-items-center"
  >
    <h2 class="oh-inner-sidebar-content__title">{% trans "History Tags" %}</h2>
    {% if perms.eaglora_audit.add_audittag %}
    	<button
    	  class="oh-btn oh-btn--secondary oh-btn--shadow"
    	  data-toggle="oh-modal-toggle"
    	  data-target="#audittagModal"
    	  hx-get="{% url 'audit-tag-create' %}"
    	  hx-target="#audittagForm"
    		>
    	  <ion-icon name="add-outline" class="me-1"></ion-icon>
    	  {% trans "Create" %}
    	</button>
    {% endif %}
  </div>
  {% if audittags %}
  	{% include 'base/audit_tag/audit_tag_view.html' %}
  {% else %}
  <div
    style="
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 100%;
    "
  >
    <img
      style="
        display: block;
        width: 15%;
        margin: 20px auto;
        filter: opacity(0.5);
      "
      src="{% static 'images/ui/price-tag.png' %}"
      class=""
      alt="Page not found. 404."
    />
    <h5 class="oh-404__subtitle">
      {% trans "There is no history tags at this moment." %}
    </h5>
  </div>
  {% endif %} {% endif %}
</div>

<!-- start of create modal -->
<div
  class="oh-modal"
  id="audittagModal"
  role="dialog"
  aria-labelledby="audittagModal"
  aria-hidden="true"
>
  <div class="oh-modal__dialog" id="audittagForm"></div>
</div>
<!-- end of create modal -->

<!-- start of edit modal -->
<div
  class="oh-modal"
  id="audittagEditModal"
  role="dialog"
  aria-labelledby="audittagEditModal"
  aria-hidden="true"
>
  <div class="oh-modal__dialog" id="audittagEditForm"></div>
</div>
<!-- end of edit modal -->

{% endblock settings %}
