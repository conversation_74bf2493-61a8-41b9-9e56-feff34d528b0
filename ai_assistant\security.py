"""
Security Module for AI Assistant

This module provides security validation and input sanitization for the <PERSON> assistant.
"""

import re
import logging
from typing import Dict, Any, List
from django.contrib.auth.models import User
from django.core.exceptions import PermissionDenied

logger = logging.getLogger(__name__)


class SecurityValidator:
    """Validate AI assistant commands for security and permissions."""
    
    def __init__(self, user: User):
        self.user = user
        
        # Define dangerous patterns that should be blocked
        self.dangerous_patterns = [
            r'\b(delete|drop|truncate|alter)\b.*\b(table|database|schema)\b',
            r'\b(exec|execute|eval|system|shell)\b',
            r'<script[^>]*>.*?</script>',
            r'javascript:',
            r'on\w+\s*=',
            r'\b(union|select|insert|update|delete)\b.*\b(from|into|set|where)\b',
        ]
        
        # Define allowed modules based on user permissions
        self.module_permissions = {
            'employee': ['view_employee', 'add_employee', 'change_employee'],
            'leave': ['view_leaverequest', 'add_leaverequest', 'change_leaverequest'],
            'attendance': ['view_attendance', 'add_attendance', 'change_attendance'],
            'payroll': ['view_payslip', 'add_payslip', 'change_payslip'],
            'recruitment': ['view_recruitment', 'add_recruitment', 'change_recruitment'],
        }
    
    def validate_command(self, command: str, result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate a command for security issues and permissions.
        
        Args:
            command: The original user command
            result: The processed command result
            
        Returns:
            Validated and potentially modified result
        """
        try:
            # Check for dangerous patterns
            if self._contains_dangerous_patterns(command):
                logger.warning(f"Dangerous pattern detected in command: {command}")
                result['intent'] = 'error'
                result['message'] = "Sorry, I cannot process that request for security reasons."
                result['redirect_url'] = None
                return result
            
            # Check module permissions
            module = result.get('module', 'general')
            if not self._has_module_permission(module, result.get('intent', 'view')):
                logger.warning(f"Permission denied for user {self.user.username} on module {module}")
                result['intent'] = 'error'
                result['message'] = "You don't have permission to access this functionality."
                result['redirect_url'] = None
                return result
            
            # Validate redirect URL
            redirect_url = result.get('redirect_url')
            if redirect_url and not self._is_safe_url(redirect_url):
                logger.warning(f"Unsafe URL detected: {redirect_url}")
                result['redirect_url'] = None
                result['message'] = "Invalid URL detected. Please try a different request."
            
            return result
            
        except Exception as e:
            logger.error(f"Error in security validation: {e}")
            result['intent'] = 'error'
            result['message'] = "Security validation failed. Please try again."
            return result
    
    def _contains_dangerous_patterns(self, text: str) -> bool:
        """Check if text contains dangerous patterns."""
        text_lower = text.lower()
        
        for pattern in self.dangerous_patterns:
            if re.search(pattern, text_lower, re.IGNORECASE):
                return True
        
        return False
    
    def _has_module_permission(self, module: str, intent: str) -> bool:
        """Check if user has permission for the module and intent."""
        if module == 'general':
            return True
        
        # Map intent to permission type
        permission_map = {
            'search': 'view',
            'view': 'view',
            'fill_form': 'add',
            'create': 'add',
            'edit': 'change',
            'update': 'change',
            'delete': 'delete',
            'export': 'view',
            'import': 'add',
        }
        
        permission_type = permission_map.get(intent, 'view')
        required_permissions = self.module_permissions.get(module, [])
        
        if not required_permissions:
            return True  # Allow if no specific permissions defined
        
        # Check if user has any of the required permissions
        for perm in required_permissions:
            if permission_type in perm and self.user.has_perm(f"{module}.{perm}"):
                return True
        
        # For superusers, allow everything
        if self.user.is_superuser:
            return True
        
        # For staff users, allow view operations
        if self.user.is_staff and permission_type == 'view':
            return True
        
        return False
    
    def _is_safe_url(self, url: str) -> bool:
        """Check if URL is safe for redirection."""
        if not url:
            return False
        
        # Must start with / (relative URL)
        if not url.startswith('/'):
            return False
        
        # Must not contain dangerous characters
        dangerous_chars = ['<', '>', '"', "'", '&', ';']
        if any(char in url for char in dangerous_chars):
            return False
        
        # Must not contain javascript or data URLs
        if 'javascript:' in url.lower() or 'data:' in url.lower():
            return False
        
        return True


class InputSanitizer:
    """Sanitize user inputs for the AI assistant."""
    
    @staticmethod
    def sanitize_command(command: str) -> str:
        """
        Sanitize user command input.
        
        Args:
            command: Raw user command
            
        Returns:
            Sanitized command
        """
        if not command:
            return ""
        
        # Remove excessive whitespace
        command = re.sub(r'\s+', ' ', command.strip())
        
        # Remove HTML tags
        command = re.sub(r'<[^>]+>', '', command)
        
        # Remove script tags and content
        command = re.sub(r'<script[^>]*>.*?</script>', '', command, flags=re.IGNORECASE | re.DOTALL)
        
        # Remove dangerous attributes
        command = re.sub(r'on\w+\s*=\s*["\'][^"\']*["\']', '', command, flags=re.IGNORECASE)
        
        # Limit length
        if len(command) > 1000:
            command = command[:1000]
        
        return command
    
    @staticmethod
    def sanitize_filters(filters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Sanitize filter dictionary.
        
        Args:
            filters: Dictionary of filters
            
        Returns:
            Sanitized filters
        """
        if not filters:
            return {}
        
        sanitized = {}
        
        for key, value in filters.items():
            # Sanitize key
            clean_key = re.sub(r'[^a-zA-Z0-9_]', '', str(key))
            if not clean_key:
                continue
            
            # Sanitize value
            if isinstance(value, str):
                clean_value = InputSanitizer.sanitize_command(value)
                if clean_value:
                    sanitized[clean_key] = clean_value
            elif isinstance(value, (int, float, bool)):
                sanitized[clean_key] = value
            elif isinstance(value, list):
                clean_list = []
                for item in value:
                    if isinstance(item, str):
                        clean_item = InputSanitizer.sanitize_command(item)
                        if clean_item:
                            clean_list.append(clean_item)
                    elif isinstance(item, (int, float, bool)):
                        clean_list.append(item)
                if clean_list:
                    sanitized[clean_key] = clean_list
        
        return sanitized
    
    @staticmethod
    def sanitize_prefill_data(prefill_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Sanitize prefill data for forms.
        
        Args:
            prefill_data: Dictionary of prefill data
            
        Returns:
            Sanitized prefill data
        """
        return InputSanitizer.sanitize_filters(prefill_data)


class PermissionChecker:
    """Check specific permissions for AI assistant operations."""
    
    def __init__(self, user: User):
        self.user = user
    
    def can_view_employees(self) -> bool:
        """Check if user can view employees."""
        return (
            self.user.has_perm('employee.view_employee') or
            self.user.is_staff or
            self.user.is_superuser
        )
    
    def can_create_leave_request(self) -> bool:
        """Check if user can create leave requests."""
        return (
            self.user.has_perm('leave.add_leaverequest') or
            self.user.is_staff or
            self.user.is_superuser
        )
    
    def can_view_payroll(self) -> bool:
        """Check if user can view payroll."""
        return (
            self.user.has_perm('payroll.view_payslip') or
            self.user.is_staff or
            self.user.is_superuser
        )
    
    def can_export_data(self) -> bool:
        """Check if user can export data."""
        return (
            self.user.is_staff or
            self.user.is_superuser or
            self.user.has_perm('employee.view_employee')
        )
    
    def can_import_data(self) -> bool:
        """Check if user can import data."""
        return (
            self.user.is_staff or
            self.user.is_superuser or
            self.user.has_perm('employee.add_employee')
        )
