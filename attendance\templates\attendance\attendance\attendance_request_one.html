{% load i18n %} {% load basefilters %}
<div class="oh-modal__dialog-header">
    <h2 class="oh-modal__dialog-title m-0" id="objectDetailsModalLabel">
        {% trans "Details" %}
    </h2>
    <button class="oh-modal__close" aria-label="Close">
        <ion-icon name="close-outline"></ion-icon>
    </button>
</div>

<div class="oh-modal__dialog-body oh-modal__dialog-relative">
    <div class="oh-modal__dialog oh-modal__dialog--navigation m-0 p-0">
        <button
            hx-get="{% url 'user-request-one-view' previous_instance %}?instances_ids={{instance_ids_json}}&validate={{request.GET.validate}}&ot={{request.GET.ot}}&all_attendance={{request.GET.all_attendance}}&my_attendance={{request.GET.my_attendance}}&dashboard={{dashboard}}"
            hx-target="#objectDetailsModalW25Target" class="oh-modal__diaglog-nav oh-modal__nav-prev"
            data-action="previous">
            <ion-icon name="chevron-back-outline" class="md hydrated" role="img"
                aria-label="chevron back outline"></ion-icon>
        </button>

        <button
            hx-get="{% url 'user-request-one-view' next_instance %}?instances_ids={{instance_ids_json}}&validate={{request.GET.validate}}&ot={{request.GET.ot}}&all_attendance={{request.GET.all_attendance}}&my_attendance={{request.GET.my_attendance}}&dashboard={{dashboard}}"
            hx-target="#objectDetailsModalW25Target" class="oh-modal__diaglog-nav oh-modal__nav-next"
            data-action="next">
            <ion-icon name="chevron-forward-outline" class="md hydrated" role="img"
                aria-label="chevron forward outline"></ion-icon>
        </button>
    </div>

    <a class="oh-timeoff-modal__profile-content" style="text-decoration:none;"
        href="{% url 'employee-view-individual' attendance_request.employee_id.id %}">
        <div class="oh-profile mb-2">
            <div class="oh-profile__avatar">
                <img src="{{attendance_request.employee_id.get_avatar}}" class="oh-profile__image me-2"
                    alt="Mary Magdalene" />
            </div>
            <div class="oh-timeoff-modal__profile-info">
                <span class="oh-timeoff-modal__user fw-bold">{{attendance_request.employee_id}}</span>
                <span class="oh-timeoff-modal__user m-0" style="font-size: 18px; color: #4d4a4a">
                    {{attendance_request.employee_id.employee_work_info.department_id}} /
                    {{attendance_request.employee_id.employee_work_info.job_position_id}}
                </span>

            </div>
        </div>
    </a>

    <div class="oh-modal__dialog-header pt-0">
        <div class="oh-timeoff-modal__stats-container mt-3 mb-3">
            <div class="oh-timeoff-modal__stat">
                <span class="oh-timeoff-modal__stat-title">{% trans "Date" %}</span>
                <span
                    class="oh-timeoff-modal__stat-count dateformat_changer">{{attendance_request.attendance_date}}</span>
            </div>
            <div class="oh-timeoff-modal__stat" style="margin-left: 20px;">
                <span class="oh-timeoff-modal__stat-title">{% trans "Day" %}</span>
                <span class="oh-timeoff-modal__stat-count">{{attendance_request.attendance_day}}</span>
            </div>
        </div>

        <div class="oh-timeoff-modal__stats-container mt-3 mb-3">
            <div class="oh-timeoff-modal__stat">
                <span class="oh-timeoff-modal__stat-title">{% trans "Check In" %}</span>
                <span
                    class="oh-timeoff-modal__stat-count timeformat_changer">{{attendance_request.attendance_clock_in}}</span>
            </div>
            <div class="oh-timeoff-modal__stat" style="margin-left: 20px;">
                <span class="oh-timeoff-modal__stat-title">{% trans "Check In Date" %}</span>
                <span
                    class="oh-timeoff-modal__stat-count dateformat_changer">{{attendance_request.attendance_clock_in_date}}</span>
            </div>
        </div>

        <div class="oh-timeoff-modal__stats-container mt-3 mb-3">
            <div class="oh-timeoff-modal__stat">
                <span class="oh-timeoff-modal__stat-title">{% trans "Check Out" %}</span>
                <span
                    class="oh-timeoff-modal__stat-count timeformat_changer">{{attendance_request.attendance_clock_out}}</span>
            </div>
            <div class="oh-timeoff-modal__stat" style="margin-left: 20px;">
                <span class="oh-timeoff-modal__stat-title">{% trans "Check Out Date" %}</span>
                <span
                    class="oh-timeoff-modal__stat-count dateformat_changer">{{attendance_request.attendance_clock_out_date}}</span>
            </div>
        </div>

        <div class="oh-timeoff-modal__stats-container mt-3 mb-3">
            <div class="oh-timeoff-modal__stat">
                <span class="oh-timeoff-modal__stat-title">{% trans "Shift" %}</span>
                <span class="oh-timeoff-modal__stat-count">{{attendance_request.shift_id}}</span>
            </div>
            <div class="oh-timeoff-modal__stat" style="margin-left: 20px;">
                <span class="oh-timeoff-modal__stat-title">{% trans "Work Type" %}</span>
                <span class="oh-timeoff-modal__stat-count">{{attendance_request.work_type_id}}</span>
            </div>
        </div>

        <div class="oh-timeoff-modal__stats-container mt-3 mb-3">
            <div class="oh-timeoff-modal__stat">
                <span class="oh-timeoff-modal__stat-title">{% trans "Min Hour" %}</span>
                <span class="oh-timeoff-modal__stat-count">{{attendance_request.minimum_hour}}</span>
            </div>
            <div class="oh-timeoff-modal__stat" style="margin-left: 20px;">
                <span class="oh-timeoff-modal__stat-title">{% trans "At Work" %}</span>
                <span class="oh-timeoff-modal__stat-count">{{at_work}}</span>
            </div>
        </div>
        <div class="oh-timeoff-modal__stats-container mt-3 mb-3">
            <div class="oh-timeoff-modal__stat">
                <span class="oh-timeoff-modal__stat-title">{% trans "Overtime" %}</span>
                <span class="oh-timeoff-modal__stat-count">{{over_time}}</span>
            </div>
            <div class="oh-timeoff-modal__stat" style="margin-left: 20px;">
                <span class="oh-timeoff-modal__stat-title">{% trans "Batch" %}</span>
                <span class="oh-timeoff-modal__stat-count">{{attendance_request.batch_attendance_id}}</span>
            </div>
        </div>
        <div class="oh-timeoff-modal__stats-container mt-3 mb-3">
            <div class="oh-timeoff-modal__stat" style="cursor: pointer;">
                <span class="oh-timeoff-modal__stat-title">{% trans "Activities" %}</span>
                <span data-target="#objectCreateModal" data-toggle="oh-modal-toggle" hx-get="{% url 'get-attendance-activities' attendance_request.id  %}"
                    hx-target="#objectCreateModalTarget" class="oh-timeoff-modal__stat-count" style="display: flex;" title="{% trans 'View Activities' %}">
                    <p class="m-0">{{attendance_request.activities.count}} {% trans "Activity" %}</p>
                    <ion-icon name="eye-outline" class="md hydrated ml-1 mt-1" role="img" aria-label="chevron back outline"></ion-icon>
                </span>
            </div>
        </div>
        {% if request.GET.my_attendance %}

        {% elif request.GET.all_attendance %}
            <a data-toggle="oh-modal-toggle" data-target="#objectUpdateModal"
                hx-get="{% url 'attendance-change' attendance_request.id %}?all_attendance=true"
                hx-target="#objectUpdateModalTarget" class="oh-btn oh-btn--info w-100" title="{% trans 'Edit' %}">
                <ion-icon name="lock-open-outline" role="img" class="md hydrated" aria-label="create outline">
                </ion-icon>
            </a>
        {% elif request.GET.ot %}
        <div class="oh-modal__button-container text-center">
            {% if perms.attendance.change_attendance and perms.attendance.delete_attendance or request.user|is_reportingmanager%}
                <div class="oh-btn-group">
                    <a class="oh-btn oh-btn--info"
                        hx-get="{% url 'attendance-update' attendance_request.id %}?dashboard={{dashboard}}"
                        hx-target="{% if dashboard == 'true' %}#editModalForm {% else %}#updateAttendanceModalBody{% endif %}"
                        hx-swap='innerHTML' data-toggle='oh-modal-toggle'
                        data-target="{% if dashboard == 'true' %}#editModal {% else %}#updateAttendanceModal {% endif %}"
                        style="width: 50%;" title='{% trans "Edit" %}'>
                        <ion-icon name="create-outline" role="img" class="md hydrated" aria-label="create outline">
                        </ion-icon>
                    </a>
                    {% if attendance_request.attendance_overtime_approve %}
                        <a href="#" class="oh-btn oh-btn--success disabled w-50" title='{% trans "Approve" %}'>
                            <ion-icon name="ban-outline"></ion-icon>
                        </a>
                    {% elif minot <= attendance_request.overtime_second %}
                        <a href="{% url 'approve-overtime' attendance_request.id %}" class="oh-btn oh-btn--success w-50"
                            title='{% trans "Approve" %}'>
                        <ion-icon name="checkmark-done-outline"></ion-icon>
                        </a>
                        {% elif attendance_request.overtime_second >= 60 %}
                            <a type="submit" href="#" title="{% trans 'Approve' %}" class="oh-btn oh-btn--warning w-50"
                                onclick="event.stopPropagation();
                                    Swal.fire({
                                            title: '{% trans 'Are you sure?' %}',
                                            text: '{% trans 'This does not satisfy the minimum OT requirement!' %}',
                                            icon: 'warning',
                                            showCancelButton: true,
                                            confirmButtonColor: '#3085d6',
                                            cancelButtonColor: '#d33',
                                            confirmButtonText: 'Approve',
                                            cancelButtonText: 'Cancel'
                                        }).then((result) => {
                                            if (result.isConfirmed) {
                                            Swal.fire(
                                                '{% trans 'Approved!' %}',
                                                '{% trans 'Your action has been approved.' %}',
                                                'success'
                                            );
                                            $('[data-ot-approve-id={{attendance_request.id}}]').click();
                                            }
                                        });                                        ">
                                <ion-icon class="me-1" name="checkmark-outline"></ion-icon>
                            </a>
                            <button type="submit" data-ot-approve-id={{attendance_request.id}} hidden href=""
                                onclick="window.location.href='{% url 'approve-overtime' attendance_request.id %}'"
                                title="{% trans 'Approve Overtime' %}" class="oh-btn oh-btn--success w-100">
                                <ion-icon class="me-1" name="checkmark-outline"></ion-icon>
                            </button>
                        {% endif %}
                        <form action="{% url 'attendance-delete' attendance_request.id  %}"
                            onsubmit="return confirm('{% trans " Are you sure want to delete this attendance?" %}')"
                            hx-target="#tab_contents" method='post' style="width: 50%;">
                            {% csrf_token %}
                            <button type='submit' class="oh-btn oh-btn--danger w-100" data-action="delete" title='{% trans "Delete" %}'>
                                <ion-icon name="trash-outline" role="img" class="md hydrated" aria-label="create outline"></ion-icon>
                            </button>
                        </form>
                </div>
            {% endif %}
        </div>
        {% elif request.GET.validate %}
            <div class="oh-modal__button-container text-center">
                {% if perms.attendance.change_attendance and perms.attendance.delete_attendance or request.user|is_reportingmanager %}
                    <div class="oh-btn-group">
                        <a class="oh-btn oh-btn--info" hx-get="{% url 'attendance-update' attendance_request.id %}"
                            hx-target="{% if dashboard == 'true' %}#editModalForm {% else %}#updateAttendanceModalBody{% endif %}"
                            hx-swap='innerHTML' data-toggle='oh-modal-toggle'
                            data-target="{% if dashboard == 'true' %}#editModal {% else %}#updateAttendanceModal {% endif %}"
                            style="width: 50%;" title='{% trans "Edit" %}'>
                            <ion-icon name="create-outline" role="img" class="md hydrated" aria-label="create outline">
                            </ion-icon>
                        </a>
                        <a href='{% url "validate-this-attendance" attendance_request.id %}' hx-target='#updateAttendanceBody'
                            class="oh-btn oh-btn--success w-50" title='{% trans "Validate" %}'
                            data-req="/attendance/request-attendance-view/?id={{attendance_request.id}}"
                            onclick="{% if attendance_request.is_validate_request %}  event.preventDefault(); showSweetAlert($(this).data('req')); {% endif %}">
                            <ion-icon name="checkmark-done-outline"></ion-icon>
                        </a>
                        <form action="{% url 'attendance-delete' attendance_request.id  %}" method='post' style="width: 50%;"
                            onsubmit="return confirm('{% trans " Are you sure want to delete this attendance?" %}')"
                            >
                            {% csrf_token %}
                            <button type='submit' class="oh-btn oh-btn--danger w-100" data-action="delete" title='{% trans "Delete" %}'>
                                <ion-icon name="trash-outline" role="img" class="md hydrated" aria-label="create outline"></ion-icon>
                            </button>
                        </form>
                    </div>
                {% endif %}
            </div>
        {% else %}
            <div class="oh-modal__button-container text-center">
                {% if perms.attendance.change_attendance and perms.attendance.delete_attendance or request.user|is_reportingmanager%}
                    <div class="oh-btn-group">
                        <a class="oh-btn oh-btn--info" hx-get="{% url 'attendance-update' attendance_request.id %}"
                            hx-target='#updateAttendanceModalBody' hx-swap='innerHTML' data-toggle='oh-modal-toggle'
                            data-target='#updateAttendanceModal' style="width: 50%;" title='{% trans "Edit" %}'>
                            <ion-icon name="create-outline" role="img" class="md hydrated" aria-label="create outline">
                            </ion-icon>
                        </a>
                        <form action="{% url 'attendance-delete' attendance_request.id  %}" onsubmit="return confirm('{% trans "
                            Are you sure want to delete this attendance?" %}')" hx-target="#tab_contents" method='post'
                            style="width: 50%;">
                            {% csrf_token %}
                            <button type='submit' class="oh-btn oh-btn--danger w-100" data-action="delete" title='{% trans "Delete" %}'>
                                <ion-icon name="trash-outline" role="img" class="md hydrated" aria-label="create outline"></ion-icon>
                            </button>
                        </form>
                    </div>
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>
