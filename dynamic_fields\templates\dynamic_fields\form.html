{% load generic_template_filters %}
<div id="{{view_id}}">
  {% for field_tuple in dynamic_create_fields %}
  <div
    class="oh-modal"
    id="dynamicModal{{field_tuple.0}}"
    role="dialog"
    aria-labelledby="dynamicModal{{field_tuple.0}}"
    aria-hidden="true"
  >
    <div
      class="oh-modal__dialog"
      id="dynamicModal{{field_tuple.0}}Body"
    ></div>
  </div>
  </div>
  {% endfor %}
  <form id="{{view_id}}Form" hx-post="{{request.path}}?{{request.GET.urlencode}}" hx-encoding="multipart/form-data" hx-swap="outerHTML" {% if hx_confirm %} hx-confirm="{{hx_confirm}}" {% endif %}>{{form.structured}}</form>
  {% for field_tuple in dynamic_create_fields %}
  <div >
  <script class="dynamic_{{field_tuple.0}}_scripts">
    {{form.initial|get_item:field_tuple.0}}
    $("#{{view_id}}Form [name={{field_tuple.0}}]").val({{form.initial|get_item:field_tuple.0|safe}}).change()
  </script>

  <form
    hidden
    id="modalButton{{field_tuple.0}}Form"
    hx-get="/dynamic-path-{{field_tuple.0}}-{{request.session.session_key}}?dynamic_field={{field_tuple.0}}"
    hx-target="#dynamicModal{{field_tuple.0}}Body"
  >
    <input type="text" name="dynamic_initial" data-dynamic-field="{{field_tuple.0}}">
    <input type="text" name="view_id" value="{{view_id}}">
    {% for field in field_tuple.2 %}
    <input type="text" name="{{field}}">
    {% endfor %}
    <button
      type="submit"
      id="modalButton{{field_tuple.0}}"
      onclick="$('#dynamicModal{{field_tuple.0}}').addClass('oh-modal--show');"
    >
    {{field_tuple.0}}
    </button>
  </form>
  <form hidden id="reload-field{{field_tuple.0}}{{view_id}}"  hx-get="{% url "reload-field" %}?form_class_path={{form_class_path}}&dynamic_field={{field_tuple.0}}" hx-target="#dynamic_field_{{field_tuple.0}}">
    <input type="text" name="dynamic_initial" data-dynamic-field="{{field_tuple.0}}">
    <input type="text" name="view_id" value="{{view_id}}">
    <button class="reload-field" data-target="{{field_tuple.0}}">
      {{field_tuple.0}}
    </button>
  </form>
  <script class="dynamic_{{field_tuple.0}}_scripts">
    $("#{{view_id}}Form [name={{field_tuple.0}}]").change(function (e) {
      values = $(this).val();
      if (!values) {
        values = ""
      }
      if (values == "dynamic_create") {
        $("#modalButton{{field_tuple.0}}").click()
        $(this).val("")
      }else if (values.includes("dynamic_create")) {
        let index = values.indexOf("dynamic_create");
        values.splice(index, 1);
        $(this).val(values).change();
        $("#modalButton{{field_tuple.0}}").parent().find('input[name=dynamic_initial]').val(values)
        $("#reload-field{{field_tuple.0}}{{view_id}}").find('input[name=dynamic_initial]').val(values)
        $("#modalButton{{field_tuple.0}}").click()
      }else if(values) {
        $("#modalButton{{field_tuple.0}}").parent().find('input[name=dynamic_initial]').val(values)
        $("#reload-field{{field_tuple.0}}{{view_id}}").find('input[name=dynamic_initial]').val(values)
      }
    });
    $("#reload-field{{field_tuple.0}}{{view_id}}").submit(function (e) {
      e.preventDefault();
      $(this).find("[name=dynamic_initial]").val();
    });
  </script>
  </div>
  {% endfor %}
</div>
