"""
API Request Executor

This module executes API requests generated by the Natural Language API Converter
with comprehensive error handling, response processing, and result summarization.
"""

import json
import logging
import requests
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from django.conf import settings
from django.contrib.auth.models import User
import time

logger = logging.getLogger(__name__)


class APIRequestExecutor:
    """
    Advanced API request executor with 200% functionality.
    Handles request execution, response processing, and result summarization.
    """
    
    def __init__(self, user: Optional[User] = None):
        self.user = user
        self.base_url = getattr(settings, 'BASE_URL', 'http://localhost:8000')
        self.session = requests.Session()
        self.request_history = []
        
        # Configure session with default headers
        self._configure_session()
    
    def execute_request(self, api_request: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute an API request with comprehensive error handling.
        
        Args:
            api_request: Structured API request dictionary
            
        Returns:
            Comprehensive response with data, metadata, and summary
        """
        
        start_time = time.time()
        
        try:
            # Validate request
            validation_result = self._validate_request(api_request)
            if not validation_result['valid']:
                return self._build_error_response(
                    'Request validation failed',
                    details=validation_result['errors'],
                    request=api_request
                )
            
            # Build request parameters
            request_params = self._build_request_params(api_request)
            
            # Execute the request
            response = self._execute_http_request(request_params)
            
            # Process response
            processed_response = self._process_response(response, api_request)
            
            # Add execution metadata
            execution_time = time.time() - start_time
            processed_response['execution_metadata'] = {
                'execution_time': execution_time,
                'timestamp': datetime.now().isoformat(),
                'user': self.user.username if self.user else 'anonymous',
                'request_id': self._generate_request_id()
            }
            
            # Store in history
            self._store_request_history(api_request, processed_response)
            
            return processed_response
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"API request execution failed: {str(e)}")
            
            return self._build_error_response(
                f"Request execution failed: {str(e)}",
                exception_type=type(e).__name__,
                execution_time=execution_time,
                request=api_request
            )
    
    def execute_batch_requests(self, api_requests: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Execute multiple API requests in batch.
        
        Args:
            api_requests: List of structured API request dictionaries
            
        Returns:
            List of response dictionaries
        """
        
        results = []
        
        for i, request in enumerate(api_requests):
            try:
                result = self.execute_request(request)
                result['batch_index'] = i
                results.append(result)
            except Exception as e:
                results.append(self._build_error_response(
                    f"Batch request {i} failed: {str(e)}",
                    batch_index=i,
                    request=request
                ))
        
        return results
    
    def _configure_session(self):
        """Configure the requests session with default settings."""
        
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': 'HRMS-AI-Assistant/1.0'
        })
        
        # Add authentication if user is provided
        if self.user:
            # This would typically add JWT token or session authentication
            # For now, we'll add a custom header
            self.session.headers.update({
                'X-User-ID': str(self.user.id),
                'X-Username': self.user.username
            })
    
    def _validate_request(self, api_request: Dict[str, Any]) -> Dict[str, Any]:
        """Validate the API request structure and parameters."""
        
        errors = []
        
        # Check required fields
        required_fields = ['url', 'method']
        for field in required_fields:
            if field not in api_request:
                errors.append(f"Missing required field: {field}")
        
        # Validate URL
        if 'url' in api_request:
            url = api_request['url']
            if not url.startswith('/'):
                errors.append("URL must start with '/'")
        
        # Validate HTTP method
        if 'method' in api_request:
            method = api_request['method'].upper()
            if method not in ['GET', 'POST', 'PUT', 'PATCH', 'DELETE']:
                errors.append(f"Invalid HTTP method: {method}")
        
        # Validate confidence threshold
        confidence = api_request.get('confidence', 0)
        if confidence < 0.7:
            errors.append(f"Low confidence score: {confidence:.2f} < 0.7")
        
        return {
            'valid': len(errors) == 0,
            'errors': errors
        }
    
    def _build_request_params(self, api_request: Dict[str, Any]) -> Dict[str, Any]:
        """Build request parameters from the API request."""
        
        # Build full URL
        full_url = f"{self.base_url}{api_request['url']}"
        
        # Build query parameters
        params = {}
        
        # Add filters as query parameters
        if api_request.get('filters'):
            params.update(api_request['filters'])
        
        # Add search parameter
        if api_request.get('search'):
            params['search'] = api_request['search']
        
        # Add sorting parameters
        if api_request.get('sort_by'):
            order_prefix = '-' if api_request.get('order') == 'desc' else ''
            params['ordering'] = f"{order_prefix}{api_request['sort_by']}"
        
        # Add pagination parameters
        if api_request.get('page'):
            params['page'] = api_request['page']
        if api_request.get('page_size'):
            params['page_size'] = api_request['page_size']
        
        return {
            'url': full_url,
            'method': api_request['method'].upper(),
            'params': params,
            'json': api_request.get('body', {}),
            'timeout': api_request.get('timeout', 30)
        }
    
    def _execute_http_request(self, request_params: Dict[str, Any]) -> requests.Response:
        """Execute the HTTP request."""
        
        method = request_params['method']
        url = request_params['url']
        params = request_params['params']
        json_data = request_params['json']
        timeout = request_params['timeout']
        
        if method == 'GET':
            return self.session.get(url, params=params, timeout=timeout)
        elif method == 'POST':
            return self.session.post(url, json=json_data, params=params, timeout=timeout)
        elif method == 'PUT':
            return self.session.put(url, json=json_data, params=params, timeout=timeout)
        elif method == 'PATCH':
            return self.session.patch(url, json=json_data, params=params, timeout=timeout)
        elif method == 'DELETE':
            return self.session.delete(url, params=params, timeout=timeout)
        else:
            raise ValueError(f"Unsupported HTTP method: {method}")
    
    def _process_response(self, response: requests.Response, api_request: Dict[str, Any]) -> Dict[str, Any]:
        """Process the HTTP response and build result dictionary."""
        
        result = {
            'success': response.status_code < 400,
            'status_code': response.status_code,
            'headers': dict(response.headers),
            'url': response.url
        }
        
        # Process response body
        try:
            if response.headers.get('content-type', '').startswith('application/json'):
                data = response.json()
                result['data'] = data
                result['content_type'] = 'json'
                
                # Generate summary for JSON data
                result['summary'] = self._generate_data_summary(data, api_request)
                
            else:
                result['data'] = response.text
                result['content_type'] = 'text'
                result['summary'] = f"Received {len(response.text)} characters of text data"
                
        except json.JSONDecodeError:
            result['data'] = response.text
            result['content_type'] = 'text'
            result['summary'] = "Received non-JSON response"
        
        # Add error details for failed requests
        if not result['success']:
            result['error'] = {
                'message': f"HTTP {response.status_code}: {response.reason}",
                'status_code': response.status_code,
                'response_text': response.text[:500]  # First 500 chars
            }
        
        return result
    
    def _generate_data_summary(self, data: Any, api_request: Dict[str, Any]) -> str:
        """Generate a human-readable summary of the response data."""
        
        method = api_request.get('method', 'GET')
        
        if method == 'GET':
            return self._generate_get_summary(data, api_request)
        elif method == 'POST':
            return "Successfully created new record"
        elif method == 'PUT':
            return "Successfully updated record"
        elif method == 'DELETE':
            return "Successfully deleted record"
        else:
            return "Request completed successfully"
    
    def _generate_get_summary(self, data: Any, api_request: Dict[str, Any]) -> str:
        """Generate summary for GET request responses."""
        
        if isinstance(data, dict):
            # Check for paginated response
            if 'results' in data:
                count = len(data['results'])
                total = data.get('count', count)
                
                if count == 0:
                    return "No records found matching your criteria"
                elif count == total:
                    return f"Found {count} record{'s' if count != 1 else ''}"
                else:
                    return f"Showing {count} of {total} records"
            
            # Check for single record
            elif 'id' in data or 'pk' in data:
                return "Retrieved record details"
            
            # Check for count/summary data
            elif 'count' in data:
                return f"Found {data['count']} records"
            
            else:
                return "Retrieved data successfully"
        
        elif isinstance(data, list):
            count = len(data)
            if count == 0:
                return "No records found"
            else:
                return f"Found {count} record{'s' if count != 1 else ''}"
        
        else:
            return "Retrieved data successfully"
    
    def _build_error_response(self, message: str, **kwargs) -> Dict[str, Any]:
        """Build a standardized error response."""
        
        return {
            'success': False,
            'error': {
                'message': message,
                'timestamp': datetime.now().isoformat(),
                **kwargs
            }
        }
    
    def _generate_request_id(self) -> str:
        """Generate a unique request ID."""
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        user_id = self.user.id if self.user else 'anon'
        return f"req_{timestamp}_{user_id}_{len(self.request_history)}"
    
    def _store_request_history(self, request: Dict[str, Any], response: Dict[str, Any]):
        """Store request and response in history."""
        
        history_entry = {
            'timestamp': datetime.now().isoformat(),
            'request': request,
            'response': {
                'success': response.get('success'),
                'status_code': response.get('status_code'),
                'summary': response.get('summary'),
                'execution_time': response.get('execution_metadata', {}).get('execution_time')
            }
        }
        
        self.request_history.append(history_entry)
        
        # Keep only last 100 requests
        if len(self.request_history) > 100:
            self.request_history = self.request_history[-100:]
    
    def get_request_history(self) -> List[Dict[str, Any]]:
        """Get the request history."""
        return self.request_history.copy()
    
    def clear_request_history(self):
        """Clear the request history."""
        self.request_history.clear()
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics from request history."""
        
        if not self.request_history:
            return {'message': 'No requests in history'}
        
        execution_times = [
            entry['response'].get('execution_time', 0)
            for entry in self.request_history
            if entry['response'].get('execution_time')
        ]
        
        success_count = sum(
            1 for entry in self.request_history
            if entry['response'].get('success')
        )
        
        return {
            'total_requests': len(self.request_history),
            'successful_requests': success_count,
            'success_rate': (success_count / len(self.request_history)) * 100,
            'average_execution_time': sum(execution_times) / len(execution_times) if execution_times else 0,
            'min_execution_time': min(execution_times) if execution_times else 0,
            'max_execution_time': max(execution_times) if execution_times else 0
        }
