{% load basefilters %}
{% load i18n %} {% load eaglorafilters %} {% load static %}
{% if messages %}
    <div class="oh-wrapper">
        {% for message in messages %}
            <div class="oh-alert-container">
                <div class="oh-alert oh-alert--animated {{message.tags}}">
                    {{ message }}
                </div>
            </div>
        {% endfor %}
    </div>
    <span class="oh-span__class" hx-get="{{close_hx_url}}" hx-target="{{close_hx_target}}" hx-trigger="load"></span>
{% endif %}
{% if work_type_request %}
    <div class="oh-modal__dialog-header">
        <span class="oh-modal__dialog-title" id="workTypeRequestSingleViewModalLabel">
            {% trans "Details" %}
        </span>
        <button class="oh-modal__close" aria-label="Close">
            <ion-icon name="close-outline"></ion-icon>
        </button>
    </div>
    <div class="oh-modal__dialog-body oh-modal__dialog-relative">
        {% if request.GET.instances_ids %}
            <div class="oh-modal__dialog oh-modal__dialog--navigation m-0 p-0">
                <button
                    hx-get="{% url 'work-type-request-single-view' previous %}?instances_ids={{requests_ids}}&dashboard={{dashboard}}"
                    hx-target="#objectDetailsModalTarget" class="oh-modal__diaglog-nav oh-modal__nav-prev"
                    data-action="previous">
                    <ion-icon name="chevron-back-outline" class="md hydrated" role="img"
                        aria-label="chevron back outline"></ion-icon>
                </button>

                <button
                    hx-get="{% url 'work-type-request-single-view' next %}?instances_ids={{requests_ids}}&dashboard={{dashboard}}"
                    hx-target="#objectDetailsModalTarget" class="oh-modal__diaglog-nav oh-modal__nav-next" data-action="next">
                    <ion-icon name="chevron-forward-outline" class="md hydrated" role="img"
                        aria-label="chevron forward outline"></ion-icon>
                </button>
            </div>
        {% endif %}
            <a class="oh-timeoff-modal__profile-content" style="text-decoration:none;"
                href="{% url 'employee-view-individual' work_type_request.employee_id.id %}">
                <div class="oh-profile mb-2">
                    <div class="oh-profile__avatar">
                        <img src="{{work_type_request.employee_id.get_avatar}}" class="oh-profile__image me-2"
                            alt="Mary Magdalene" />
                    </div>
                    <div class="oh-timeoff-modal__profile-info">
                        <span class="oh-timeoff-modal__user fw-bold">{{work_type_request.employee_id}}</span>
                        <span class="oh-timeoff-modal__user m-0" style="font-size: 18px; color: #4d4a4a">
                            {{work_type_request.employee_id.employee_work_info.department_id}} /
                            {{work_type_request.employee_id.employee_work_info.job_position_id}}</span>
                    </div>
                </div>
            </a>

        <div class="oh-modal__dialog-header" style="padding-top: 5px">
            <div class="oh-timeoff-modal__stats-container mb-3">
                <div class="oh-timeoff-modal__stat">
                    <span class="oh-timeoff-modal__stat-title">{% trans "Requested work type" %}</span>
                    <span class="oh-timeoff-modal__stat-count">{{work_type_request.work_type_id}}</span>
                </div>
                <div class="oh-timeoff-modal__stat" style="margin-left: 20px;">
                    <span class="oh-timeoff-modal__stat-title">{% trans "Previous work type" %}</span>
                    <span class="oh-timeoff-modal__stat-count">{{work_type_request.previous_work_type_id}}</span>
                </div>
            </div>
            <div class="oh-timeoff-modal__stats-container mb-3">
                <div class="oh-timeoff-modal__stat">
                    <span class="oh-timeoff-modal__stat-title">{% trans "Requested date" %}</span>
                    <span
                        class="oh-timeoff-modal__stat-count dateformat_changer">{{work_type_request.requested_date}}</span>
                </div>
                <div class="oh-timeoff-modal__stat" style="margin-left: 20px;">
                    <span class="oh-timeoff-modal__stat-title">{% trans "Requested till" %}</span>
                    <span
                        class="oh-timeoff-modal__stat-count dateformat_changer">{{work_type_request.requested_till}}</span>
                </div>
            </div>
            <div class="oh-timeoff-modal__stats-container mb-3">
                <div class="oh-timeoff-modal__stat">
                    <span class="oh-timeoff-modal__stat-title">{% trans "Description" %}</span>
                    <span class="oh-timeoff-modal__stat-count">{{work_type_request.description}}</span>
                </div>
                <div class="oh-timeoff-modal__stat" style="margin-left: 20px;">
                    <span class="oh-timeoff-modal__stat-title">{% trans "Is permenent work type" %}</span>
                    <span class="oh-timeoff-modal__stat-count">
                        {{work_type_request.is_permanent_work_type|yes_no}}
                    </span>
                </div>
            </div>
            {% with can_change=perms.base.change_worktyperequest can_delete=perms.base.delete_worktyperequest %}
                {% if can_change or request.user|is_reportingmanager or can_delete %}
                    <div class="oh-modal__button-container text-center">
                        <div class="oh-btn-group">
                            {% if not work_type_request.approved and not work_type_request.canceled and can_change and dashboard == "true" %}
                                <a hx-confirm="{% trans 'Do you want to approve this request?' %}"
                                    hx-post="/work-type-request-approve/{{work_type_request.id}}/?instances_ids={{requests_ids}}&dashboard={{dashboard}}"
                                    hx-target="#objectDetailsModalTarget"
                                    class="oh-btn oh-btn--success w-50">
                                    {% trans "Approve" %}
                                </a>
                                <a href="/work-type-request-cancel/{{work_type_request.id}}/"
                                    onclick="return confirm('{% trans 'Do you want to cancel this request?' %}')"
                                    class="oh-btn oh-btn--danger w-50" title="{% trans 'Reject' %}">
                                    {% trans "Reject" %}
                                </a>
                            {% else %}
                                {% if dashboard != "true" %}
                                    {% if can_change or can_delete %}
                                        {% if not work_type_request.approved and not work_type_request.canceled %}
                                            <a hx-get="{% url 'work-type-request-update' work_type_request.id %}"
                                                hx-target='#objectUpdateModalTarget'
                                                data-toggle="oh-modal-toggle"
                                                data-target='#objectUpdateModal'
                                                class="oh-btn oh-btn--info w-50" title="{% trans 'Edit' %}">
                                                <ion-icon name="create-outline"></ion-icon>
                                            </a>
                                        {% else %}
                                            <button class="oh-btn oh-btn--info w-50" disabled>
                                                <ion-icon name="create-outline"></ion-icon>
                                            </button>
                                        {% endif %}
                                        {% if not work_type_request.approved and not work_type_request.canceled %}
                                            <form hx-confirm="{% trans 'Are you sure you want to delete this work type request?' %}"
                                                hx-post="{% url 'work-type-request-delete' work_type_request.id  %}?instances_ids={{requests_ids}}"
                                                hx-target="#objectDetailsModalTarget" style="width: 50%">
                                                {% csrf_token %}
                                                <button type="submit" class="oh-btn oh-btn--secondary w-100" title="{% trans 'Remove' %}"
                                                    data-action="delete">
                                                    <ion-icon name="trash-outline"></ion-icon>
                                                </button>
                                            </form>
                                        {% else %}
                                            <button class="oh-btn oh-btn--secondary w-50" disabled>
                                                <ion-icon name="trash-outline"></ion-icon>
                                            </button>
                                        {% endif %}
                                    {% endif %}
                                {% endif %}
                            {% endif %}
                        </div>
                    </div>
                {% endif %}
            {% endwith %}
        </div>
    </div>
{% else %}
    <div class="oh-modal__dialog-header">
        <span class="oh-modal__dialog-title" style="margin-left: 30px"></span>
        <button class="oh-modal__close" aria-label="Close">
            <ion-icon name="close-outline"></ion-icon>
        </button>
    </div>
    <div class="oh-modal__dialog-body oh-modal__dialog-relative">
        <img style="margin-left: 33%; width: 150px; height: 150px" src="{% static 'images/ui/employee_shift.png' %}"
            class="oh-404__image mb-2" alt="Page not found. 404." />
        <h5 class="oh-404__subtitle">
            {% trans "There are no work type requets to display." %}
        </h5>
    </div>
{% endif %}
