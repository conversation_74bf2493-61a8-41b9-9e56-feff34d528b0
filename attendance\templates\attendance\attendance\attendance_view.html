{% extends 'index.html' %}
{% block content %}
{% load i18n %}
{% load static %}
{% load basefilters %}
{% include 'attendance/attendance/attendance_nav.html' %}
<div class="oh-wrapper" id="attendance-container">
    <div class="oh-tabs">
        <ul class="oh-tabs__tablist">
            <li class="oh-tabs__tab" data-target="#tab_1">
                {% trans "Attendance To Validate " %}
                <div class="oh-dropdown float-end" x-data="{open: false}">
                    <button class="oh-btn oh-stop-prop oh-btn--transparent oh-accordion-meta__btn" @click="open = !open"
                        @click.outside="open = false" title='{% trans "Actions" %}'>
                        <ion-icon name="ellipsis-vertical"></ion-icon>
                    </button>
                    <div class="oh-dropdown__menu oh-dropdown__menu--right" x-show="open" style="display: none">
                        <ul class="oh-dropdown__items">
                            {% if perms.attendance.change_attendance or request.user|is_reportingmanager %}
                                <li class="oh-dropdown__item">
                                    <a href="#" id="validateAttendances" class="oh-dropdown__link">{% trans "Validate" %}</a>
                                </li>
                            {% endif %}
                        </ul>
                    </div>
                </div>
            </li>
            <li class="oh-tabs__tab oh-tabs__tab" data-target="#tab_3">
                {% trans "OT Attendances" %}
                <div class="oh-dropdown float-end" x-data="{open: false}">
                    <button class="oh-btn oh-stop-prop oh-btn--transparent oh-accordion-meta__btn" @click="open = !open"
                        @click.outside="open = false" title='{% trans "Actions" %}'>
                        <ion-icon name="ellipsis-vertical"></ion-icon>
                    </button>
                    <div class="oh-dropdown__menu oh-dropdown__menu--right" x-show="open" style="display: none">
                        <ul class="oh-dropdown__items">
                            {% if perms.attendance.change_attendance or request.user|is_reportingmanager %}
                                <li class="oh-dropdown__item">
                                    <a href="#" id="approveOt" class="oh-dropdown__link">{% trans "Approve OT" %}</a>
                                </li>
                            {% endif %}
                        </ul>
                    </div>
                </div>
            </li>
            <li class="oh-tabs__tab" data-target="#tab_2">
                {% trans "Validated Attendances" %}
            </li>
        </ul>
        <div class="oh-tabs__contents" id="tab_contents">
            {% include 'attendance/attendance/tab_content.html' %}
        </div>
    </div>
</div>
<span
    id = "dynamicCreateBatchAttendanceSpan"
    hx-get="{% url "create-batch-attendance" %}"
    data-target = "#dynamicCreateModal"
    data-toggle="oh-modal-toggle"
    hx-target="#dynamicCreateModalTarget"
    hx-include = "#attendanceRequestForm,#attendanceUpdateForm,#attendanceCreateForm"
></span>
<button hidden hx-post="{% url "update-title" %}" hx-target="#objectDetailsModalTarget" id="updateTitleSpan"></button>

<script>
    function dateChange(selectElement) {
        var selectedDate = selectElement.val();
        let parentForm = selectElement.parents().closest("form");
        var shiftId = parentForm.find("[name=shift_id]").val();

        $.ajax({
            type: "post",
            url: "{% url 'update-date-details' %}",
            data: {
                csrfmiddlewaretoken: getCookie("csrftoken"),
                attendance_date: selectedDate,
                shift_id: shiftId,
            },
            success: function (response) {
                parentForm.find("[name=minimum_hour]").val(response.minimum_hour);
            },
        });
    }
    // Dynamic batch attendance create
    function dynamicBatchAttendance(element){
        batch = element.val()
        if (batch === 'dynamic_create'){
        var parentForm = element.parents().closest("form");
        previous_url = parentForm.data('url');
        $('#dynamicCreateBatchAttendanceSpan').attr("hx-vals",`{"previous_url":"${previous_url}"}`)
        $('#dynamicCreateBatchAttendanceSpan').click();
        // unselect the choosen value
        element.val('').change();
        }
    }

    // Batch title change
    function batchTitleChange(element){
        console.log(element)
        title = $(element).val()
        batchId = $(element).data('id')
        $('#updateTitleSpan').attr("hx-vals",`{"batch_id":"${batchId}","title":"${title}"}`)
        $('#updateTitleSpan').click()
    }
</script>

{% endblock content %}
