{% extends "base.html" %}
{% load ai_assistant_tags %}
{% load static %}

{% block title %}AI Assistant Chat History{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title mb-0">
                        <i class="fas fa-robot"></i>
                        AI Assistant Chat History
                    </h4>
                    <div class="card-actions">
                        <button type="button" class="btn btn-outline-danger btn-sm" id="clear-history-btn">
                            <i class="fas fa-trash"></i>
                            Clear History
                        </button>
                    </div>
                </div>
                
                <div class="card-body">
                    {% if chat_history %}
                        <div class="chat-history-container">
                            {% for chat in chat_history %}
                                <div class="chat-entry">
                                    <div class="chat-header">
                                        <div class="chat-info">
                                            <span class="chat-intent">
                                                <i class="{{ chat.intent|ai_intent_icon }}"></i>
                                                {{ chat.intent|title }}
                                            </span>
                                            <span class="chat-confidence {{ chat.confidence|ai_confidence_color }}">
                                                {{ chat.confidence|floatformat:2 }}
                                            </span>
                                        </div>
                                        <div class="chat-meta">
                                            <span class="chat-time">{{ chat.created_at }}</span>
                                            <span class="processing-time">
                                                {{ chat.processing_time|floatformat:2 }}s
                                            </span>
                                        </div>
                                    </div>
                                    
                                    <div class="chat-content">
                                        <div class="user-message">
                                            <div class="message-label">
                                                <i class="fas fa-user"></i>
                                                You asked:
                                            </div>
                                            <div class="message-text">{{ chat.message }}</div>
                                        </div>
                                        
                                        <div class="assistant-response">
                                            <div class="message-label">
                                                <i class="fas fa-robot"></i>
                                                AI Assistant:
                                            </div>
                                            <div class="message-text">{{ chat.get_response_message }}</div>
                                            
                                            {% if chat.response.url %}
                                                <div class="response-action">
                                                    <i class="fas fa-external-link-alt"></i>
                                                    Action: <a href="{{ chat.response.url }}" target="_blank">{{ chat.response.url }}</a>
                                                </div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                        
                        <!-- Pagination -->
                        {% if page_obj.has_other_pages %}
                            <nav aria-label="Chat history pagination" class="mt-4">
                                <ul class="pagination justify-content-center">
                                    {% if page_obj.has_previous %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page=1">&laquo; First</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Previous</a>
                                        </li>
                                    {% endif %}
                                    
                                    <li class="page-item active">
                                        <span class="page-link">
                                            Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                                        </span>
                                    </li>
                                    
                                    {% if page_obj.has_next %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">Last &raquo;</a>
                                        </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        {% endif %}
                    {% else %}
                        <div class="empty-state">
                            <div class="empty-icon">
                                <i class="fas fa-comments"></i>
                            </div>
                            <h5>No Chat History</h5>
                            <p class="text-muted">You haven't had any conversations with the AI assistant yet.</p>
                            <a href="/" class="btn btn-primary">
                                <i class="fas fa-home"></i>
                                Go to Dashboard
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.chat-history-container {
    max-height: 70vh;
    overflow-y: auto;
}

.chat-entry {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    border-left: 4px solid #667eea;
}

.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
}

.chat-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.chat-intent {
    background: #667eea;
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
}

.chat-confidence {
    font-weight: 600;
    font-size: 14px;
}

.chat-meta {
    display: flex;
    align-items: center;
    gap: 15px;
    font-size: 12px;
    color: #6c757d;
}

.processing-time {
    background: #e9ecef;
    padding: 2px 8px;
    border-radius: 12px;
}

.chat-content {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.user-message, .assistant-response {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.message-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    font-size: 14px;
    color: #495057;
}

.message-text {
    background: white;
    padding: 12px 16px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    line-height: 1.5;
}

.assistant-response .message-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
}

.response-action {
    font-size: 12px;
    color: #6c757d;
    margin-top: 5px;
}

.response-action a {
    color: #667eea;
    text-decoration: none;
}

.response-action a:hover {
    text-decoration: underline;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
}

.empty-icon {
    font-size: 64px;
    color: #e9ecef;
    margin-bottom: 20px;
}

.empty-state h5 {
    color: #495057;
    margin-bottom: 10px;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const clearBtn = document.getElementById('clear-history-btn');
    
    if (clearBtn) {
        clearBtn.addEventListener('click', function() {
            if (confirm('Are you sure you want to clear all chat history? This action cannot be undone.')) {
                fetch('{% url "ai_assistant:clear-history" %}', {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Failed to clear chat history: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while clearing chat history.');
                });
            }
        });
    }
});
</script>
{% endblock %}
