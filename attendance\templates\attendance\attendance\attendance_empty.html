{% extends 'index.html' %} {% block content %} {% load static %} {% load i18n %}

{% load basefilters %}
{% if perms.attendance.add_attendance or request.user|is_reportingmanager %}
<div class="oh-modal" id="addAttendance" role="dialog" aria-labelledby="addAttendance" aria-hidden="true">
    <div class="oh-modal__dialog">
        <div class="oh-modal__dialog-header">
            <h2 class="oh-modal__dialog-title" id="addEmployeeModalLabel">
                {% trans "Add Attendances" %}
            </h2>
            <button class="oh-modal__close" aria-label="Close">
                <ion-icon name="close-outline"></ion-icon>
            </button>
        </div>
        <div class="oh-modal__dialog-body" id="addAttendanceModalBody"></div>
    </div>
</div>
{% endif %}

<div class="oh-modal" id="attendanceImport" role="dialog" aria-labelledby="attendanceImport" aria-hidden="true">
    <div class="oh-modal__dialog">
        <div class="oh-modal__dialog-header">
            <h2 class="oh-modal__dialog-title" id="attendanceImportLavel">
                {% trans "Import Attendances" %}
            </h2>
            <button class="oh-modal__close" aria-label="Close">
                <ion-icon name="close-outline"></ion-icon>
            </button>
            <div class="oh-modal__dialog-body p-0 pt-2" id="attendanceImportModalBody">
                <form hx-post="{% url 'attendance-info-import' %}" hx-encoding="multipart/form-data"
                    hx-target="#attendanceImport" id="attendanceImportForm">
                    {% csrf_token %}
                    <div class="oh-modal__dialog-body mr-5" id="uploading" style="display: none">
                        <div class="loader-container">
                            <div class="loader"></div>
                            <div class="loader-text">{% trans "Uploading..." %}</div>
                        </div>
                    </div>
                    <div id="uploadContainer">
                        <label class="oh-dropdown__import-label" for="uploadFile">
                            <ion-icon name="cloud-upload" class="oh-dropdown__import-form-icon"></ion-icon>
                            <span class="oh-dropdown__import-form-title">{% trans "Upload a File" %}</span>
                            <span class="oh-dropdown__import-form-text">{% trans "Drag and drop files here" %}</span>
                        </label>
                        <input type="file" name="attendance_import" id="" required />
                    </div>
                    <button type="submit" class="oh-btn oh-btn--small oh-btn--secondary w-100 mt-3">
                        {% trans "Upload" %}
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<section class="oh-wrapper oh-main__topbar">
    <div class="oh-main__titlebar oh-main__titlebar--left">
        <h1 class="oh-main__titlebar-title fw-bold">
            <a href="{% url 'attendance-view' %}" class="text-dark">
                {% trans "Attendances" %}
            </a>
        </h1>
    </div>
    <div class="oh-main__titlebar oh-main__titlebar--right">
        <div class="oh-main__titlebar-button-container">
            <div class="oh-dropdown ml-2" x-data="{open: false}">
                <button class="oh-btn oh-btn--dropdown" @click="open = !open" @click.outside="open = false">
                    {% trans "Actions" %}
                </button>
                <div class="oh-dropdown__menu oh-dropdown__menu--right" x-show="open" style="display: none">
                    <ul class="oh-dropdown__items">
                        {% if perms.attendance.change_attendance or request.user|is_reportingmanager %}
                        <li class="oh-dropdown__item">
                            <a href="#" class="oh-dropdown__link" id="attendance-info-import"
                                data-toggle="oh-modal-toggle" data-target="#attendanceImport">{% trans "Import" %}</a>
                        </li>
                        {% endif %}
                    </ul>
                </div>
            </div>
            {% if perms.attendance.add_attendance or request.user|is_reportingmanager%}
            <button class="oh-btn oh-btn--secondary ml-2" data-toggle="oh-modal-toggle" data-target="#addAttendance"
                hx-get="{% url 'attendance-create' %}" hx-target="#addAttendanceModalBody">
                <ion-icon name="add-sharp" class="mr-1"></ion-icon>{% trans "Create" %}
            </button>
            {% endif %}
        </div>
    </div>
</section>

<div class="oh-wrapper">
    <div class="oh-empty">
        <img src="{% static 'images/ui/search.svg' %}" class="oh-404__image" alt="Page not found. 404." />
        <h1 class="oh-empty__title">{% trans "No Records found." %}</h1>
        <p class="oh-empty__subtitle">{% trans "There are no attendance records to display." %}</p>
    </div>
</div>

<span id="dynamicCreateBatchAttendanceSpan" hx-get="{% url 'create-batch-attendance' %}"
    data-target="#dynamicCreateModal" data-toggle="oh-modal-toggle" hx-target="#dynamicCreateModalTarget"
    hx-include="#attendanceRequestForm,#attendanceUpdateForm,#attendanceCreateForm"></span>
<button hidden hx-post="{% url 'update-title' %}" hx-target="#objectDetailsModalTarget" id="updateTitleSpan"></button>

<script>
    function dateChange(selectElement) {
        var selectedDate = selectElement.val();
        let parentForm = selectElement.parents().closest("form");
        var shiftId = parentForm.find("[name=shift_id]").val();

        $.ajax({
            type: "post",
            url: "{% url 'update-date-details' %}",
            data: {
                csrfmiddlewaretoken: getCookie("csrftoken"),
                attendance_date: selectedDate,
                shift_id: shiftId,
            },
            success: function (response) {
                parentForm.find("[name=minimum_hour]").val(response.minimum_hour);
            },
        });
    }
    // Dynamic batch attendance create
    function dynamicBatchAttendance(element) {
        batch = element.val()
        if (batch === 'dynamic_create') {
            var parentForm = element.parents().closest("form");
            previous_url = parentForm.data('url');
            $('#dynamicCreateBatchAttendanceSpan').attr("hx-vals", `{"previous_url":"${previous_url}"}`)
            $('#dynamicCreateBatchAttendanceSpan').click();
            element.val('').change();
        }
    }

    // Batch title change
    function batchTitleChange(element) {
        console.log(element)
        title = $(element).val()
        batchId = $(element).data('id')
        $('#updateTitleSpan').attr("hx-vals", `{"batch_id":"${batchId}","title":"${title}"}`)
        $('#updateTitleSpan').click()
    }
</script>

{% endblock content %}
