{% load i18n %}
<div class="oh-sticky-table">
	<div class="oh-sticky-table__table oh-table--sortable">
		<div class="oh-sticky-table__thead">
			<div class="oh-sticky-table__tr">
				<div class="oh-sticky-table__th">{% trans "Title" %}</div>
				<div class="oh-sticky-table__th">{% trans "Color" %}</div>
				{% if perms.base.delete_tags or perms.base.delete_tags %}
					<div class="oh-sticky-table__th">{% trans "Actions" %}</div>
				{% endif %}
			</div>
		</div>
		<div class="oh-sticky-table__tbody">
			{% for tag in tags %}
				<div class="oh-sticky-table__tr" draggable="true" id="tagTr{{tag.id}}">
					<div class="oh-sticky-table__td">{{tag}}</div>
					<div class="oh-sticky-table__td">
						<span style=" height: 25px;
							width: 25px;
							background-color: {{tag.color}};
							border-radius: 50%;
							display: inline-block;"></span>
					</div>
					{% if perms.base.delete_tags or perms.base.delete_tags %}
						<div class="oh-sticky-table__td">
							<div class="oh-btn-group">
								{% if perms.base.change_tags %}
									<a hx-get="{% url 'tag-update' tag.id %}" hx-target="#tagEditForm" data-toggle="oh-modal-toggle"
										data-target="#tagEditModal" type="button" class="oh-btn oh-btn--light-bkg w-50"
										title="{% trans 'Edit' %}">
										<ion-icon name="create-outline"></ion-icon></a>
								{% endif %}
								{% if perms.base.delete_tags %}
									<form hx-confirm="{% trans 'Are you sure you want to delete this tag ?' %}"
										hx-post="{% url 'tag-delete' tag.id %}" hx-target="#tagTr{{tag.id}}"
										hx-on-htmx-after-request="reloadMessage(this);" hx-swap="outerHTML"
										class="w-50">
										{% csrf_token %}
										<button type="submit" class="oh-btn oh-btn--danger-outline oh-btn--light-bkg w-100"
											title="{% trans 'Delete' %}">
											<ion-icon name="trash-outline"></ion-icon>
										</button>
									</form>
								{% endif %}
							</div>
						</div>
					{% endif %}
				</div>
			{% endfor %}
		</div>
	</div>
</div>
