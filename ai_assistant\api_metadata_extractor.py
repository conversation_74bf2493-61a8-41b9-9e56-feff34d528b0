"""
API Metadata Extractor

This module automatically extracts API metadata from Django URL patterns
to enable dynamic schema awareness for the AI agent.
"""

import re
import inspect
from typing import Dict, Any, List, Optional
from django.urls import get_resolver, URLPattern, URLResolver
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.db.models import Model
from django.db import models


class APIMetadataExtractor:
    """
    Extracts comprehensive API metadata from Django URL patterns and views.
    Provides 200% functionality for dynamic schema awareness.
    """
    
    def __init__(self):
        self.url_resolver = get_resolver()
        self.metadata_cache = {}
        
    def extract_all_metadata(self) -> Dict[str, Any]:
        """
        Extract metadata from all URL patterns in the Django project.
        
        Returns:
            Comprehensive metadata dictionary with API endpoints
        """
        
        if self.metadata_cache:
            return self.metadata_cache
        
        metadata = {
            'endpoints': {},
            'models': {},
            'field_mappings': {},
            'intent_mappings': {},
            'extraction_timestamp': None
        }
        
        # Extract from URL patterns
        self._extract_from_url_patterns(self.url_resolver.url_patterns, metadata, '')
        
        # Extract model metadata
        self._extract_model_metadata(metadata)
        
        # Build field mappings
        self._build_field_mappings(metadata)
        
        # Build intent mappings
        self._build_intent_mappings(metadata)
        
        self.metadata_cache = metadata
        return metadata
    
    def _extract_from_url_patterns(self, patterns: List, metadata: Dict[str, Any], prefix: str):
        """Recursively extract metadata from URL patterns."""
        
        for pattern in patterns:
            if isinstance(pattern, URLResolver):
                # Recursive case for included URLs
                new_prefix = prefix + str(pattern.pattern)
                self._extract_from_url_patterns(pattern.url_patterns, metadata, new_prefix)
            
            elif isinstance(pattern, URLPattern):
                # Extract from individual URL pattern
                url_path = prefix + str(pattern.pattern)
                self._extract_pattern_metadata(pattern, url_path, metadata)
    
    def _extract_pattern_metadata(self, pattern: URLPattern, url_path: str, metadata: Dict[str, Any]):
        """Extract metadata from a single URL pattern."""
        
        # Clean up the URL path
        clean_path = self._clean_url_path(url_path)
        
        # Skip non-API URLs
        if not self._is_api_url(clean_path):
            return
        
        # Get view information
        view_info = self._extract_view_info(pattern)
        
        # Extract HTTP methods
        methods = self._extract_http_methods(pattern, view_info)
        
        # Extract model information
        model_info = self._extract_model_info(view_info)
        
        # Build endpoint metadata
        endpoint_metadata = {
            'url': clean_path,
            'methods': methods,
            'view_class': view_info.get('class_name'),
            'view_type': view_info.get('type'),
            'model': model_info.get('model_name'),
            'filters': model_info.get('filters', []),
            'search_fields': model_info.get('search_fields', []),
            'sort_fields': model_info.get('sort_fields', []),
            'body_fields': model_info.get('body_fields', []),
            'required_fields': model_info.get('required_fields', []),
            'field_types': model_info.get('field_types', {}),
            'permissions': self._extract_permissions(view_info),
            'description': self._generate_description(clean_path, view_info, model_info)
        }
        
        # Add custom metadata from kwargs if present
        if hasattr(pattern, 'kwargs') and pattern.kwargs:
            custom_metadata = pattern.kwargs
            endpoint_metadata.update(custom_metadata)
        
        metadata['endpoints'][clean_path] = endpoint_metadata
    
    def _clean_url_path(self, url_path: str) -> str:
        """Clean and normalize URL path."""
        
        # Remove regex patterns and convert to clean path
        clean_path = re.sub(r'\^', '', url_path)
        clean_path = re.sub(r'\$', '', clean_path)
        clean_path = re.sub(r'\\', '', clean_path)
        clean_path = re.sub(r'\(\?\w*<\w+>[^)]+\)', '{id}', clean_path)
        clean_path = re.sub(r'\([^)]+\)', '{param}', clean_path)
        
        # Ensure it starts with /
        if not clean_path.startswith('/'):
            clean_path = '/' + clean_path
        
        # Remove double slashes
        clean_path = re.sub(r'/+', '/', clean_path)
        
        return clean_path
    
    def _is_api_url(self, url_path: str) -> bool:
        """Check if URL is an API endpoint."""
        
        api_indicators = [
            '/api/',
            '/employee/',
            '/leave/',
            '/attendance/',
            '/payroll/',
            '/recruitment/',
            'export',
            'import',
            'view',
            'create',
            'update',
            'delete'
        ]
        
        return any(indicator in url_path.lower() for indicator in api_indicators)
    
    def _extract_view_info(self, pattern: URLPattern) -> Dict[str, Any]:
        """Extract information about the view."""
        
        view_info = {}
        
        if hasattr(pattern, 'callback'):
            callback = pattern.callback
            
            if hasattr(callback, 'view_class'):
                # Class-based view
                view_class = callback.view_class
                view_info['class_name'] = view_class.__name__
                view_info['module'] = view_class.__module__
                view_info['type'] = self._determine_view_type(view_class)
                view_info['class'] = view_class
            else:
                # Function-based view
                view_info['function_name'] = callback.__name__
                view_info['module'] = callback.__module__
                view_info['type'] = 'function'
                view_info['function'] = callback
        
        return view_info
    
    def _determine_view_type(self, view_class) -> str:
        """Determine the type of view (list, create, update, delete, etc.)."""
        
        if issubclass(view_class, ListView):
            return 'list'
        elif issubclass(view_class, CreateView):
            return 'create'
        elif issubclass(view_class, UpdateView):
            return 'update'
        elif issubclass(view_class, DeleteView):
            return 'delete'
        else:
            # Try to infer from class name
            class_name = view_class.__name__.lower()
            if 'list' in class_name or 'view' in class_name:
                return 'list'
            elif 'create' in class_name or 'add' in class_name:
                return 'create'
            elif 'update' in class_name or 'edit' in class_name:
                return 'update'
            elif 'delete' in class_name or 'remove' in class_name:
                return 'delete'
            elif 'export' in class_name:
                return 'export'
            else:
                return 'custom'
    
    def _extract_http_methods(self, pattern: URLPattern, view_info: Dict[str, Any]) -> List[str]:
        """Extract supported HTTP methods for the endpoint."""
        
        methods = []
        
        if 'class' in view_info:
            view_class = view_info['class']
            
            # Check for standard HTTP method handlers
            if hasattr(view_class, 'get'):
                methods.append('GET')
            if hasattr(view_class, 'post'):
                methods.append('POST')
            if hasattr(view_class, 'put'):
                methods.append('PUT')
            if hasattr(view_class, 'patch'):
                methods.append('PATCH')
            if hasattr(view_class, 'delete'):
                methods.append('DELETE')
            
            # Infer from view type
            view_type = view_info.get('type')
            if view_type == 'list' and 'GET' not in methods:
                methods.append('GET')
            elif view_type == 'create' and 'POST' not in methods:
                methods.extend(['GET', 'POST'])
            elif view_type == 'update' and 'PUT' not in methods:
                methods.extend(['GET', 'PUT'])
            elif view_type == 'delete' and 'DELETE' not in methods:
                methods.extend(['GET', 'DELETE'])
        
        else:
            # Function-based view - assume GET by default
            methods = ['GET']
        
        return methods or ['GET']
    
    def _extract_model_info(self, view_info: Dict[str, Any]) -> Dict[str, Any]:
        """Extract model information from the view."""
        
        model_info = {}
        
        if 'class' in view_info:
            view_class = view_info['class']
            
            # Try to get model from view
            model = None
            if hasattr(view_class, 'model'):
                model = view_class.model
            elif hasattr(view_class, 'queryset') and view_class.queryset is not None:
                model = view_class.queryset.model
            
            if model:
                model_info = self._extract_model_fields(model)
                model_info['model_name'] = model.__name__
                model_info['model_class'] = model
        
        return model_info
    
    def _extract_model_fields(self, model) -> Dict[str, Any]:
        """Extract field information from a Django model."""
        
        if not model:
            return {}
        
        fields = model._meta.get_fields()
        
        filters = []
        search_fields = []
        sort_fields = []
        body_fields = []
        required_fields = []
        field_types = {}
        
        for field in fields:
            field_name = field.name
            
            # Skip reverse foreign keys and many-to-many relations
            if hasattr(field, 'related_model') and field.many_to_one is False:
                continue
            
            # Add to appropriate lists based on field type
            if isinstance(field, (models.CharField, models.TextField)):
                filters.append(field_name)
                search_fields.append(field_name)
                body_fields.append(field_name)
                field_types[field_name] = 'string'
                
            elif isinstance(field, (models.IntegerField, models.FloatField, models.DecimalField)):
                filters.append(field_name)
                sort_fields.append(field_name)
                body_fields.append(field_name)
                field_types[field_name] = 'number'
                
            elif isinstance(field, (models.DateField, models.DateTimeField)):
                filters.append(field_name)
                sort_fields.append(field_name)
                body_fields.append(field_name)
                field_types[field_name] = 'date'
                
            elif isinstance(field, models.BooleanField):
                filters.append(field_name)
                body_fields.append(field_name)
                field_types[field_name] = 'boolean'
                
            elif isinstance(field, models.EmailField):
                filters.append(field_name)
                search_fields.append(field_name)
                body_fields.append(field_name)
                field_types[field_name] = 'email'
            
            # Check if field is required
            if hasattr(field, 'null') and not field.null and hasattr(field, 'blank') and not field.blank:
                if field_name not in ['id', 'created_at', 'updated_at']:
                    required_fields.append(field_name)
        
        return {
            'filters': filters,
            'search_fields': search_fields,
            'sort_fields': sort_fields,
            'body_fields': body_fields,
            'required_fields': required_fields,
            'field_types': field_types
        }
    
    def _extract_permissions(self, view_info: Dict[str, Any]) -> List[str]:
        """Extract permission requirements from the view."""
        
        permissions = []
        
        if 'class' in view_info:
            view_class = view_info['class']
            
            if hasattr(view_class, 'permission_required'):
                perm = view_class.permission_required
                if isinstance(perm, str):
                    permissions.append(perm)
                elif isinstance(perm, (list, tuple)):
                    permissions.extend(perm)
        
        return permissions
    
    def _generate_description(self, url_path: str, view_info: Dict[str, Any], model_info: Dict[str, Any]) -> str:
        """Generate a human-readable description for the endpoint."""
        
        view_type = view_info.get('type', 'custom')
        model_name = model_info.get('model_name', 'resource')
        
        descriptions = {
            'list': f"List and search {model_name.lower()} records",
            'create': f"Create new {model_name.lower()} record",
            'update': f"Update existing {model_name.lower()} record",
            'delete': f"Delete {model_name.lower()} record",
            'export': f"Export {model_name.lower()} data"
        }
        
        return descriptions.get(view_type, f"Manage {model_name.lower()} records")
    
    def _extract_model_metadata(self, metadata: Dict[str, Any]):
        """Extract metadata for all models used in the API."""
        
        models_metadata = {}
        
        for endpoint_path, endpoint_info in metadata['endpoints'].items():
            model_class = endpoint_info.get('model_class')
            if model_class:
                model_name = model_class.__name__
                if model_name not in models_metadata:
                    models_metadata[model_name] = {
                        'class': model_class,
                        'fields': self._extract_model_fields(model_class),
                        'verbose_name': getattr(model_class._meta, 'verbose_name', model_name),
                        'verbose_name_plural': getattr(model_class._meta, 'verbose_name_plural', model_name + 's')
                    }
        
        metadata['models'] = models_metadata
    
    def _build_field_mappings(self, metadata: Dict[str, Any]):
        """Build natural language to field name mappings."""
        
        field_mappings = {}
        
        for model_name, model_info in metadata['models'].items():
            fields = model_info['fields']
            for field_list_name, field_list in fields.items():
                for field_name in field_list:
                    # Create various natural language mappings
                    mappings = [
                        field_name,
                        field_name.replace('_', ' '),
                        field_name.replace('_', ''),
                    ]
                    
                    # Add specific mappings
                    if 'name' in field_name:
                        mappings.extend(['name', 'full name', 'employee name'])
                    elif 'email' in field_name:
                        mappings.extend(['email', 'email address', 'mail'])
                    elif 'date' in field_name:
                        mappings.extend(['date', 'when', 'time'])
                    
                    for mapping in mappings:
                        field_mappings[mapping.lower()] = field_name
        
        metadata['field_mappings'] = field_mappings
    
    def _build_intent_mappings(self, metadata: Dict[str, Any]):
        """Build intent to endpoint mappings."""
        
        intent_mappings = {}
        
        for endpoint_path, endpoint_info in metadata['endpoints'].items():
            view_type = endpoint_info.get('view_type')
            model_name = endpoint_info.get('model', '').lower()
            
            # Map intents to endpoints
            if view_type == 'list':
                intent_key = f"list_{model_name}"
                intent_mappings[intent_key] = endpoint_path
            elif view_type == 'create':
                intent_key = f"create_{model_name}"
                intent_mappings[intent_key] = endpoint_path
            elif view_type == 'update':
                intent_key = f"update_{model_name}"
                intent_mappings[intent_key] = endpoint_path
            elif view_type == 'delete':
                intent_key = f"delete_{model_name}"
                intent_mappings[intent_key] = endpoint_path
            elif view_type == 'export':
                intent_key = f"export_{model_name}"
                intent_mappings[intent_key] = endpoint_path
        
        metadata['intent_mappings'] = intent_mappings
