{% load i18n %} {% load static %} {% include 'filter_tags.html' %}
<style>
  .row-status--orange {
    border-left: 4px solid orange;
    border-radius: 5px;
  }
  .row-status--red {
    border-left: 4px solid rgb(243, 44, 44);
    border-radius: 5px;
  }
  .row-status--green {
    border-left: 4px solid rgb(2, 190, 2);
    border-radius: 5px;
  }
  .row-status--blue {
    border-left: 4px solid rgb(16, 56, 235);
    border-radius: 5px;
  }
</style>

{% if attendances %}

  <div class="d-flex flex-row-reverse mb-2">
    <span
      class="me-3"
      style="cursor: pointer"
      hx-get='{% url "filter-own-attendance" %}?is_validate_request_approved=true'
      hx-swap="innerHTML"
      hx-target="#attendance-container"
    >
      <span
        class="oh-dot oh-dot--small me-1"
        style="background-color: blue"
      ></span>
      {% trans "Approved request" %}
    </span>
    <span
      class="me-3"
      style="cursor: pointer"
      hx-get='{% url "filter-own-attendance" %}?is_validate_request=true'
      hx-swap="innerHTML"
      hx-target="#attendance-container"
    >
      <span
        class="oh-dot oh-dot--smallui-avatars.com me-1"
        style="background-color: orange"
      ></span>
      {% trans "Requested" %}
    </span>
    <span
      class="me-3"
      style="cursor: pointer"
      hx-get='{% url "filter-own-attendance" %}?attendance_validated=false'
      hx-swap="innerHTML"
      hx-target="#attendance-container"
    >
      <span
        class="oh-dot oh-dot--small me-1"
        style="background-color: red"
      ></span>
      {% trans "Not validated" %}
    </span>
    <span
      class="me-3"
      style="cursor: pointer"
      hx-get='{% url "filter-own-attendance" %}?attendance_validated=true'
      hx-swap="innerHTML"
      hx-target="#attendance-container"
    >
      <span
        class="oh-dot oh-dot--small me-1"
        style="background-color: green"
      ></span>
      {% trans "Validated" %}
    </span>
  </div>
  <div class="oh-table_sticky--wrapper">
    <div class="oh-sticky-dropdown--header">
      <div class="oh-dropdown" x-data="{open: false}">
        <button class="oh-sticky-dropdown_btn " @click="open = !open"><ion-icon name="ellipsis-vertical-sharp"
          role="img" class="md hydrated" aria-label="ellipsis vertical sharp"></ion-icon></button>
        <div class="oh-dropdown__menu oh-sticky-table_dropdown" x-show="open" @click.outside="open = false">
        <ul class="oh-dropdown__items" id="MyAttendanceCells">
        </ul>
        </div>
      </div>
    </div>
  </div>
  <div id="my-attendance-table" data-table-name="my_attendance_tab">
    <div class="oh-sticky-table">
      <div class="oh-sticky-table__table oh-table--sortable">
        <div class="oh-sticky-table__thead">
          <div class="oh-sticky-table__tr">
            <div
              class="oh-sticky-table__th {% if request.sort_option.order == '-employee_id__employee_first_name' %}arrow-up {% elif request.sort_option.order == 'employee_id__employee_first_name' %}arrow-down {% else %} arrow-up-down {% endif %}"
              hx-target="#attendance-container"
              hx-get="{% url 'own-attendance-filter' %}?{{pd}}&orderby=employee_id__employee_first_name"
            >
              {% trans "Employee" %}
            </div>
            <div

              class="oh-sticky-table__th {% if request.sort_option.order == '-attendance_date' %}arrow-up {% elif request.sort_option.order == 'attendance_date' %}arrow-down {% else %} arrow-up-down {% endif %}"
              hx-target="#attendance-container"
              hx-get="{% url 'own-attendance-filter' %}?{{pd}}&orderby=attendance_date"
            >
              {% trans "Date" %}
            </div>
            <div
              data-cell-index="2" data-cell-title='{% trans "Day" %}'
              class="oh-sticky-table__th"
            >
              {% trans "Day" %}
            </div>
            <div
              data-cell-index="3" data-cell-title='{% trans "Check-In" %}'
              class="oh-sticky-table__th"
            >
              {% trans "Check-In" %}
            </div>
            <div
              data-cell-index="4" data-cell-title='{% trans "In Date" %}'
              class="oh-sticky-table__th {% if request.sort_option.order == '-attendance_clock_in_date' %}arrow-up {% elif request.sort_option.order == 'attendance_clock_in_date' %}arrow-down {% else %} arrow-up-down {% endif %}"
              hx-target="#attendance-container"
              hx-get="{% url 'own-attendance-filter' %}?{{pd}}&orderby=attendance_clock_in_date"
            >
              {% trans "In Date" %}
            </div>
            <div
              data-cell-index="5" data-cell-title='{% trans "Check-Out" %}'
              class="oh-sticky-table__th {% if request.sort_option.order == '-attendance_clock_out' %}arrow-up {% elif request.sort_option.order == 'attendance_clock_out' %}arrow-down {% else %} arrow-up-down {% endif %}"
              hx-target="#attendance-container"
              hx-get="{% url 'own-attendance-filter' %}?{{pd}}&orderby=attendance_clock_out"
            >
              {% trans "Check-Out" %}
            </div>
            <div
              data-cell-index="6" data-cell-title='{% trans "Out Date" %}'
              class="oh-sticky-table__th {% if request.sort_option.order == '-attendance_clock_out_date' %}arrow-up {% elif request.sort_option.order == 'attendance_clock_out_date' %}arrow-down {% else %} arrow-up-down {% endif %}"
              hx-target="#attendance-container"
              hx-get="{% url 'own-attendance-filter' %}?{{pd}}&orderby=attendance_clock_out_date"
            >
              {% trans "Out Date" %}
            </div>
            <div
              data-cell-index="7" data-cell-title='{% trans "Shift" %}'
              class="oh-sticky-table__th"
            >
              {% trans "Shift" %}
            </div>
            <div
              data-cell-index="8" data-cell-title='{% trans "Work Type" %}'
              class="oh-sticky-table__th"
            >
              {% trans "Work Type" %}
            </div>
            <div
              data-cell-index="9" data-cell-title='{% trans "Min Hour" %}'
              class="oh-sticky-table__th"
            >
              {% trans "Min Hour" %}
            </div>
            <div
              data-cell-index="10" data-cell-title='{% trans "At Work" %}'
              class="oh-sticky-table__th {% if request.sort_option.order == '-at_work_second' %}arrow-up {% elif request.sort_option.order == 'at_work_second' %}arrow-down {% else %} arrow-up-down {% endif %}"
              hx-target="#attendance-container"
              hx-get="{% url 'own-attendance-filter' %}?{{pd}}&orderby=at_work_second"
            >
              {% trans "At Work" %}
            </div>
            <div
              data-cell-index="11" data-cell-title='{% trans "Pending Hour" %}' class="oh-sticky-table__th"
              >
              {% trans "Pending Hour" %}
            </div>
            <div
              data-cell-index="12" data-cell-title='{% trans "Overtime" %}'
              class="oh-sticky-table__th {% if request.sort_option.order == '-overtime_second' %}arrow-up {% elif request.sort_option.order == 'overtime_second' %}arrow-down {% else %} arrow-up-down {% endif %}"
              hx-target="#attendance-container"
              hx-get="{% url 'own-attendance-filter' %}?{{pd}}&orderby=overtime_second"
            >
              {% trans "Overtime" %}
            </div>
          </div>
        </div>
        <div class="oh-sticky-table__tbody">
          {% for attendance in attendances %}
          <div
            class="oh-sticky-table__tr"
            draggable="false"
            data-toggle="oh-modal-toggle"
            data-target="#objectDetailsModalW25"
            hx-target="#objectDetailsModalW25Target"
            hx-get="{% url 'user-request-one-view' attendance.id %}?my_attendance=true&instances_ids={{attendances_ids}}"
          >
            <div
              class="oh-sticky-table__sd{% if attendance.is_validate_request_approved %} row-status--blue {% elif attendance.attendance_validated %} row-status--green {% elif attendance.is_validate_request %} row-status--orange {% else %} row-status--red{% endif %}"
            >
              <div class="oh-profile oh-profile--md">
                <div class="oh-profile__avatar mr-1">
                  <img
                    src="{{attendance.employee_id.get_avatar}}"
                    class="oh-profile__image"
                    alt="Mary Magdalene"
                  />
                </div>
                <span class="oh-profile__name oh-text--dark"
                  >{{attendance.employee_id}}</span
                >
              </div>
            </div>
            <div class="oh-sticky-table__td dateformat_changer">{{attendance.attendance_date}}</div>
            <div data-cell-index="2" class="oh-sticky-table__td">
              {{attendance.attendance_day|capfirst}}
            </div>
            <div data-cell-index="3" class="oh-sticky-table__td timeformat_changer">
              {{attendance.attendance_clock_in}}
            </div>
            <div data-cell-index="4" class="oh-sticky-table__td dateformat_changer">
              {{attendance.attendance_clock_in_date}}
            </div>
            <div data-cell-index="5" class="oh-sticky-table__td timeformat_changer">
              {{attendance.attendance_clock_out}}
            </div>
            <div data-cell-index="6" class="oh-sticky-table__td dateformat_changer">
              {{attendance.attendance_clock_out_date}}
            </div>
            <div data-cell-index="7" class="oh-sticky-table__td">{{attendance.shift_id}}</div>
            <div data-cell-index="8" class="oh-sticky-table__td">{{attendance.work_type_id}}</div>
            <div data-cell-index="9" class="oh-sticky-table__td">{{attendance.minimum_hour}}</div>
            <div data-cell-index="10" class="oh-sticky-table__td">
              {{attendance.attendance_worked_hour}}
            </div>
            <div data-cell-index="11" class="oh-sticky-table__td">{{ attendance.hours_pending }}</div>
            <div data-cell-index="12" class="oh-sticky-table__td">
              {{attendance.attendance_overtime}}
            </div>
          </div>
          {% endfor %}
        </div>
      </div>
    </div>

    <div class="oh-pagination">
      <span class="oh-pagination__page">
        {% trans "Page" %} {{ attendances.number }} {% trans "of" %} {{ attendances.paginator.num_pages }}.
      </span>
      <nav class="oh-pagination__nav">
        <div class="oh-pagination__input-container me-3">
          <span class="oh-pagination__label me-1">{% trans "Page" %}</span>
          <input
            type="number"
            name="page"
            class="oh-pagination__input"
            value="{{attendances.number}}"
            hx-get="{% url 'own-attendance-filter' %}?{{pd}}"
            hx-target="#attendance-container"
            min="1"
          />
          <span class="oh-pagination__label"
            >{% trans "of" %} {{attendances.paginator.num_pages}}</span
          >
        </div>
        <ul class="oh-pagination__items">
          {% if attendances.has_previous %}
          <li class="oh-pagination__item oh-pagination__item--wide">
            <a
              hx-target="#attendance-container"
              hx-get="{% url 'own-attendance-filter' %}?{{pd}}&page=1"
              class="oh-pagination__link"
              >{% trans "First" %}</a
            >
          </li>
          <li class="oh-pagination__item oh-pagination__item--wide">
            <a
              hx-target="#attendance-container"
              hx-get="{% url 'own-attendance-filter' %}?{{pd}}&page={{ attendances.previous_page_number }}"
              class="oh-pagination__link"
              >{% trans "Previous" %}</a
            >
          </li>
          {% endif %} {% if attendances.has_next %}
          <li class="oh-pagination__item oh-pagination__item--wide">
            <a
              hx-target="#attendance-container"
              hx-get="{% url 'own-attendance-filter' %}?{{pd}}&page={{ attendances.next_page_number }}"
              class="oh-pagination__link"
              >{% trans "Next" %}</a
            >
          </li>
          <li class="oh-pagination__item oh-pagination__item--wide">
            <a
              hx-target="#attendance-container"
              hx-get="{% url 'own-attendance-filter' %}?{{pd}}&page={{ attendances.paginator.num_pages }}"
              class="oh-pagination__link"
              >{% trans "Last" %}</a
            >
          </li>
          {% endif %}
        </ul>
      </nav>
    </div>
  </div>
{% else %}
  <!-- start of empty page -->
  <div>
    <div style="display: flex; flex-direction: column; justify-content: center; align-items: center; height: 100%;" >
      <img
      style="width: 150px; height: 150px"
      src="{% static 'images/ui/no-results.png' %}"
      class="oh-404__image mb-4"
      />
      <h5 class="oh-404__subtitle">
      {% trans "No search result found!" %}
      </h5>
    </div>
  </div>
  <!-- end of empty page -->
{% endif %}

<script>
  // toggle columns //
  toggleColumns("my-attendance-table","MyAttendanceCells")
  localStorageMyAttendanceCells = localStorage.getItem("my_attendance_tab")
  if (!localStorageMyAttendanceCells) {
    $("#MyAttendanceCells").find("[type=checkbox]").prop("checked",true)
  }
  $("[type=checkbox]").change()
</script>
