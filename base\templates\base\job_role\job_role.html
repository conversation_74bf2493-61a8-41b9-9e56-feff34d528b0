{% extends 'settings.html' %} {% load i18n %} {% block settings %} {% load static %}
<div class="oh-inner-sidebar-content">
	{% if perms.base.view_jobrole %}
	<div class="oh-inner-sidebar-content__header d-flex justify-content-between align-items-center">
		<h2 class="oh-inner-sidebar-content__title">{% trans "Job Role" %}</h2>
		{% if perms.base.add_jobrole %}
        <button
			class="oh-btn oh-btn--secondary oh-btn--shadow"
			data-toggle="oh-modal-toggle"
			data-target="#jobRoleModal"
			hx-get="{% url 'job-role-create' %}"
			hx-target="#jobRoleForm"
		>
			<ion-icon name="add-outline" class="me-1"></ion-icon>
			{% trans "Create" %}
		</button>
		{% endif %}
	</div>
	{% if job_role %}
		{% include 'base/job_role/job_role_view.html' %}
	{% else %}
		<div style="display: flex; flex-direction: column; justify-content: center; align-items: center; height: 100%;">
			<img style="display: block; width: 15%; margin: 20px auto; filter: opacity(0.5);" src="{% static 'images/ui/job_role.png' %}" class="" alt="Page not found. 404." />
			<h5 class="oh-404__subtitle">{% trans "There is no Job roles at this moment." %}</h5>
		</div>
	{% endif %}
    {% endif %}

</div>

<div
	class="oh-modal"
	id="jobRoleModal"
	role="dialog"
	aria-labelledby="jobRoleModal"
	aria-hidden="true"
>
	<div class="oh-modal__dialog" id="jobRoleForm"> </div>
</div>

{% endblock settings %}
