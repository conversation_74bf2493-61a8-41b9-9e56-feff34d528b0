"""
Command Processor for AI Assistant

This module processes AI assistant commands and executes appropriate actions
based on user permissions and security validation.
"""

import logging
from typing import Dict, Any, Optional
from django.contrib.auth.models import User
from django.urls import reverse, NoReverseMatch
from django.db.models import Q

from employee.models import Employee, EmployeeWorkInformation
from attendance.models import Attendance
from leave.models import LeaveRequest, LeaveType
from payroll.models.models import Payslip
from base.models import Department, JobPosition
from .security import SecurityValidator

logger = logging.getLogger(__name__)


class CommandProcessor:
    """Process AI assistant commands with security and permission checks."""
    
    def __init__(self, user: User):
        self.user = user
        self.security_validator = SecurityValidator(user)
        
    def process_command(self, command_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a command result and execute appropriate actions.
        
        Args:
            command_result: Dictionary containing intent, filters, etc.
            
        Returns:
            Updated command result with processed data
        """
        intent = command_result.get('intent', 'reply')
        module = command_result.get('module', 'general')
        
        try:
            # Route to appropriate processor based on intent
            if intent == 'search':
                return self._process_search_command(command_result)
            elif intent == 'fill_form':
                return self._process_form_command(command_result)
            elif intent == 'run_action':
                return self._process_action_command(command_result)
            elif intent == 'export':
                return self._process_export_command(command_result)
            elif intent == 'import':
                return self._process_import_command(command_result)
            elif intent == 'navigate':
                return self._process_navigation_command(command_result)
            else:
                return command_result
                
        except Exception as e:
            logger.error(f"Error processing command: {e}")
            command_result['message'] = "Sorry, I encountered an error processing your request."
            command_result['intent'] = 'error'
            return command_result
    
    def _process_search_command(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """Process search commands."""
        module = result.get('module', 'general')
        filters = result.get('filters', {})
        
        if module == 'employee':
            return self._search_employees(result, filters)
        elif module == 'leave':
            return self._search_leave_requests(result, filters)
        elif module == 'attendance':
            return self._search_attendance(result, filters)
        elif module == 'payroll':
            return self._search_payroll(result, filters)
        else:
            result['message'] = "Search functionality available for employees, leave, attendance, and payroll."
            
        return result
    
    def _search_employees(self, result: Dict[str, Any], filters: Dict[str, Any]) -> Dict[str, Any]:
        """Search employees based on filters."""
        try:
            # Build URL with filters
            url_params = []
            
            if filters.get('department'):
                url_params.append(f"employee_work_info__department__department__icontains={filters['department']}")
            
            if filters.get('name'):
                url_params.append(f"employee_first_name__icontains={filters['name']}")
            
            if filters.get('location'):
                url_params.append(f"employee_work_info__location__icontains={filters['location']}")
            
            # Add sorting
            sort_by = result.get('sort_by')
            order = result.get('order', 'asc')
            
            if sort_by:
                sort_param = self._map_sort_field(sort_by)
                if sort_param:
                    if order == 'desc':
                        sort_param = f"-{sort_param}"
                    url_params.append(f"ordering={sort_param}")
            
            # Build final URL
            base_url = "/employee/employee-view/"
            if url_params:
                result['redirect_url'] = f"{base_url}?{'&'.join(url_params)}"
            else:
                result['redirect_url'] = base_url
            
            # Update message
            filter_desc = self._describe_filters(filters)
            if filter_desc:
                result['message'] = f"Showing employees {filter_desc}"
            else:
                result['message'] = "Showing all employees"
                
        except Exception as e:
            logger.error(f"Error searching employees: {e}")
            result['message'] = "Error searching employees. Please try again."
            
        return result
    
    def _search_leave_requests(self, result: Dict[str, Any], filters: Dict[str, Any]) -> Dict[str, Any]:
        """Search leave requests based on filters."""
        try:
            url_params = []
            
            if filters.get('employee_name'):
                url_params.append(f"employee_id__employee_first_name__icontains={filters['employee_name']}")
            
            if filters.get('status'):
                url_params.append(f"status={filters['status']}")
            
            if filters.get('leave_type'):
                url_params.append(f"leave_type_id__name__icontains={filters['leave_type']}")
            
            base_url = "/leave/leave-request-view/"
            if url_params:
                result['redirect_url'] = f"{base_url}?{'&'.join(url_params)}"
            else:
                result['redirect_url'] = base_url
            
            result['message'] = "Showing leave requests"
            
        except Exception as e:
            logger.error(f"Error searching leave requests: {e}")
            result['message'] = "Error searching leave requests. Please try again."
            
        return result
    
    def _search_attendance(self, result: Dict[str, Any], filters: Dict[str, Any]) -> Dict[str, Any]:
        """Search attendance records based on filters."""
        try:
            url_params = []
            
            if filters.get('employee_name'):
                url_params.append(f"employee_id__employee_first_name__icontains={filters['employee_name']}")
            
            if filters.get('date'):
                url_params.append(f"attendance_date={filters['date']}")
            
            base_url = "/attendance/attendance-view/"
            if url_params:
                result['redirect_url'] = f"{base_url}?{'&'.join(url_params)}"
            else:
                result['redirect_url'] = base_url
            
            result['message'] = "Showing attendance records"
            
        except Exception as e:
            logger.error(f"Error searching attendance: {e}")
            result['message'] = "Error searching attendance. Please try again."
            
        return result
    
    def _search_payroll(self, result: Dict[str, Any], filters: Dict[str, Any]) -> Dict[str, Any]:
        """Search payroll records based on filters."""
        try:
            url_params = []
            
            if filters.get('employee_name'):
                url_params.append(f"employee_id__employee_first_name__icontains={filters['employee_name']}")
            
            if filters.get('month'):
                url_params.append(f"start_date__month={filters['month']}")
            
            base_url = "/payroll/payslip-view/"
            if url_params:
                result['redirect_url'] = f"{base_url}?{'&'.join(url_params)}"
            else:
                result['redirect_url'] = base_url
            
            result['message'] = "Showing payroll records"
            
        except Exception as e:
            logger.error(f"Error searching payroll: {e}")
            result['message'] = "Error searching payroll. Please try again."
            
        return result
    
    def _process_form_command(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """Process form-related commands."""
        module = result.get('module', 'general')
        prefill_data = result.get('prefill_data', {})
        
        if module == 'leave':
            result['redirect_url'] = "/leave/leave-request-create/"
            if prefill_data:
                # Convert prefill data to URL parameters
                params = []
                for key, value in prefill_data.items():
                    params.append(f"{key}={value}")
                if params:
                    result['redirect_url'] += f"?{'&'.join(params)}"
            
            result['message'] = "Opening leave request form"
        else:
            result['message'] = "Form functionality available for leave requests."
            
        return result
    
    def _process_action_command(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """Process action commands like running payroll."""
        module = result.get('module', 'general')
        
        if module == 'payroll':
            result['redirect_url'] = "/payroll/payslip-create/"
            result['message'] = "Opening payroll processing"
        else:
            result['message'] = "Action functionality available for payroll processing."
            
        return result
    
    def _process_export_command(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """Process export commands."""
        module = result.get('module', 'general')
        
        if module == 'employee':
            result['redirect_url'] = "/employee/employee-export/"
            result['message'] = "Preparing employee data export"
        elif module == 'attendance':
            result['redirect_url'] = "/attendance/attendance-export/"
            result['message'] = "Preparing attendance data export"
        else:
            result['message'] = "Export functionality available for employees and attendance."
            
        return result
    
    def _process_import_command(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """Process import commands."""
        module = result.get('module', 'general')
        
        if module == 'employee':
            result['redirect_url'] = "/employee/employee-import/"
            result['message'] = "Opening employee data import"
        else:
            result['message'] = "Import functionality available for employee data."
            
        return result
    
    def _process_navigation_command(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """Process navigation commands."""
        # This will be enhanced with URL mapping
        result['message'] = "Navigation functionality will be enhanced with URL mapping."
        return result
    
    def _map_sort_field(self, sort_by: str) -> str:
        """Map natural language sort fields to database fields."""
        field_mapping = {
            'salary': 'employee_work_info__basic_salary',
            'joining_date': 'employee_work_info__date_joining',
            'name': 'employee_first_name',
            'department': 'employee_work_info__department__department',
            'experience': 'employee_work_info__experience',
        }
        return field_mapping.get(sort_by, '')
    
    def _describe_filters(self, filters: Dict[str, Any]) -> str:
        """Create a human-readable description of applied filters."""
        descriptions = []
        
        if filters.get('department'):
            descriptions.append(f"in {filters['department']} department")
        
        if filters.get('name'):
            descriptions.append(f"named '{filters['name']}'")
        
        if filters.get('location'):
            descriptions.append(f"in {filters['location']}")
        
        return " ".join(descriptions)
