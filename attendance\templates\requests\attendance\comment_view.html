{% load basefilters %}
{% load i18n static %}
{% if messages %}
    <div class="oh-alert-container">
      {% for message in messages %}
      <div class="oh-alert oh-alert--animated {{message.tags}}">{{ message }}</div>
      {% endfor %}
    </div>
{% endif %}
{% if no_comments %}

<div class="oh-timeoff-modal__profile-content">
    <div class="">
        <div>
            <span class="oh-timeoff-title fw-bold" style="display: block;">{% trans "There is no comments to show." %}</span>
            <img style="display: block;width: 100px;margin: 20px auto ;" src="{% static "/images/ui/comment.png" %}" class="">
        </div>
    </div>
</div>

{% else %}

    {% for comment in comments %}
        <div class="oh-timeoff-modal__profile-content">
            <div class="oh-profile ms-4">
                <div>
                    <span class="oh-timeoff-modal__stat-title fw-bold">{% trans "Comment" %}

                    <a hx-get='{% url "attendance-request-delete-comment" comment.id %}'
                       data-target="#shiftRequestDetailModal"
                       hx-swap="innerHTML"
                       hx-target="#shiftRequestDetailTarget" title="{% trans 'Delete Comment' %}"
                       >
                        <ion-icon name="close-circle-outline" class="text-danger ms-3 mt-1" aria-label="close outline"></ion-icon></a>
                    </span>
                    <div>
                        <span class="oh-timeoff-title fw-bold">{{comment.comment}}</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="oh-modal__dialog-body">
            <div class="oh-timeoff-modal__stats-container">
                <div class="oh-timeoff-modal__stat">
                    <span class="oh-timeoff-modal__stat-title fw-bold">{% trans "By" %}</span>
                    <a style="text-decoration:none;"
                    href ="{% url 'employee-view-individual' comment.employee_id.id %}">
                    <span class="oh-timeoff-modal__stat-count">{{ comment.employee_id }}</span></a>
                </div>
                <div class="oh-timeoff-modal__stat" style="margin-left: 20px;">
                    <span class="oh-timeoff-modal__stat-title fw-bold">{% trans "Date & Time" %}</span>
                    <span class="oh-timeoff-modal__stat-count">
                        {% trans "on" %} &nbsp<span class="dateformat_changer">{{ comment.created_at|date:"F j, Y" }}</span> &nbsp
                        {% trans "at" %} &nbsp <span class="timeformat_changer">{{ comment.created_at|time:"g:i A" }}</span>
                    </span>
                </div>

            </div>
            <hr>
        </div>


    {% endfor %}

{% endif %}
