{% load i18n %}
<div class="oh-modal__dialog-header">
    <span class="oh-modal__dialog-title" id="editModal1ModalLabel">
        {% if employee_type.id %} {% trans "Update" %} {% else %} {% trans "Create" %} {% endif %} {{form.verbose_name}}
    </span>
    <button class="oh-modal__close" aria-label="Close">
        <ion-icon name="close-outline"></ion-icon>
    </button>
</div>
<div class="oh-modal__dialog-body">
    <form hx-post="{{ request.get_full_path }}"
        hx-target=" {% if dynamic %} #dynamicCreateModalBody {% else %} #employeeTypeForm {% endif %}"
        class="oh-profile-section">
        {% csrf_token %} {{form.non_field_errors}}
        {{form.as_p}}
        <div class="oh-modal__dialog-footer p-0 mt-3">
            <button type="submit" class="oh-btn oh-btn--secondary oh-btn--shadow">
                {% trans "Save" %}
            </button>
        </div>
    </form>
</div>
