{% load i18n %}{% load static %}
<div class="oh-dropdown" x-data="{open: false}">
  <button class="oh-btn" @click="open = !open" onclick = event.preventDefault()>
    <ion-icon name="filter" class="mr-1"></ion-icon>{% trans "Filter" %}<div id="filterCount"></div>
  </button>
  <div
    class="oh-dropdown__menu oh-dropdown__menu--right oh-dropdown__filter p-4"
    x-show="open"
    @click.outside="open = false"
    style="display: none;"
  >

      <div class="oh-dropdown__filter-body">
                <div class="oh-accordion">
          <div class="oh-accordion-header">{% trans "Attendance" %}</div>
          <div class="oh-accordion-body">
            <div class="row">
              <div class="col-sm-12 col-md-12 col-lg-6">
                <div class="oh-input-group">
                  <label class="oh-label">{% trans "Attendance Date" %}</label>
                  {{f.form.attendance_date}}
                </div>
                <div class="oh-input-group">
                  <label class="oh-label">{% trans "In Time" %}</label>
                  {{f.form.attendance_clock_in}}
                </div>
                <div class="oh-input-group">
                  <label class="oh-label">{% trans "Validated?" %}</label>
                  {{f.form.attendance_validated}}
                </div>
                <div class="oh-input-group">
                  <label class="oh-label">{% trans "Requested?" %}</label>
                  {{f.form.is_validate_request}}
                </div>
              </div>
              <div class="col-sm-12 col-md-12 col-lg-6">
                <div class="oh-input-group">
                  <label class="oh-label">{% trans "Min Hour" %}</label>
                  {{f.form.minimum_hour}}
                </div>
                <div class="oh-input-group">
                  <label class="oh-label">{% trans "Out Time" %}</label>
                  {{f.form.attendance_clock_out}}
                </div>
                <div class="oh-input-group">
                  <label class="oh-label">{% trans "OT Approved?" %}</label>
                  {{f.form.attendance_overtime_approve}}
                </div>
                <div class="oh-input-group">
                  <label class="oh-label">{% trans "Approved Request" %}</label>
                  {{f.form.is_validate_request_approved}}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="oh-accordion">
          <div class="oh-accordion-header">{% trans "Advanced" %}</div>
          <div class="oh-accordion-body">
            <div class="row">
              <div class="col-sm-12 col-md-12 col-lg-6">
                <div class="oh-input-group">
                  <label class="oh-label">{% trans "Attendance From" %}</label>
                  {{f.form.attendance_date__gte}}
                </div>
                <div class="oh-input-group">
                  <label class="oh-label">{% trans "In From" %}</label>
                  {{f.form.attendance_clock_in__lte}}
                </div>
                <div class="oh-input-group">
                  <label class="oh-label">{% trans "Out From" %}</label>
                  {{f.form.attendance_clock_out__lte}}
                </div>
                <div class="oh-input-group">
                  <label class="oh-label">{% trans "At Work Greater or Equal" %}</label>
                  {{f.form.at_work_second__gte}}
                </div>
                <div class="oh-input-group">
                  <label class="oh-label">{% trans "OT Greater or Equal" %}</label>
                  {{f.form.overtime_second__gte}}
                </div>
              </div>
              <div class="col-sm-12 col-md-12 col-lg-6">
                <div class="oh-input-group">
                  <label class="oh-label">{% trans "Attendance Till" %}</label>
                  {{f.form.attendance_date__lte}}
                </div>
                <div class="oh-input-group">
                  <label class="oh-label">{% trans "In Till" %}</label>
                  {{f.form.attendance_clock_in__lte}}
                </div>
                <div class="oh-input-group">
                  <label class="oh-label">{% trans "Out Till" %}</label>
                  {{f.form.attendance_clock_out__lte}}
                </div>
                <div class="oh-input-group">
                  <label class="oh-label">{% trans "At Work Less Than or Equal" %}</label>
                  {{f.form.at_work_second__lte}}
                </div>
                <div class="oh-input-group">
                  <label class="oh-label">{% trans "OT Less Than or Equal" %}</label>
                  {{f.form.overtime_second__lte}}
                </div>
              </div>
            </div>
          </div>
        </div>
        {% comment %} <div class="oh-accordion">
          <div class="oh-accordion-header">{% trans "Group By" %}</div>
          <div class="oh-accordion-body">
            <div class="row">
              <div class="col-sm-12 col-md-12 col-lg-6">
                <div class="oh-input-group">
                  <label class="oh-label">{% trans "Field" %}</label>
                </div>
              </div>
              <div class="col-sm-12 col-md-12 col-lg-6">
                <div class="oh-input-group">
                  <select
                    class="oh-select mt-1"
                    id="field"
                    name="field"
                    class="select2-selection select2-selection--single"
                    id="gp"
                  >
                    {% for field in gp_fields %}
                    <option value="{{ field.0 }}">{{ field.1 }}</option>
                    {% endfor %}
                  </select>
                </div>
              </div>
            </div>
          </div>
        </div> {% endcomment %}
      </div>
      <div class="oh-dropdown__filter-footer">
        <button
          class="oh-btn oh-btn--secondary oh-btn--small w-100 filterButton"
          id="filterSubmit"
        >
          {% trans "Filter" %}
        </button>
      </div>
    </form>
  </div>
</div>
<script src="{% static '/base/filter.js' %}"></script>
