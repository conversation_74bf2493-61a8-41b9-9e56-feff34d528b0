{% extends "index.html" %} {% load i18n %} {% block content %} {% include 'work_type_request/work_type_request_nav.html' %}
<div
    class="oh-checkpoint-badge mb-2"
    id="selectedWorktypes"
    data-ids="[]"
    data-clicked=""
    style="display: none"
>
    {% trans "Selected Worktypes" %}
</div>
<div class="oh-wrapper">
    {% if data %}
        <!-- start of Quick filters  -->
        <div class="d-flex flex-row-reverse" style="height:3px;">
            <span
            class="m-3 review_ongoing"
            onclick="$('[name=canceled]').val('true');$('[name=approved]').val('unknown');$('[name=canceled]').first().change();$('.filterButton').click()"
            style="cursor: pointer"
            >
            <span
                class="oh-dot oh-dot--small me-1"
                style="background-color: red"
            ></span>
            {% trans "Rejected" %}
            </span>
            <span
            class="m-3 paid"
            onclick="$('[name=approved]').val('true');$('[name=canceled]').val('unknown');$('[name=approved]').first().change();$('.filterButton').click()"
            style="cursor: pointer"
            >
            <span
                class="oh-dot oh-dot--small me-1"
                style="background-color: yellowgreen"
            ></span>
            {% trans "Approved" %}
            </span>
        </div>
        <!-- end of quick filters  -->
    {% endif %}
    <div id="view-container" >
        {% if data %}
            {% include 'work_type_request/htmx/requests.html' %}
        {% else %}
            {% include 'work_type_request/htmx/empty_request.html' %}
        {% endif %}
    </div>
</div>

<div class="oh-activity-sidebar" id="worktypeactivitySidebar" style="z-index:1000;">
    <div class="oh-activity-sidebar__body" id="commentContainer"></div>
</div>

<script>
    $(document).on("htmx:load", "#view-container", function () {
        $("[data-toggle='oh-modal-toggle']").on("click", function () {
            let clickedEl = $(this).closest(
                '[data-toggle = "oh-modal-toggle"]'
            );
            if (clickedEl != null) {
                const targetEl = clickedEl.data("target");
                $(targetEl).addClass("oh-modal--show");
            }
        });
        $(".oh-modal__close").on("click", function () {
            $(".oh-modal--show").removeClass("oh-modal--show");
        });
    });
	function enlargeImage(src,$element) {
		$(".enlargeImageContainer").empty()
		var enlargeImageContainer = $element.parents().closest("li").find(".enlargeImageContainer")
		enlargeImageContainer.empty()
		style = 'width:100%; height:90%; box-shadow: 0 10px 10px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.2); background:white'
		var enlargedImage = $('<iframe>').attr({ src: src, style: style })
		var name = $('<span>').text(src.split('/').pop().replace(/_/g, ' '))
		enlargeImageContainer.append(enlargedImage)
		enlargeImageContainer.append(name)
		setTimeout(function () {
			enlargeImageContainer.show()

			const iframe = document.querySelector('iframe').contentWindow
			var iframe_document = iframe.document
			iframe_image = iframe_document.getElementsByTagName('img')[0]
			$(iframe_image).attr('style', 'width:100%; height:100%;')
		}, 100)
	}

	function hideEnlargeImage() {
		var enlargeImageContainer = $('.enlargeImageContainer')
		enlargeImageContainer.empty()
	}

	$(document).on('click', function (event) {
		if (!$(event.target).closest('#enlargeImageContainer').length) {
			hideEnlargeImage()
		}
	})
	function submitForm(elem) {
	  $(elem).siblings(".add_more_submit").click();
  }
</script>
{% endblock content %}
