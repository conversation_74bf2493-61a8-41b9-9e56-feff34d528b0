{% load i18n %} {% load static %}
{% load basefilters %}
{% if messages %}
    <div class="oh-wrapper">
    {% for message in messages %}
        <div class="oh-alert-container">
            <div class="oh-alert oh-alert--animated {{message.tags}}">
            {{ message }}
            </div>
        </div>
    {% endfor %}
    </div>
    <span hx-trigger="load" hx-get="{{close_hx_url}}?{{pd}}" hx-target="{{close_hx_target}}"></span>
{% endif %}
<div class="oh-modal__dialog-header">
    {% if instance %}
        <span
          class="oh-modal__dialog-title"
          id="objectDetailsModalLabel"
            >
          {% trans "Details" %}
        </span>
    {% endif %}
    <button class="oh-modal__close" aria-label="Close">
      <ion-icon name="close-outline"></ion-icon>
    </button>
</div>
{% if instance %}
    <div class="oh-modal__dialog-body oh-modal__dialog-relative">
    {% if request.GET.instances_ids %}
        <div class="oh-modal__dialog oh-modal__dialog--navigation m-0 p-0">
            <button hx-get="{% url 'rwork-individual-view' previous %}?{{pd}}&instances_ids={{assign_ids}}"
                    hx-target = "#objectDetailsModalTarget" class="oh-modal__diaglog-nav oh-modal__nav-prev"
                    data-action="previous"
                >
                <ion-icon name="chevron-back-outline" class="md hydrated" role="img"
                aria-label="chevron back outline"></ion-icon>
            </button>

            <button hx-get="{% url 'rwork-individual-view' next  %}?{{pd}}&instances_ids={{assign_ids}}"
                    hx-target = "#objectDetailsModalTarget" class="oh-modal__diaglog-nav oh-modal__nav-next"
                    data-action="next"
                >
                <ion-icon name="chevron-forward-outline" class="md hydrated" role="img"
                aria-label="chevron forward outline"></ion-icon>
            </button>
        </div>
    {% endif %}
    <a class="oh-timeoff-modal__profile-content" style="text-decoration:none;"
        href ="{% url 'employee-view-individual' instance.employee_id.id %}">
    <div class="oh-profile mb-2">
        <div class="oh-profile__avatar">
        <img
            src="{{instance.employee_id.get_avatar}}"
            class="oh-profile__image me-2"
            alt="Mary Magdalene"
        />
        </div>
        <div class="oh-timeoff-modal__profile-info">
        <span class="oh-timeoff-modal__user fw-bold"
            >{{instance.employee_id}}</span
        >
        <span
            class="oh-timeoff-modal__user m-0"
            style="font-size: 18px; color: #4d4a4a"
        >
            {{instance.employee_id.employee_work_info.department_id}} /
            {{instance.employee_id.employee_work_info.job_position_id}}</span
        >
        </div>
    </div>
    </a>
    <div class="oh-modal__dialog-header" style="padding-top: 5px;">
        <div class="oh-timeoff-modal__stats-container">
            <div class="oh-timeoff-modal__stat" >
                <span class="oh-timeoff-modal__stat-title">{% trans "Title" %}</span>
                <span class="oh-timeoff-modal__stat-count">{{instance.get_based_on_display}}</span>
            </div>
            <div class="oh-timeoff-modal__stat" >
                <span class="oh-timeoff-modal__stat-title">{% trans "Based On" %}</span>
                <span class="oh-timeoff-modal__stat-count">
                    {{instance.get_based_on_display}}
                </span>
            </div>
        </div>
        <div class="oh-timeoff-modal__stats-container mt-3 mb-3">
            <div class="oh-timeoff-modal__stat">
                <span class="oh-timeoff-modal__stat-title">{% trans "Rotate" %}</span>
                <span class="oh-timeoff-modal__stat-count">
                    {% if instance.based_on == 'after' %}
                        {% trans "Rotate after" %} {{instance.rotate_after_day}} {% trans "days" %}
                    {% elif instance.based_on == "weekly" %}
                        {% trans "Weekly every" %} {{instance.rotate_every_weekend}}
                    {% elif instance.based_on == "monthly" %}
                        {% if instance.rotate_every == "1" %}
                            {% trans "Rotate every" %} {{instance.rotate_every}}{% trans "st day of month" %}
                        {% elif instance.rotate_every == "2" %}
                            {% trans "Rotate every" %} {{instance.rotate_every}}{% trans "nd day of month" %}
                        {% elif instance.rotate_every == "3" %}
                            {% trans "Rotate every" %} {{instance.rotate_every}}{% trans "rd day of month" %}
                        {% elif instance.rotate_every == "last" %}
                            {% trans "Rotate every last day of month" %}
                        {% else %}
                            {% trans "Rotate every" %} {{instance.rotate_every}}{% trans "th day of month" %}
                        {% endif %}
                    {% endif %}
                </span>
            </div>
            <div class="oh-timeoff-modal__stat">
                <span class="oh-timeoff-modal__stat-title">{% trans "Start Date" %}</span>
                <span class="oh-timeoff-modal__stat-count dateformat_changer">{{instance.start_date}}</span>
            </div>
        </div>
        <div class="oh-timeoff-modal__stats-container mt-3 mb-3">
            <div class="oh-timeoff-modal__stat" >
                <span class="oh-timeoff-modal__stat-title">{% trans "Current Work Type" %}</span>
                <span class="oh-timeoff-modal__stat-count">{{instance.current_work_type}}</span>
            </div>
            <div class="oh-timeoff-modal__stat" >
                <span class="oh-timeoff-modal__stat-title">{% trans "Next Work Type" %}</span>
                <span class="oh-timeoff-modal__stat-count">
                    {{instance.next_work_type}}
                </span>
            </div>
        </div>

        <div class="oh-timeoff-modal__stats-container mt-3 mb-3" >
            <div class="oh-timeoff-modal__stat" >
                <span class="oh-timeoff-modal__stat-title">{% trans "Next Change Date" %}</span>
                <span class="oh-timeoff-modal__stat-count dateformat_changer">
                    {{instance.next_change_date}}
                </span>
            </div>
            <div class="oh-timeoff-modal__stat" >
                <span class="oh-timeoff-modal__stat-title">{% trans "Status" %}</span>
                <span class="oh-timeoff-modal__stat-count">{% if instance.is_active %}{% trans "Is Active" %}{% else %}{% trans "Archived" %}{% endif %}</span>
            </div>
        </div>
        {% if perms.base.change_rotatingworktypeassign or request.user|is_reportingmanager or perms.base.delete_rotatingworktypeassign %}
            <div class="oh-modal__button-container text-center mt-3">
                <div class="oh-btn-group">
                    {% if perms.base.change_rotatingworktypeassign or request.user|is_reportingmanager  %}
                        <a class="oh-btn oh-btn--info" hx-get="{% url "rotating-work-type-assign-update" instance.id %}" hx-target="#objectUpdateModalTarget" data-toggle="oh-modal-toggle" data-target="#objectUpdateModal" style="width: 50%;">
                            <ion-icon name="create-outline">
                            </ion-icon>{% trans "Edit" %}
                        </a>
                        <form  class="oh-btn oh-btn--primary" style="width: 50%;" hx-confirm="{% trans 'Do you want to archive this rotating work type assign?' %}" hx-get="{% url 'rotating-work-type-assign-archive' instance.id %}?{{pd}}&instances_ids={{assign_ids}}" hx-target="#objectDetailsModalTarget">
                            <button style="background: none;
                                color: inherit;
                                border: none;
                                padding: 0;
                                font: inherit;
                                cursor: pointer;
                                outline: inherit;">
                                <ion-icon name="archive-outline">
                                </ion-icon>
                                {% if instance.is_active %}
                                    {% trans "Archive" %}
                                {% else %}
                                    {% trans "Un-archive" %}
                                {% endif %}
                            </button>
                        </form>
                    {% endif %}
                    {% if perms.base.delete_rotatingworktypeassign or request.user|is_reportingmanager %}
                        <form  class="oh-btn oh-btn--secondary" style="width: 50%;" hx-confirm="{% trans 'Are you sure you want to delete this rotating work type assign?' %}" hx-post="{% url 'rotating-work-type-assign-delete' instance.id %}?{{pd}}&instances_ids={{assign_ids}}" hx-target="#objectDetailsModalTarget">
                            {% csrf_token %}
                            <button style="background: none;
                                color: inherit;
                                border: none;
                                padding: 0;
                                font: inherit;
                                cursor: pointer;
                                outline: inherit;">
                                <ion-icon name="trash-outline">
                                </ion-icon>{% trans "Delete" %}
                            </button>
                        </form>
                    {% endif %}
                </div>
            </div>
        {% endif %}
    </div>
    </div>
{% else %}
    <div class="oh-modal__dialog-body oh-modal__dialog-relative">
        <img
        style="margin-left: 33%; width: 150px; height: 150px"
        src="{% static 'images/ui/employee_shift.png' %}"
        class="oh-404__image mb-2"
        alt="Page not found. 404."
        />
        <h5 class="oh-404__subtitle">
        {% trans "There are no rotating work type assigned to this employee." %}
        </h5>
    </div>
{% endif %}
