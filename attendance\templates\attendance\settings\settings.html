{% load i18n %}
{% csrf_token %}
<div class="oh-inner-sidebar-content__header mt-4">
    <h2 class="oh-inner-sidebar-content__title">{% trans 'Time Runner' %}</h2>
</div>
<form  hx-get="{% url 'enable-timerunner' %}" hx-swap="none">
  <div class="oh-label__info" for="application_tracking">
      <label class="oh-label" for="application_tracking">{% trans "At-Work Tracker" %}</label>
      <span class="oh-info mr-2" title="{% trans "By enabling this feature user's will be able to see their current day worked hours in live on the navbar (inside the check in button)." %}">
      </span>
  </div>
  <div class="oh-switch p-3">
    <input type="checkbox"class="oh-switch__checkbox" name="time_runner" onchange="$(this).closest('form').find('input[type=submit]').click()" {% if enabled_timerunner %} checked{% endif %} />
  </div>
  <input type="submit" hidden />
</form>
<div class="oh-inner-sidebar-content__footer"></div>
