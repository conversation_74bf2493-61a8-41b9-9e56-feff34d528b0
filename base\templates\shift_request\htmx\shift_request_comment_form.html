{% load i18n %}
{% if form.errors %}
<!-- form errors  -->
    <div class="oh-wrapper">
        <div class="oh-alert-container">
            {% for error in form.non_field_errors %}
            <div class="oh-alert oh-alert--animated oh-alert--danger">
                {{ error }}
            </div>
            {% endfor %}
        </div>
    </div>
{% endif %}
<form
    hx-post="{% url 'shift-request-add-comment' request_id %}"
    hx-target="#shiftRequestCommentForm"
    method="post"
    hx-encoding="multipart/form-data">
    {% csrf_token %}
    {{form.as_p}}
    <button
		type="submit"
		class="oh-btn oh-btn--secondary mt-2 mr-0 oh-btn--w-100-resp"
	>
		{% trans "Save" %}
	</button>
</form>
