{% load i18n %}
<div class="oh-modal__dialog-header">
    <button type="button" class="oh-modal__close" data-dismiss="oh-modal" aria-label="Close">
        <ion-icon name="close-outline"></ion-icon>
    </button>
    <span class="oh-modal__dialog-title ml-5" id="addEmployeeObjectiveModalLabel">
        <h5>{{asset_allocation_form.verbose_name}}</h5>
    </span>
</div>
<div class="oh-modal__dialog-body">
    <form hx-post="{%url 'asset-request-approve' req_id=id %}" hx-target="#objectCreateModalTarget"
        hx-encoding="multipart/form-data">
        {% csrf_token %} {{asset_allocation_form.non_field_errors}}
        {{asset_allocation_form.errors}}
        <div class="oh-profile-section pt-0">
            <div class="oh-input__group">
                <label class="oh-input__label" for="{{asset_allocation_form.asset_id.id_for_label}}">
                    {{asset_allocation_form.asset_id.label}}
                </label>
                {{asset_allocation_form.asset_id}}
                {{asset_allocation_form.asset_id.errors}}
            </div>
            <div class="oh-input__group">
                <label class="oh-input__label" for="{{asset_allocation_form.assign_images.id_for_label}}">
                    {{asset_allocation_form.assign_images.label}}
                </label>
                {{asset_allocation_form.assign_images}}
                {{asset_allocation_form.assign_images.errors}}
            </div>
            <div class="oh-modal__dialog-footer p-0">
                <button type="submit" class="oh-btn oh-btn--secondary oh-btn--shadow">
                    {% trans "Save" %}
                </button>
            </div>
        </div>
    </form>
</div>
