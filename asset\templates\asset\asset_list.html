{% load i18n %} {% load static %}
<script>
  $("#asset-count{{asset_category_id}}").html("{{assets.paginator.count}}");
  $("#asset-count{{asset_category_id}}").attr("title","{{assets.paginator.count}} {% trans 'Assets' %}");
</script>
<!-- start of messages -->
{% if messages %}
<div class="oh-wrapper">
  {% for message in messages %}
  <div class="oh-alert-container">
    <div class="oh-alert oh-alert--animated {{message.tags}}">
      {{ message }}
    </div>
  </div>
  {% endfor %}
</div>
{% endif %}
<!-- end of messages -->
<div
  class="oh-sticky-table oh-sticky-table--no-overflow mb-5"
  id="assetList{{asset_category.id}}"
>
  <div class="oh-sticky-table__table" data-count="{{total_count}}">
    <div class="oh-sticky-table__thead">
      <div class="oh-sticky-table__tr">
        <div class="oh-sticky-table__th">{% trans "Asset" %}</div>
        <div class="oh-sticky-table__th">{% trans "Status" %}</div>
        <div class="oh-sticky-table__th">{% trans "Tracking Id" %}</div>
        <div class="oh-sticky-table__th">{% trans "Batch No" %}</div>
        {% if perms.asset.change_asset or perms.asset.delete_asset %}
          <div class="oh-sticky-table__th">{% trans "Actions" %}</div>
        {% endif %}
      </div>
    </div>
    <div class="oh-sticky-table__tbody" id="assetPaginatorTarget">
      {% for asset in assets %}
        <div
          class="oh-sticky-table__tr"
          data-toggle="oh-modal-toggle"
          data-target="#objectDetailsModal"
          hx-get="{% url 'asset-information' asset_id=asset.id %}?{{pd}}&requests_ids={{requests_ids}}"
          hx-target="#objectDetailsModalTarget"
          id="assetDelete{{asset.id}}"
          draggable="true"
          >
          <div class="oh-sticky-table__sd">
            <div class="oh-profile oh-profile--md">
              <div class="oh-profile__avatar mr-1">
                <img
                  src="https://ui-avatars.com/api/?name={{asset.asset_name}}&background=random"
                  class="oh-profile__image"
                  alt="Mary Magdalene"
                />
              </div>
              <span class="oh-profile__name oh-text--dark"
                >{{asset.asset_name}}</span
              >
            </div>
          </div>
          <div class="oh-sticky-table__td">{% trans asset.asset_status %}</div>
          <div class="oh-sticky-table__td">{{asset.asset_tracking_id}}</div>
          <div class="oh-sticky-table__td">{{asset.asset_lot_number_id}}</div>
          {% if perms.asset.change_asset or perms.asset.delete_asset %}
            <div class="oh-sticky-table__td oh-sticky-table__right">
              <div class="oh-btn-group">
                {% if perms.asset.change_asset %}
                  {% if asset_under == 'asset_filter' %}
                    <a
                      class="oh-btn oh-btn--light-bkg w-100"
                      data-toggle="oh-modal-toggle"
                      data-target="#objectUpdateModal"
                      hx-get="{% url 'asset-update'  asset_id=asset.id %}?asset_under=asset_filter&{{request.GET.urlencode}}"
                      title="{% trans 'Update' %}"
                      hx-target="#objectUpdateModalTarget"
                      onclick="event.stopPropagation()"
                      id="oh-btn-asset-update-modal"
                    >
                      <ion-icon
                        name="create-outline"
                        role="img"
                        class="md hydrated"
                        aria-label="create outline"
                      ></ion-icon>
                    </a>
                    {% else %}
                    <a
                      class="oh-btn oh-btn--light-bkg w-100"
                      data-toggle="oh-modal-toggle"
                      data-target="#objectUpdateModal"
                      hx-get="{% url 'asset-update'  asset_id=asset.id %}?{{request.GET.urlencode}}"
                      title="{% trans 'Update' %}"
                      hx-target="#objectUpdateModalTarget"
                      onclick="event.stopPropagation()"
                      id="oh-btn-asset-update-modal"
                    >
                      <ion-icon
                        name="create-outline"
                        role="img"
                        class="md hydrated"
                        aria-label="create outline"
                      ></ion-icon>
                    </a>
                  {% endif %}
                {% endif %}
                {% if perms.asset.add_asset %}
                  <a
                    class="oh-btn oh-btn--light-bkg w-100"
                    data-toggle="oh-modal-toggle"
                    data-target="#objectCreateModal"
                    hx-get="{% url 'duplicate-asset' asset.id %}?{{request.GET.urlencode}}"
                    hx-target="#objectCreateModalTarget"
                    title="{% trans 'Duplicate' %}"
                    onclick="event.stopPropagation()"
                    style="cursor: pointer"
                  >
                    <ion-icon name="copy-outline"></ion-icon>
                  </a>
                {% endif %}
                {% if asset.assetassignment_set.all %}
                  <a
                    class="oh-btn oh-btn--light-bkg w-100"
                    onclick="event.preventDefault();event.stopPropagation()"
                    data-toggle="oh-modal-toggle"
                    data-target="#dynamicCreateModal"
                    hx-get="{% url 'add-asset-report' asset.id %}?asset_list=true"
                    hx-target="#dynamicCreateModalTarget"
                    id="oh-btn-asset-update-modal"
                    title="{% trans 'Asset Report' %}"
                  >
                    <ion-icon name="document-attach-outline"></ion-icon>
                  </a>
                {% else %}
                  <a
                    class="oh-btn oh-btn--light-bkg w-100 oh-btn--disabled"
                    onclick="event.stopPropagation()"
                    id="oh-btn-asset-update-modal"
                    title="{% trans 'Asset Report' %}"
                  >
                    <ion-icon name="document-attach-outline"></ion-icon>
                  </a>
                {% endif %}
                {% if perms.asset.delete_asset %}
                  {% if asset_under == 'asset_filter' %}
                    <form action="{% url 'asset-delete' asset.id %}?asset_list=asset_filter&{{request.GET.urlencode}}"
                      onsubmit="return confirm('{% trans "Do you want to delete this asset?" %}')"
                      method="post" style="width:100%">
                      {% csrf_token %}
                      <button
                        class="oh-btn oh-btn--danger-outline oh-btn--light-bkg w-100"
                        onclick="event.stopPropagation()"
                        title="{% trans 'Delete' %}"
                        >
                        <ion-icon
                          name="trash-outline"
                          role="img"
                          class="md hydrated"
                          aria-label="trash outline"
                        ></ion-icon>
                      </button>
                    </form>
                  {% else %}
                    <form hx-confirm="{% trans 'Do you want to delete this asset?' %}"
                        hx-post="{% url 'asset-delete' asset_id=asset.id %}?{{request.GET.urlencode}}"
                        hx-target="#assetCategory{{asset.asset_category_id.id}}"
                        onclick="event.stopPropagation()"
                        style="width:100%">
                      {% csrf_token %}
                      <button
                        class="oh-btn oh-btn--danger-outline oh-btn--light-bkg w-100"
                        title="{% trans 'Delete' %}"
                      >
                        <ion-icon
                          name="trash-outline"
                          role="img"
                          class="md hydrated"
                          aria-label="trash outline"
                        ></ion-icon>
                      </button>
                    </form>
                  {% endif %}
                {% endif %}
              </div>
            </div>
          {% endif %}
        </div>
      {% endfor %}
    </div>
  </div>
</div>

<!-- pagination start -->
<div class="oh-pagination">
  <span
    class="oh-pagination__page"
    data-toggle="modal"
    data-target="#addEmployeeModal"
  ></span>
  <nav class="oh-pagination__nav">
    <div class="oh-pagination__input-container me-3">
      <span class="oh-pagination__label me-1">{% trans "Page" %}</span>
      <input
        type="number"
        name="page"
        class="oh-pagination__input"
        value="{{assets.number }}"
        min="1"
        hx-get="{% url 'asset-list' cat_id=asset_category_id  %}?{{request.GET.urlencode}}"
        hx-target="#assetCategory{{asset_category_id}}"
      />
      <span class="oh-pagination__label"
        >{% trans "of" %} {{ assets.paginator.num_pages }}</span
      >
    </div>
    <ul class="oh-pagination__items">
      {% if assets.has_previous %}
      <li class="oh-pagination__item oh-pagination__item--wide">
        <a
          hx-get="{% url 'asset-list' cat_id=asset_category_id %}?{{request.GET.urlencode}}&page=1"
          class="oh-pagination__link"
          hx-target="#assetCategory{{asset_category_id}}"
          >{% trans "First" %}</a
        >
      </li>
      <li class="oh-pagination__item oh-pagination__item--wide">
        <a
          hx-get="{% url 'asset-list' cat_id=asset_category_id %}?{{request.GET.urlencode}}&page={{ assets.previous_page_number }}"
          class="oh-pagination__link"
          hx-target="#assetCategory{{asset_category_id}}"
          >{% trans "Previous" %}</a
        >
      </li>
      {%endif %} {% if assets.has_next %}
      <li class="oh-pagination__item oh-pagination__item--wide">
        <a
          hx-get="{% url 'asset-list' cat_id=asset_category_id %}?{{request.GET.urlencode}}&page={{ assets.next_page_number }}"
          class="btn btn-outline-secondary"
          hx-target="#assetCategory{{asset_category_id}}"
          >{% trans "Next" %}</a
        >
      </li>
      <li class="oh-pagination__item oh-pagination__item--wide">
        <a
          hx-get="{% url 'asset-list' cat_id=asset_category_id %}?{{request.GET.urlencode}}&page={{ assets.paginator.num_pages }}"
          hx-target="#assetCategory{{asset_category_id}}"
          class="oh-pagination__link"
          >{% trans "Last" %}</a
        >
      </li>
      {% endif %}
    </ul>
  </nav>
</div>

<!-- this tag is used to identify if asset is under category or filter -->
<p style="display: none" id="asset_under">{{asset_under}}</p>
<p style="display: none" id="asset-count-updated">{{ asset_count }}</p>
<p style="display: none" id="asset-category">{{ asset_category_id }}</p>

<!-- end of pagination -->
<script>
  $(document).ready(function () {
    // asset delete
    $(".asset-delete").on("click", function () {
      var assetCategoryId = $(this).attr("data-category-id").trim();
      setTimeout(function () {
        updateAssetCount(assetCategoryId);
      }, 1000);
    });

    // asset pagination target change
    var asset_under = $("#asset_under").text().trim();
    // if the asset is filter the hx-target will be changed
    if (asset_under === "asset_filter") {
      // the asset list is loaded in filter so the pagination target is
      $(".oh-pagination *[hx-target]").each(function () {
        $(this).attr("hx-target", "#assetCategoryList");
      });
    }

    // asset count update
    function updateAssetCount(assetCategoryId) {
      var csrf_token = $('input[name="csrfmiddlewaretoken"]').attr("value");
      $.ajax({
        type: "POST",
        url: "/asset/asset-count-update",
        data: {
          asset_category_id: assetCategoryId,
          csrfmiddlewaretoken: csrf_token,
        },
        dataType: "json",
        success: function (response) {
          $(`#asset-count${assetCategoryId}`).text(response);
        },
      });
    }
  });
</script>
