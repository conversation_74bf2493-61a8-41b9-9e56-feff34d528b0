{% load i18n %}
<div class="oh-sticky-table">
	<div class="oh-sticky-table__table oh-table--sortable">
		<div class="oh-sticky-table__thead">
			<div class="oh-sticky-table__tr">
				<div class="oh-sticky-table__th">{% trans "Employee Type" %}</div>
				{% if perms.base.change_employeetype or perms.base.delete_employeetype %}
					<div class="oh-sticky-table__th">{% trans "Actions" %}</div>
				{% endif %}
			</div>
		</div>
		<div class="oh-sticky-table__tbody">
			{% for type in employee_types %}
				<div class="oh-sticky-table__tr" draggable="true" id="employeeTypeTr{{type.id}}">
					<div class="oh-sticky-table__td">{{type}}</div>
					{% if perms.base.change_employeetype or perms.base.delete_employeetype %}
						<div class="oh-sticky-table__td">
							<div class="oh-btn-group">
								{% if perms.base.change_employeetype %}
									<a data-toggle="oh-modal-toggle" data-target="#employeeTypeModal"
										hx-get="{% url 'employee-type-update' type.id %}" hx-target="#employeeTypeForm"
										type="button" class="oh-btn oh-btn--light-bkg w-50" title="{% trans 'Edit' %}">
										<ion-icon name="create-outline"></ion-icon></a>
								{% endif %}
								{% if perms.base.delete_employeetype %}
									<form hx-confirm="{% trans 'Are you sure you want to delete this employee type?' %}"
										hx-post="{% url 'employee-type-delete' type.id %}" hx-target="#employeeTypeTr{{type.id}}"
										hx-swap="outerHTML" class="w-50" hx-on-htmx-after-request="reloadMessage(this);">
										{% csrf_token %}
										<button type="submit" class="oh-btn oh-btn--danger-outline oh-btn--light-bkg w-100" title="{% trans 'Remove' %}">
											<ion-icon name="trash-outline"></ion-icon>
										</button>
									</form>
								{% endif %}
							</div>
						</div>
					{% endif %}
				</div>
			{% endfor %}
		</div>
	</div>
</div>
