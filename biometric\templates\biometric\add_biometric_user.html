{% load i18n %}
<div class="oh-modal__dialog-header pb-0">
    <span class="oh-modal__dialog-title" id="biometricEmployeesLavel">
        {% trans "Add Employee" %}
    </span>
    <button class="oh-modal__close" aria-label="Close">
        <ion-icon name="close-outline"></ion-icon>
    </button>
</div>
<div class="oh-modal__dialog-body" id="biometricEmployeesModalBody">
    <form hx-post="{%url 'add-biometric-user' device_id%}" id="biometricEmployeesForm" class="oh-profile-section pt-0">
        {% csrf_token %}
        <div class="col-sm-12 col-md-12 col-lg-12">
            <div class="oh-input-group">
                <label class="oh-label" for="{{form.employee_ids.id_for_label}}">{% trans "Employees" %}</label>
                {{form.employee_ids}}
            </div>
        </div>
        <div class="oh-modal__dialog-footer p-0 pt-3">
            <button type="submit" class="oh-btn oh-btn--secondary oh-btn--shadow">
                {% trans "Add" %}
            </button>
        </div>
    </form>
</div>
<script>
    $(document).ready(function () {
        $("#biometricEmployeesForm").submit(function (event) {
            $("#BiometricDeviceTestModal").toggleClass("oh-modal--show");
        });
    });
</script>
