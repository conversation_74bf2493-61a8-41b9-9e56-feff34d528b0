{% load i18n %} {% load basefilters %}
<div class="oh-modal__dialog-header pb-0">
    <h2 class="oh-modal__dialog-title" id="restrictIpLabel">
      {% trans "Edit Allowed IPs" %}
    </h2>
    <button class="oh-modal__close" aria-label="Close">
      <ion-icon name="close-outline" role="img" class="md hydrated" aria-label="close outline"></ion-icon>
    </button>
</div>
<div class="oh-modal__dialog-body">
    <form class="oh-profile-section" hx-post="{% url 'edit-allowed-ip' %}?id={{id}}" hx-target="#objectDetailsModalW25Target">
        {{form.as_p}}
        <div class="oh-modal__dialog-footer p-0 mt-3">
            <button type="submit" class="oh-btn oh-btn--secondary oh-btn--shadow" >
                {% trans "Save" %}
            </button>
        </div>
    </form>
</div>
