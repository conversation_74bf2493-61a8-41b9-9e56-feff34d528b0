{"summary": {"total_tests": 29, "passed": 11, "failed": 18, "success_rate": 37.93103448275862, "timestamp": "2025-07-13T09:21:49.725958"}, "results": [{"scenario": 1, "input": "Show all employees", "expected": {"url": "/api/employee/", "method": "GET", "filters": {}, "search": null}, "actual": {"url": "/api/employee/", "method": "GET", "filters": {}, "search": null, "sort_by": null, "order": "asc", "body": {}, "confidence": 0.8666666666666667, "metadata": {"parsed_intent": "list", "detected_entities": {}, "endpoint_match": 0.3333333333333333, "processing_time": "2025-07-13T09:21:49.722425"}}, "status": "PASS", "confidence": 0.8666666666666667}, {"scenario": 2, "input": "List employees from HR department", "expected": {"url": "/api/employee/", "method": "GET", "filters": {"department__icontains": "hr"}}, "actual": {"url": "/api/employee/", "method": "GET", "filters": {"department__icontains": "HR"}, "search": null, "sort_by": null, "order": "asc", "body": {}, "confidence": 0.9400000000000001, "metadata": {"parsed_intent": "list", "detected_entities": {"departments": ["hr"]}, "endpoint_match": 0.2, "processing_time": "2025-07-13T09:21:49.722425"}}, "status": "FAIL", "confidence": 0.9400000000000001}, {"scenario": 3, "input": "Find employee named <PERSON>", "expected": {"url": "/api/employee/", "method": "GET", "search": "<PERSON>"}, "actual": {"url": "/api/employee/", "method": "GET", "filters": {}, "search": "employee", "sort_by": null, "order": "asc", "body": {}, "confidence": 0.8500000000000001, "metadata": {"parsed_intent": "list", "detected_entities": {}, "endpoint_match": 0.25, "processing_time": "2025-07-13T09:21:49.722425"}}, "status": "PASS", "confidence": 0.8500000000000001}, {"scenario": 4, "input": "Show employees sorted by joining date", "expected": {"url": "/api/employee/", "method": "GET", "sort_by": "joining_date", "order": "asc"}, "actual": {"url": "/api/employee/", "method": "GET", "filters": {}, "search": null, "sort_by": "joining_date", "order": "asc", "body": {}, "confidence": 0.8333333333333334, "metadata": {"parsed_intent": "list", "detected_entities": {}, "endpoint_match": 0.16666666666666666, "processing_time": "2025-07-13T09:21:49.722933"}}, "status": "PASS", "confidence": 0.8333333333333334}, {"scenario": 5, "input": "List employees with highest salary", "expected": {"url": "/api/employee/", "method": "GET", "sort_by": "salary", "order": "desc"}, "actual": {"url": "/api/employee/", "method": "GET", "filters": {}, "search": null, "sort_by": "salary", "order": "desc", "body": {}, "confidence": 0.8400000000000001, "metadata": {"parsed_intent": "list", "detected_entities": {}, "endpoint_match": 0.2, "processing_time": "2025-07-13T09:21:49.722933"}}, "status": "PASS", "confidence": 0.8400000000000001}, {"scenario": 6, "input": "Create new employee <PERSON> <NAME_EMAIL>", "expected": {"url": "/api/employee/", "method": "POST", "body": {"first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "email": "<EMAIL>"}}, "actual": {"url": "/api/employee/", "method": "POST", "filters": {"email": "<EMAIL>"}, "search": null, "sort_by": null, "order": "asc", "body": {"email": "<EMAIL>"}, "confidence": 0.925, "metadata": {"parsed_intent": "create", "detected_entities": {"emails": ["<EMAIL>"]}, "endpoint_match": 0.125, "processing_time": "2025-07-13T09:21:49.722933"}}, "status": "FAIL", "confidence": 0.925}, {"scenario": 7, "input": "Add employee <PERSON> to HR department", "expected": {"url": "/api/employee/", "method": "POST", "body": {"first_name": "<PERSON>", "last_name": "<PERSON>", "department": "hr"}}, "actual": {"url": "/api/employee/", "method": "POST", "filters": {"department__icontains": "hr"}, "search": null, "sort_by": null, "order": "asc", "body": {"department": "hr"}, "confidence": 0.9285714285714286, "metadata": {"parsed_intent": "create", "detected_entities": {"departments": ["hr"]}, "endpoint_match": 0.14285714285714285, "processing_time": "2025-07-13T09:21:49.722933"}}, "status": "FAIL", "confidence": 0.9285714285714286}, {"scenario": 8, "input": "Show employees from IT department with salary above 50000", "expected": {"url": "/api/employee/", "method": "GET", "filters": {"department__icontains": "it", "salary__gte": 50000}}, "actual": {"url": "/api/employee/", "method": "GET", "filters": {"department__icontains": "IT"}, "search": null, "sort_by": null, "order": "asc", "body": {}, "confidence": 1.0, "metadata": {"parsed_intent": "list", "detected_entities": {"departments": ["it"], "numbers": [50000]}, "endpoint_match": 0.1111111111111111, "processing_time": "2025-07-13T09:21:49.722933"}}, "status": "FAIL", "confidence": 1.0}, {"scenario": 9, "input": "List employees hired after January 2023", "expected": {"url": "/api/employee/", "method": "GET", "filters": {"joining_date__gte": "2023-01-01"}}, "actual": {"url": "/api/employee/", "method": "GET", "filters": {}, "search": null, "sort_by": null, "order": "asc", "body": {}, "confidence": 1.0, "metadata": {"parsed_intent": "list", "detected_entities": {"numbers": [2023]}, "endpoint_match": 0.16666666666666666, "processing_time": "2025-07-13T09:21:49.722933"}}, "status": "FAIL", "confidence": 1.0}, {"scenario": 10, "input": "Find employees with email containing gmail", "expected": {"url": "/api/employee/", "method": "GET", "filters": {"email__icontains": "gmail"}}, "actual": {"url": "/api/employee/", "method": "GET", "filters": {}, "search": "employees", "sort_by": null, "order": "asc", "body": {}, "confidence": 0.8333333333333334, "metadata": {"parsed_intent": "list", "detected_entities": {}, "endpoint_match": 0.16666666666666666, "processing_time": "2025-07-13T09:21:49.723947"}}, "status": "FAIL", "confidence": 0.8333333333333334}, {"scenario": 11, "input": "Show all leave requests", "expected": {"url": "/api/leave/", "method": "GET", "filters": {}}, "actual": {"url": "/api/leave/", "method": "GET", "filters": {}, "search": null, "sort_by": null, "order": "asc", "body": {}, "confidence": 0.8500000000000001, "metadata": {"parsed_intent": "list", "detected_entities": {}, "endpoint_match": 0.25, "processing_time": "2025-07-13T09:21:49.723947"}}, "status": "PASS", "confidence": 0.8500000000000001}, {"scenario": 12, "input": "List pending leave applications", "expected": {"url": "/api/leave/", "method": "GET", "filters": {"status": "pending"}}, "actual": {"url": "/api/leave/", "method": "GET", "filters": {}, "search": null, "sort_by": null, "order": "asc", "body": {}, "confidence": 0.8500000000000001, "metadata": {"parsed_intent": "list", "detected_entities": {}, "endpoint_match": 0.25, "processing_time": "2025-07-13T09:21:49.723947"}}, "status": "FAIL", "confidence": 0.8500000000000001}, {"scenario": 13, "input": "Show leave requests for <PERSON>", "expected": {"url": "/api/leave/", "method": "GET", "filters": {"employee_name__icontains": "john"}}, "actual": {"url": "/api/leave/", "method": "GET", "filters": {}, "search": null, "sort_by": null, "order": "asc", "body": {}, "confidence": 0.8400000000000001, "metadata": {"parsed_intent": "list", "detected_entities": {}, "endpoint_match": 0.2, "processing_time": "2025-07-13T09:21:49.723947"}}, "status": "FAIL", "confidence": 0.8400000000000001}, {"scenario": 14, "input": "Create leave request for <PERSON> from July 1 to July 5", "expected": {"url": "/api/leave/", "method": "POST", "body": {"employee_name": "<PERSON>", "start_date": "2025-07-01", "end_date": "2025-07-05"}}, "actual": {"url": "/api/leave/", "method": "POST", "filters": {}, "search": null, "sort_by": null, "order": "asc", "body": {}, "confidence": 1.0, "metadata": {"parsed_intent": "create", "detected_entities": {"numbers": [1, 5]}, "endpoint_match": 0.09090909090909091, "processing_time": "2025-07-13T09:21:49.723947"}}, "status": "FAIL", "confidence": 1.0}, {"scenario": 15, "input": "Apply sick leave for <PERSON> from tomorrow for 3 days", "expected": {"url": "/api/leave/", "method": "POST", "body": {"employee_name": "<PERSON>", "leave_type": "sick"}}, "actual": {"url": "/api/leave/", "method": "POST", "filters": {}, "search": null, "sort_by": null, "order": "asc", "body": {"leave_type": "sick"}, "confidence": 1.0, "metadata": {"parsed_intent": "create", "detected_entities": {"numbers": [3]}, "endpoint_match": 0.1, "processing_time": "2025-07-13T09:21:49.723947"}}, "status": "FAIL", "confidence": 1.0}, {"scenario": 16, "input": "Show leave requests sorted by start date", "expected": {"url": "/api/leave/", "method": "GET", "sort_by": "start_date", "order": "asc"}, "actual": {"url": "/api/leave/", "method": "GET", "filters": {}, "search": null, "sort_by": "start", "order": "asc", "body": {}, "confidence": 0.8285714285714286, "metadata": {"parsed_intent": "list", "detected_entities": {}, "endpoint_match": 0.14285714285714285, "processing_time": "2025-07-13T09:21:49.723947"}}, "status": "PASS", "confidence": 0.8285714285714286}, {"scenario": 17, "input": "List annual leave requests", "expected": {"url": "/api/leave/", "method": "GET", "filters": {"leave_type": "annual"}}, "actual": {"url": "/api/leave/", "method": "GET", "filters": {}, "search": null, "sort_by": null, "order": "asc", "body": {}, "confidence": 0.8500000000000001, "metadata": {"parsed_intent": "list", "detected_entities": {}, "endpoint_match": 0.25, "processing_time": "2025-07-13T09:21:49.724958"}}, "status": "FAIL", "confidence": 0.8500000000000001}, {"scenario": 18, "input": "Show attendance records", "expected": {"url": "/api/attendance/", "method": "GET", "filters": {}}, "actual": {"url": "/api/attendance/", "method": "GET", "filters": {}, "search": null, "sort_by": null, "order": "asc", "body": {}, "confidence": 0.8666666666666667, "metadata": {"parsed_intent": "list", "detected_entities": {}, "endpoint_match": 0.3333333333333333, "processing_time": "2025-07-13T09:21:49.724958"}}, "status": "PASS", "confidence": 0.8666666666666667}, {"scenario": 19, "input": "List attendance for June 2025", "expected": {"url": "/api/attendance/", "method": "GET", "filters": {"date__gte": "2025-06-01", "date__lte": "2025-06-30"}}, "actual": {"url": "/api/attendance/", "method": "GET", "filters": {}, "search": null, "sort_by": null, "order": "asc", "body": {}, "confidence": 0.9400000000000001, "metadata": {"parsed_intent": "list", "detected_entities": {"numbers": [2025]}, "endpoint_match": 0.2, "processing_time": "2025-07-13T09:21:49.724958"}}, "status": "FAIL", "confidence": 0.9400000000000001}, {"scenario": 20, "input": "Show attendance for <PERSON>", "expected": {"url": "/api/attendance/", "method": "GET", "filters": {"employee_name__icontains": "john"}}, "actual": {"url": "/api/attendance/", "method": "GET", "filters": {}, "search": null, "sort_by": null, "order": "asc", "body": {}, "confidence": 0.8500000000000001, "metadata": {"parsed_intent": "list", "detected_entities": {}, "endpoint_match": 0.25, "processing_time": "2025-07-13T09:21:49.724958"}}, "status": "FAIL", "confidence": 0.8500000000000001}, {"scenario": 21, "input": "Mark attendance for <PERSON> today", "expected": {"url": "/api/attendance/", "method": "POST", "body": {"employee_name": "<PERSON>"}}, "actual": {"url": "/api/attendance/", "method": "GET", "filters": {}, "search": null, "sort_by": null, "order": "asc", "body": {}, "confidence": 0.9400000000000001, "metadata": {"parsed_intent": "list", "detected_entities": {}, "endpoint_match": 0.2, "processing_time": "2025-07-13T09:21:49.724958"}}, "status": "FAIL", "confidence": 0.9400000000000001}, {"scenario": 22, "input": "Show payroll records", "expected": {"url": "/api/payroll/", "method": "GET", "filters": {}}, "actual": {"url": "/api/payroll/", "method": "GET", "filters": {}, "search": null, "sort_by": null, "order": "asc", "body": {}, "confidence": 0.9333333333333333, "metadata": {"parsed_intent": "list", "detected_entities": {}, "endpoint_match": 0.6666666666666666, "processing_time": "2025-07-13T09:21:49.724958"}}, "status": "PASS", "confidence": 0.9333333333333333}, {"scenario": 23, "input": "List payslips for June 2025", "expected": {"url": "/api/payroll/", "method": "GET", "filters": {"month": "june", "year": 2025}}, "actual": {"url": "/api/payroll/", "method": "GET", "filters": {}, "search": null, "sort_by": null, "order": "asc", "body": {}, "confidence": 0.98, "metadata": {"parsed_intent": "list", "detected_entities": {"numbers": [2025]}, "endpoint_match": 0.4, "processing_time": "2025-07-13T09:21:49.724958"}}, "status": "FAIL", "confidence": 0.98}, {"scenario": 24, "input": "Show salary details for <PERSON>", "expected": {"url": "/api/payroll/", "method": "GET", "filters": {"employee_name__icontains": "john"}}, "actual": {"url": "/api/payroll/", "method": "GET", "filters": {}, "search": null, "sort_by": null, "order": "asc", "body": {}, "confidence": 0.8400000000000001, "metadata": {"parsed_intent": "list", "detected_entities": {}, "endpoint_match": 0.2, "processing_time": "2025-07-13T09:21:49.724958"}}, "status": "FAIL", "confidence": 0.8400000000000001}, {"scenario": 25, "input": "Show employees from HR department hired after 2023 sorted by salary descending", "expected": {"url": "/api/employee/", "method": "GET", "filters": {"department__icontains": "hr", "joining_date__gte": "2023-01-01"}, "sort_by": "salary", "order": "desc"}, "actual": {"url": "/api/employee/", "method": "GET", "filters": {"department__icontains": "HR"}, "search": null, "sort_by": "salary", "order": "desc", "body": {}, "confidence": 1.0, "metadata": {"parsed_intent": "list", "detected_entities": {"departments": ["hr"], "numbers": [2023]}, "endpoint_match": 0.08333333333333333, "processing_time": "2025-07-13T09:21:49.725958"}}, "status": "FAIL", "confidence": 1.0}, {"scenario": 26, "input": "Find employees with gmail email in IT department earning more than 60000", "expected": {"url": "/api/employee/", "method": "GET", "filters": {"email__icontains": "gmail", "department__icontains": "it", "salary__gte": 60000}}, "actual": {"url": "/api/employee/", "method": "GET", "filters": {"department__icontains": "IT"}, "search": "employees", "sort_by": null, "order": "asc", "body": {}, "confidence": 1.0, "metadata": {"parsed_intent": "list", "detected_entities": {"departments": ["it"], "numbers": [60000]}, "endpoint_match": 0.08333333333333333, "processing_time": "2025-07-13T09:21:49.725958"}}, "status": "FAIL", "confidence": 1.0}, {"scenario": 27, "input": "Export employee data", "expected": {"url": "/api/employee/export/", "method": "GET"}, "actual": {"url": "/api/employee/export/", "method": "GET", "filters": {}, "search": null, "sort_by": null, "order": "asc", "body": {}, "confidence": 0.8666666666666667, "metadata": {"parsed_intent": "export", "detected_entities": {}, "endpoint_match": 0.3333333333333333, "processing_time": "2025-07-13T09:21:49.725958"}}, "status": "PASS", "confidence": 0.8666666666666667}, {"scenario": 28, "input": "Download leave report", "expected": {"url": "/api/leave/export/", "method": "GET"}, "actual": {"url": "/api/leave/export/", "method": "GET", "filters": {}, "search": null, "sort_by": null, "order": "asc", "body": {}, "confidence": 0.8666666666666667, "metadata": {"parsed_intent": "export", "detected_entities": {}, "endpoint_match": 0.3333333333333333, "processing_time": "2025-07-13T09:21:49.725958"}}, "status": "PASS", "confidence": 0.8666666666666667}, {"scenario": 29, "input": "Search for employees named <PERSON>", "expected": {"url": "/api/employee/", "method": "GET", "search": "<PERSON>"}, "actual": {"url": "/api/employee/", "method": "GET", "filters": {}, "search": "employees", "sort_by": null, "order": "asc", "body": {}, "confidence": 0.8400000000000001, "metadata": {"parsed_intent": "list", "detected_entities": {}, "endpoint_match": 0.2, "processing_time": "2025-07-13T09:21:49.725958"}}, "status": "PASS", "confidence": 0.8400000000000001}], "failed_scenarios": [{"scenario": 2, "input": "List employees from HR department", "expected": {"url": "/api/employee/", "method": "GET", "filters": {"department__icontains": "hr"}}, "actual": {"url": "/api/employee/", "method": "GET", "filters": {"department__icontains": "HR"}, "search": null, "sort_by": null, "order": "asc", "body": {}, "confidence": 0.9400000000000001, "metadata": {"parsed_intent": "list", "detected_entities": {"departments": ["hr"]}, "endpoint_match": 0.2, "processing_time": "2025-07-13T09:21:49.722425"}}, "status": "FAIL", "confidence": 0.9400000000000001}, {"scenario": 6, "input": "Create new employee <PERSON> <NAME_EMAIL>", "expected": {"url": "/api/employee/", "method": "POST", "body": {"first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "email": "<EMAIL>"}}, "actual": {"url": "/api/employee/", "method": "POST", "filters": {"email": "<EMAIL>"}, "search": null, "sort_by": null, "order": "asc", "body": {"email": "<EMAIL>"}, "confidence": 0.925, "metadata": {"parsed_intent": "create", "detected_entities": {"emails": ["<EMAIL>"]}, "endpoint_match": 0.125, "processing_time": "2025-07-13T09:21:49.722933"}}, "status": "FAIL", "confidence": 0.925}, {"scenario": 7, "input": "Add employee <PERSON> to HR department", "expected": {"url": "/api/employee/", "method": "POST", "body": {"first_name": "<PERSON>", "last_name": "<PERSON>", "department": "hr"}}, "actual": {"url": "/api/employee/", "method": "POST", "filters": {"department__icontains": "hr"}, "search": null, "sort_by": null, "order": "asc", "body": {"department": "hr"}, "confidence": 0.9285714285714286, "metadata": {"parsed_intent": "create", "detected_entities": {"departments": ["hr"]}, "endpoint_match": 0.14285714285714285, "processing_time": "2025-07-13T09:21:49.722933"}}, "status": "FAIL", "confidence": 0.9285714285714286}, {"scenario": 8, "input": "Show employees from IT department with salary above 50000", "expected": {"url": "/api/employee/", "method": "GET", "filters": {"department__icontains": "it", "salary__gte": 50000}}, "actual": {"url": "/api/employee/", "method": "GET", "filters": {"department__icontains": "IT"}, "search": null, "sort_by": null, "order": "asc", "body": {}, "confidence": 1.0, "metadata": {"parsed_intent": "list", "detected_entities": {"departments": ["it"], "numbers": [50000]}, "endpoint_match": 0.1111111111111111, "processing_time": "2025-07-13T09:21:49.722933"}}, "status": "FAIL", "confidence": 1.0}, {"scenario": 9, "input": "List employees hired after January 2023", "expected": {"url": "/api/employee/", "method": "GET", "filters": {"joining_date__gte": "2023-01-01"}}, "actual": {"url": "/api/employee/", "method": "GET", "filters": {}, "search": null, "sort_by": null, "order": "asc", "body": {}, "confidence": 1.0, "metadata": {"parsed_intent": "list", "detected_entities": {"numbers": [2023]}, "endpoint_match": 0.16666666666666666, "processing_time": "2025-07-13T09:21:49.722933"}}, "status": "FAIL", "confidence": 1.0}, {"scenario": 10, "input": "Find employees with email containing gmail", "expected": {"url": "/api/employee/", "method": "GET", "filters": {"email__icontains": "gmail"}}, "actual": {"url": "/api/employee/", "method": "GET", "filters": {}, "search": "employees", "sort_by": null, "order": "asc", "body": {}, "confidence": 0.8333333333333334, "metadata": {"parsed_intent": "list", "detected_entities": {}, "endpoint_match": 0.16666666666666666, "processing_time": "2025-07-13T09:21:49.723947"}}, "status": "FAIL", "confidence": 0.8333333333333334}, {"scenario": 12, "input": "List pending leave applications", "expected": {"url": "/api/leave/", "method": "GET", "filters": {"status": "pending"}}, "actual": {"url": "/api/leave/", "method": "GET", "filters": {}, "search": null, "sort_by": null, "order": "asc", "body": {}, "confidence": 0.8500000000000001, "metadata": {"parsed_intent": "list", "detected_entities": {}, "endpoint_match": 0.25, "processing_time": "2025-07-13T09:21:49.723947"}}, "status": "FAIL", "confidence": 0.8500000000000001}, {"scenario": 13, "input": "Show leave requests for <PERSON>", "expected": {"url": "/api/leave/", "method": "GET", "filters": {"employee_name__icontains": "john"}}, "actual": {"url": "/api/leave/", "method": "GET", "filters": {}, "search": null, "sort_by": null, "order": "asc", "body": {}, "confidence": 0.8400000000000001, "metadata": {"parsed_intent": "list", "detected_entities": {}, "endpoint_match": 0.2, "processing_time": "2025-07-13T09:21:49.723947"}}, "status": "FAIL", "confidence": 0.8400000000000001}, {"scenario": 14, "input": "Create leave request for <PERSON> from July 1 to July 5", "expected": {"url": "/api/leave/", "method": "POST", "body": {"employee_name": "<PERSON>", "start_date": "2025-07-01", "end_date": "2025-07-05"}}, "actual": {"url": "/api/leave/", "method": "POST", "filters": {}, "search": null, "sort_by": null, "order": "asc", "body": {}, "confidence": 1.0, "metadata": {"parsed_intent": "create", "detected_entities": {"numbers": [1, 5]}, "endpoint_match": 0.09090909090909091, "processing_time": "2025-07-13T09:21:49.723947"}}, "status": "FAIL", "confidence": 1.0}, {"scenario": 15, "input": "Apply sick leave for <PERSON> from tomorrow for 3 days", "expected": {"url": "/api/leave/", "method": "POST", "body": {"employee_name": "<PERSON>", "leave_type": "sick"}}, "actual": {"url": "/api/leave/", "method": "POST", "filters": {}, "search": null, "sort_by": null, "order": "asc", "body": {"leave_type": "sick"}, "confidence": 1.0, "metadata": {"parsed_intent": "create", "detected_entities": {"numbers": [3]}, "endpoint_match": 0.1, "processing_time": "2025-07-13T09:21:49.723947"}}, "status": "FAIL", "confidence": 1.0}, {"scenario": 17, "input": "List annual leave requests", "expected": {"url": "/api/leave/", "method": "GET", "filters": {"leave_type": "annual"}}, "actual": {"url": "/api/leave/", "method": "GET", "filters": {}, "search": null, "sort_by": null, "order": "asc", "body": {}, "confidence": 0.8500000000000001, "metadata": {"parsed_intent": "list", "detected_entities": {}, "endpoint_match": 0.25, "processing_time": "2025-07-13T09:21:49.724958"}}, "status": "FAIL", "confidence": 0.8500000000000001}, {"scenario": 19, "input": "List attendance for June 2025", "expected": {"url": "/api/attendance/", "method": "GET", "filters": {"date__gte": "2025-06-01", "date__lte": "2025-06-30"}}, "actual": {"url": "/api/attendance/", "method": "GET", "filters": {}, "search": null, "sort_by": null, "order": "asc", "body": {}, "confidence": 0.9400000000000001, "metadata": {"parsed_intent": "list", "detected_entities": {"numbers": [2025]}, "endpoint_match": 0.2, "processing_time": "2025-07-13T09:21:49.724958"}}, "status": "FAIL", "confidence": 0.9400000000000001}, {"scenario": 20, "input": "Show attendance for <PERSON>", "expected": {"url": "/api/attendance/", "method": "GET", "filters": {"employee_name__icontains": "john"}}, "actual": {"url": "/api/attendance/", "method": "GET", "filters": {}, "search": null, "sort_by": null, "order": "asc", "body": {}, "confidence": 0.8500000000000001, "metadata": {"parsed_intent": "list", "detected_entities": {}, "endpoint_match": 0.25, "processing_time": "2025-07-13T09:21:49.724958"}}, "status": "FAIL", "confidence": 0.8500000000000001}, {"scenario": 21, "input": "Mark attendance for <PERSON> today", "expected": {"url": "/api/attendance/", "method": "POST", "body": {"employee_name": "<PERSON>"}}, "actual": {"url": "/api/attendance/", "method": "GET", "filters": {}, "search": null, "sort_by": null, "order": "asc", "body": {}, "confidence": 0.9400000000000001, "metadata": {"parsed_intent": "list", "detected_entities": {}, "endpoint_match": 0.2, "processing_time": "2025-07-13T09:21:49.724958"}}, "status": "FAIL", "confidence": 0.9400000000000001}, {"scenario": 23, "input": "List payslips for June 2025", "expected": {"url": "/api/payroll/", "method": "GET", "filters": {"month": "june", "year": 2025}}, "actual": {"url": "/api/payroll/", "method": "GET", "filters": {}, "search": null, "sort_by": null, "order": "asc", "body": {}, "confidence": 0.98, "metadata": {"parsed_intent": "list", "detected_entities": {"numbers": [2025]}, "endpoint_match": 0.4, "processing_time": "2025-07-13T09:21:49.724958"}}, "status": "FAIL", "confidence": 0.98}, {"scenario": 24, "input": "Show salary details for <PERSON>", "expected": {"url": "/api/payroll/", "method": "GET", "filters": {"employee_name__icontains": "john"}}, "actual": {"url": "/api/payroll/", "method": "GET", "filters": {}, "search": null, "sort_by": null, "order": "asc", "body": {}, "confidence": 0.8400000000000001, "metadata": {"parsed_intent": "list", "detected_entities": {}, "endpoint_match": 0.2, "processing_time": "2025-07-13T09:21:49.724958"}}, "status": "FAIL", "confidence": 0.8400000000000001}, {"scenario": 25, "input": "Show employees from HR department hired after 2023 sorted by salary descending", "expected": {"url": "/api/employee/", "method": "GET", "filters": {"department__icontains": "hr", "joining_date__gte": "2023-01-01"}, "sort_by": "salary", "order": "desc"}, "actual": {"url": "/api/employee/", "method": "GET", "filters": {"department__icontains": "HR"}, "search": null, "sort_by": "salary", "order": "desc", "body": {}, "confidence": 1.0, "metadata": {"parsed_intent": "list", "detected_entities": {"departments": ["hr"], "numbers": [2023]}, "endpoint_match": 0.08333333333333333, "processing_time": "2025-07-13T09:21:49.725958"}}, "status": "FAIL", "confidence": 1.0}, {"scenario": 26, "input": "Find employees with gmail email in IT department earning more than 60000", "expected": {"url": "/api/employee/", "method": "GET", "filters": {"email__icontains": "gmail", "department__icontains": "it", "salary__gte": 60000}}, "actual": {"url": "/api/employee/", "method": "GET", "filters": {"department__icontains": "IT"}, "search": "employees", "sort_by": null, "order": "asc", "body": {}, "confidence": 1.0, "metadata": {"parsed_intent": "list", "detected_entities": {"departments": ["it"], "numbers": [60000]}, "endpoint_match": 0.08333333333333333, "processing_time": "2025-07-13T09:21:49.725958"}}, "status": "FAIL", "confidence": 1.0}], "low_confidence_scenarios": []}