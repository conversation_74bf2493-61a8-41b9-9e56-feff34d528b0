{% load i18n %}
<div class="oh-modal__dialog-header pb-0">
  <h2 class="oh-modal__dialog-title" id="createModalTitle">
    {% if tag_id %}
        {% trans "History Tag Update" %}
    {% else %}
        {% trans "Create History Tag" %}
    {% endif %}
  </h2>
  <button class="oh-modal__close" aria-label="Close">
    <ion-icon name="close-outline"></ion-icon>
  </button>
</div>
<div class="oh-modal__dialog-body">
  {% if form.errors %}
  <!-- form errors  -->
  <div class="oh-wrapper">
    <div class="oh-alert-container">
      {% for error in form.non_field_errors %}
      <div class="oh-alert oh-alert--animated oh-alert--danger">
        {{ error }}
      </div>
      {% endfor %}
    </div>
  </div>
  {% endif %}
  <form
    {% if tag_id %}
        hx-post="{% url 'audit-tag-update' tag_id %}"
        hx-target="#audittagEditForm"
    {% else %}
        hx-post="{% url 'audit-tag-create' %}"
        hx-target="#audittagForm"
    {% endif %}
    hx-encoding="multipart/form-data"
    class="oh-profile-section"
  >
    {% csrf_token %}
    <div class="oh-input__group ">
        <label class="oh-input__label" for="{{form.title.id_for_label}}">{% trans "Title" %}</label>
        {{form.title}}
        {{form.title.errors}}
    </div>
    <div class="oh-input__group ">
        <label class="oh-input__label" for="{{form.highlight.id_for_label}}">{% trans "Highlight" %}</label>
        <div class="oh-switch">
            {{form.highlight}}
            {{form.highlight.errors}}
        </div>
    </div>
    <div class="oh-modal__dialog-footer p-0 mt-3">
        <button type="submit" class="oh-btn oh-btn--secondary oh-btn--shadow" >
            {% trans "Save" %}
        </button>
    </div>
  </form>
</div>
