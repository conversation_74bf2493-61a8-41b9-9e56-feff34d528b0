{% load eaglorafilters %} {% load basefilters %} {% load static %}
{% load i18n %} {% include 'filter_tags.html' %}
{% if messages %}
<div class="oh-wrapper">
  {% for message in messages %}
  <div class="oh-alert-container">
    <div class="oh-alert oh-alert--animated {{message.tags}}">
      {{ message }}
    </div>
  </div>
  {% endfor %}
</div>
{% endif %}
<div class="oh-wrapper">
  {% if perms.base.view_shiftrequest and rshift_assign %}
    <div
    class="oh-checkpoint-badge text-success mb-2"
    id="selectAllRShifts"
    style="cursor: pointer"
    >
    {% trans "Select All Shifts" %}
    </div>
    <div
    class="oh-checkpoint-badge text-secondary mb-2"
    id="unselectAllRShifts"
    style="cursor: pointer; display: none;"
    >
    {% trans "Unselect All Shifts" %}
    </div>
  {% endif %}
  <div
  class="oh-checkpoint-badge text-info mb-2"
  id="exportRShifts"
  style="cursor: pointer; display: none;"
  >
  {% trans "Export Shifts" %}
  </div>
  <div class="oh-checkpoint-badge text-danger mb-2" id="selectedShowRShifts" style="display: none"></div>
</div>
{% if rshift_assign %}
<div class="oh-card oh-wrapper">
    {% for rshift_request_list in rshift_assign %}
    <div class="oh-accordion-meta">
		<div class="oh-accordion-meta__item">
			<div class="oh-accordion-meta__header" onclick='$(this).toggleClass("oh-accordion-meta__header--show");'>
				<span class="oh-accordion-meta__title pt-3 pb-3">
					<div class="oh-tabs__input-badge-container">
						<span
							class="oh-badge oh-badge--secondary oh-badge--small oh-badge--round mr-1"
						>
							{{rshift_request_list.list.paginator.count}}
						</span>
						{{rshift_request_list.grouper}}
					</div>
				</span>
			</div>
			<div class="oh-accordion-meta__body d-none">
				<div class="oh-sticky-table oh-sticky-table--no-overflow mb-5">
          <div class="oh-sticky-table">
              <div class="oh-sticky-table__table oh-table--sortable">
                  <div class="oh-sticky-table__thead">
                      <div class="oh-sticky-table__tr">
                      <div class="oh-sticky-table__th" style="width:10px">
                          <div class="centered-div">
                              <input
                              type="checkbox"
                              class="oh-input oh-input__checkbox all-rshift"
                              title='{% trans "Select All" %}'
                          />
                          </div>
                      </div>
                      <div class="oh-sticky-table__th">{% trans "Employee" %}</div>
                      <div class="oh-sticky-table__th">{% trans "Title" %}</div>
                      <div class="oh-sticky-table__th">{% trans "Based On" %}</div>
                      <div class="oh-sticky-table__th">{% trans "Rotate" %}</div>
                      <div class="oh-sticky-table__th">{% trans "Start Date" %}</div>
                      <div class="oh-sticky-table__th">{% trans "Current Shift" %}</div>
                      <div class="oh-sticky-table__th">{% trans "Next Switch" %}</div>
                      <div class="oh-sticky-table__th">{% trans "Next Shift" %}</div>
                      <div class="oh-sticky-table__th oh-sticky-table__right" style="width:250px">{% trans "Actions" %}</div>
                      </div>
                  </div>
                  <div class="oh-sticky-table__tbody">
                      {% for rshift in rshift_request_list.list %}
                      <div class="oh-sticky-table__tr" draggable="true" hx-get="{% url "rshift-individual-view" rshift.id %}?{{pd}}&instances_ids={{assign_ids}}" hx-target="#objectDetailsModalTarget" data-toggle="oh-modal-toggle" data-target="#objectDetailsModal">
                      <div class="oh-sticky-table__sd" onclick="event.stopPropagation();">
                          <div class="centered-div">
                          <input
                              type="checkbox"
                              id="{{rshift.id}}"
                              onchange="highlightRow($(this))"
                              class="oh-input rshift-checkbox oh-input__checkbox all-rshift-row"
                          />
                          </div>
                      </div>
                      <div class="oh-sticky-table__td">
                          <div class="oh-profile oh-profile--md">
                          <div class="oh-profile__avatar mr-1">
                              <img
                              src="{{rshift.employee_id.get_avatar}}"
                              class="oh-profile__image"
                              alt=""
                              />
                          </div>
                              <span class="oh-profile__name oh-text--dark">{{rshift.employee_id}}</span>
                          </div>
                      </div>
                      <div class="oh-sticky-table__td">{{rshift.rotating_shift_id}}</div>
                      <div class="oh-sticky-table__td">{{rshift.get_based_on_display }}</div>
                      <div class="oh-sticky-table__td">
                          {% if rshift.based_on == 'after' %}
                      {% trans "Rotate after" %} {{rshift.rotate_after_day}} {% trans "days" %}
                      {% elif rshift.based_on == "weekly" %}
                      {% trans "Weekly every" %} {{rshift.rotate_every_weekend}}
                      {% elif rshift.based_on == "monthly" %}
                      {% if rshift.rotate_every == "1" %}
                      {% trans "Rotate every" %} {{rshift.rotate_every}}st {% trans "day of month" %}
                      {% elif rshift.rotate_every == "2" %}
                      {% trans "Rotate every" %} {{rshift.rotate_every}}nd {% trans "day of month" %}
                      {% elif rshift.rotate_every == "3" %}
                      {% trans "Rotate every" %} {{rshift.rotate_every}}rd {% trans "day of month" %}
                      {% elif rshift.rotate_every == "last" %}
                      {% trans "Rotate every last day of month" %}
                      {% else %}
                      {% trans "Rotate every" %} {{rshift.rotate_every}}th {% trans "day of month" %}
                      {% endif %}
                      {% endif %}
                      </div>
                      <div class="oh-sticky-table__td dateformat_changer">{{rshift.start_date}}</div>
                      <div class="oh-sticky-table__td">{{rshift.current_shift}}</div>
                      <div class="oh-sticky-table__td dateformat_changer">{{rshift.next_change_date}}</div>
                      <div class="oh-sticky-table__td">{{rshift.next_shift}}</div>
                      <div class="oh-sticky-table__td oh-sticky-table__right">
                          <div class="oh-btn-group" onclick="event.stopPropagation()">
                          {% if perms.base.change_rotatingshiftassign  or request.user|is_reportingmanager %}
                          <a
                              hx-get="{% url 'rotating-shift-assign-update' rshift.id %}"
                              hx-target="#objectUpdateModalTarget"
                              hx-swap="innerHTML"
                              data-target="#objectUpdateModal"
                              data-toggle="oh-modal-toggle"
                              type="button"
                              class="oh-btn oh-btn--light-bkg w-50"
                              title="{% trans 'Update' %}"
                          >
                              <ion-icon name="create-outline"></ion-icon
                          ></a>
                          <a
                            class="oh-btn oh-btn--light-bkg w-50"
                            data-toggle="oh-modal-toggle"
                            data-target="#objectCreateModal"
                            hx-get="{% url 'rotating-shift-assign-duplicate' rshift.id %}?{{pg}}"
                            title="{% trans 'Duplicate' %}"
                            hx-target="#objectCreateModalTarget"
                            style= "cursor: pointer;"
                          >
                            <ion-icon name="copy-outline"></ion-icon>
                          </a>
                          {% endif %}
                          {% if perms.base.change_rotatingshiftassign  or request.user|is_reportingmanager %}
                            {% if rshift.is_active %}
                              <form hx-target="{% trans 'Do you want to archive this rotating shift assign?' %}" hx-get="{% url 'rotating-shift-assign-archive' rshift.id %}?{{pd}}"
                                    hx-target="#view-container" style="width:100%"
                                >
                                  <button class="oh-btn oh-btn--danger-outline oh-btn--light-bkg w-100" title="{% trans 'Archive' %}">
                                  <ion-icon name="archive"></ion-icon>
                                  </button>
                              </form>
                            {% else %}
                              <form hx-confirm="{% trans 'Do you Want to un-archive this rotating shift assign?' %}" hx-get="{% url 'rotating-shift-assign-archive' rshift.id %}?{{pd}}"
                                    hx-target="#view-container" style="width:100%"
                                >
                                  <button class="oh-btn oh-btn--danger-outline oh-btn--light-bkg w-100" title="{% trans 'Un-Archive' %}">
                                  <ion-icon name="archive"></ion-icon>
                                  </button>
                              </form>
                            {% endif %}
                          {% endif %}
                          {% if perms.base.delete_rotatingshiftassign or request.user|is_reportingmanager %}
                            <form
                                hx-confirm="{% trans 'Are you sure you want to delete this rotating shift assign?' %}"
                                hx-post="{% url 'rotating-shift-assign-delete' rshift.id %}?{{pd}}"
                                hx-target="#view-container"
                                style="width:100%"
                              >
                                {% csrf_token %}
                                <button
                                type="submit"
                                class="oh-btn oh-btn--danger-outline oh-btn--light-bkg w-100"
                                title="{% trans 'Remove' %}"
                                >
                                <ion-icon name="trash-outline"></ion-icon>
                                </button>
                            </form>
                          {% endif %}
                          </div>
                      </div>
                      </div>
                      {% endfor %}
                  </div>
                  </div>
              </div>
				</div>
        <div class="oh-pagination">
          <span class="oh-pagination__page">
              {% trans "Page" %} {{ rshift_request_list.list.number }}
              {%trans "of" %} {{rshift_request_list.list.paginator.num_pages }}.
          </span>
          <nav class="oh-pagination__nav">
              <div class="oh-pagination__input-container me-3">
                  <span class="oh-pagination__label me-1"
                      >{% trans "Page" %}</span
                  >
                  <input
                      type="number"
                      name="{{rshift_request_list.dynamic_name}}"
                      class="oh-pagination__input"
                      value="{{rshift_request_list.list.number}}"
                      hx-get="{% url 'rotating-shift-assign-view' %}?{{pd}}"
                      hx-target="#view-container"
                      min="1"
                  />
                  <span class="oh-pagination__label"
                      >{% trans "of" %}
                      {{rshift_request_list.list.paginator.num_pages}}</span
                  >
              </div>
              <ul class="oh-pagination__items">
                  {% if rshift_request_list.list.has_previous %}
                  <li
                      class="oh-pagination__item oh-pagination__item--wide"
                  >
                      <a
                          hx-target="#view-container"
                          hx-get="{% url 'rotating-shift-assign-view' %}?{{pd}}&{{rshift_request_list.dynamic_name}}=1"
                          class="oh-pagination__link"
                          >{% trans "First" %}</a
                      >
                  </li>
                  <li
                      class="oh-pagination__item oh-pagination__item--wide"
                  >
                      <a
                          hx-target="#view-container"
                          hx-get="{% url 'rotating-shift-assign-view' %}?{{pd}}&{{rshift_request_list.dynamic_name}}={{ rshift_request_list.list.previous_page_number }}"
                          class="oh-pagination__link"
                          >{% trans "Previous" %}</a
                      >
                  </li>
                  {% endif %} {% if rshift_request_list.list.has_next %}
                  <li
                      class="oh-pagination__item oh-pagination__item--wide"
                  >
                      <a
                          hx-target="#view-container"
                          hx-get="{% url 'rotating-shift-assign-view' %}?{{pd}}&{{rshift_request_list.dynamic_name}}={{ rshift_request_list.list.next_page_number }}"
                          class="oh-pagination__link"
                          >{% trans "Next" %}</a
                      >
                  </li>
                  <li
                      class="oh-pagination__item oh-pagination__item--wide"
                  >
                      <a
                          hx-target="#view-container"
                          hx-get="{% url 'rotating-shift-assign-view' %}?{{pd}}&{{rshift_request_list.dynamic_name}}={{ rshift_request_list.list.paginator.num_pages }}"
                          class="oh-pagination__link"
                          >{% trans "Last" %}</a
                      >
                  </li>
                  {% endif %}
              </ul>
          </nav>
        </div>
			</div>
		</div>
	</div>
  {% endfor %}

	<div class="oh-pagination oh-wrapper">
        <span class="oh-pagination__page">
          {% trans "Page" %} {{ rshift_assign.number }} {% trans "of" %} {{ rshift_assign.paginator.num_pages }}.
        </span>
        <nav class="oh-pagination__nav">
          <div class="oh-pagination__input-container me-3">
            <span class="oh-pagination__label me-1">{% trans "Page" %}</span>
            <input
              type="number"
              name="page"
              class="oh-pagination__input"
              value="{{rshift_assign.number}}"
              hx-get="{% url 'rotating-shift-assign-view' %}?{{pd}}"
              hx-target="#view-container"
              min="1"
            />
            <span class="oh-pagination__label"
              >{% trans "of" %} {{rshift_assign.paginator.num_pages}}</span
            >
          </div>
          <ul class="oh-pagination__items">
            {% if rshift_assign.has_previous %}
            <li class="oh-pagination__item oh-pagination__item--wide">
              <a
                hx-target="#view-container"
                hx-get="{% url 'rotating-shift-assign-view' %}?{{pd}}&page=1"
                class="oh-pagination__link"
                >{% trans "First" %}</a
              >
            </li>
            <li class="oh-pagination__item oh-pagination__item--wide">
              <a
                hx-target="#view-container"
                hx-get="{% url 'rotating-shift-assign-view' %}?{{pd}}&page={{ rshift_assign.previous_page_number }}"
                class="oh-pagination__link"
                >{% trans "Previous" %}</a
              >
            </li>
            {% endif %} {% if rshift_assign.has_next %}
            <li class="oh-pagination__item oh-pagination__item--wide">
              <a
                hx-target="#view-container" hx-get="{% url 'rotating-shift-assign-view' %}?{{pd}}&page={{ rshift_assign.next_page_number }}"
                class="oh-pagination__link"
                >{% trans "Next" %}</a
              >
            </li>
            <li class="oh-pagination__item oh-pagination__item--wide">
              <a
                hx-target="#view-container"
                hx-get="{% url 'rotating-shift-assign-view' %}?{{pd}}&page={{ rshift_assign.paginator.num_pages }}"
                class="oh-pagination__link"
                >{% trans "Last" %}</a
              >
            </li>
            {% endif %}
          </ul>
        </nav>
      </div>
      {% else %}
        <div class="oh-wrapper">
          <div class="oh-404">
            <img
              style="width: 150px; height: 150px"
              src="{% static 'images/ui/no_request.png' %}"
              class="oh-404__image mb-4"
            />
            <h5 class="oh-404__subtitle">
              {% trans "No group result found!" %}
            </h5>
          </div>
        </div>
      {% endif %}
</div>
<script>
  function addingRShiftIds() {
    var ids = JSON.parse($("#selectedRShifts").attr("data-ids") || "[]");
    var selectedCount = 0;

    $(".all-rshift-row").each(function () {
      if ($(this).is(":checked")) {
        ids.push(this.id);
      } else {
        var index = ids.indexOf(this.id);
        if (index > -1) {
          ids.splice(index, 1);
        }
      }
    });

    ids = makeRShiftListUnique(ids);
    selectedCount = ids.length;

    getCurrentLanguageCode(function (code) {
      languageCode = code;
      var message = rowMessages[languageCode];
      $("#selectedRShifts").attr("data-ids", JSON.stringify(ids));
      if (selectedCount === 0) {
        $("#selectedShowRShifts").css("display", "none");
        $("#exportRShifts").css("display", "none");
        $("#unselectAllRShifts").css("display", "none");
      } else {
        $("#exportRShifts").css("display", "inline-flex");
        $("#unselectAllRShifts").css("display", "inline-flex");
        $("#selectedShowRShifts").css("display", "inline-flex");
        $("#selectedShowRShifts").text(selectedCount + " - " + message);
      }
    });
  }

  $(document).ready(function () {
    $(".all-rshift-row").change(function () {
      var parentTable = $(this).closest(".oh-sticky-table");
      var body = parentTable.find(".oh-sticky-table__tbody");
      var parentCheckbox = parentTable.find(".all-rshift");
      parentCheckbox.prop(
        "checked",
        body.find(".all-rshift-row:checked").length ===
          body.find(".all-rshift-row").length
      );
      addingRShiftIds();
    });

    $(".all-rshift").change(function () {
      addingRShiftIds();
    });

    tickRShiftCheckboxes();

    $("#selectAllRShifts").click(function () {
      $("#selectedRShifts").attr("data-clicked", 1);
      $("#selectedShowRShifts").removeAttr("style");
      var savedFilters = JSON.parse(localStorage.getItem("savedFilters"));

      if (savedFilters && savedFilters["filterData"] !== null) {
        var filter = savedFilters["filterData"];
        $.ajax({
          url: '{% url "r-shift-select-filter" %}',
          data: { page: "all", filter: JSON.stringify(filter) },
          type: "GET",
          dataType: "json",
          success: function (response) {
            var employeeIds = response.employee_ids;
            var selectedCount = employeeIds.length;
            for (var i = 0; i < employeeIds.length; i++) {
              var empId = employeeIds[i];
              $("#" + empId).prop("checked", true);
            }
            $("#selectedRShifts").attr("data-ids", JSON.stringify(employeeIds));

            count = makeRShiftListUnique(employeeIds);
            tickRShiftCheckboxes(count);
          },
          error: function (xhr, status, error) {
            console.error("Error:", error);
          },
        });
      } else {
        $.ajax({
          url: '{% url "r-shift-select" %}',
          data: { page: "all" },
          type: "GET",
          dataType: "json",
          success: function (response) {
            var employeeIds = response.employee_ids;
            var selectedCount = employeeIds.length;
            for (var i = 0; i < employeeIds.length; i++) {
              var empId = employeeIds[i];
              $("#" + empId).prop("checked", true);
            }
            var previousIds = $("#selectedRShifts").attr("data-ids");
            $("#selectedRShifts").attr(
              "data-ids",
              JSON.stringify(
                Array.from(
                  new Set([...employeeIds, ...JSON.parse(previousIds)])
                )
              )
            );
            count = makeRShiftListUnique(employeeIds);
            tickRShiftCheckboxes(count);
          },
          error: function (xhr, status, error) {
            console.error("Error:", error);
          },
        });
      }
    });

    $("#unselectAllRShifts").click(function () {
      $("#selectedRShifts").attr("data-clicked", 0);
      $.ajax({
        url: '{% url "r-shift-select" %}',
        data: { page: "unselect", filter: "{}" },
        type: "GET",
        dataType: "json",
        success: function (response) {
          var employeeIds = response.employee_ids;
          for (var i = 0; i < employeeIds.length; i++) {
            var empId = employeeIds[i];
            $("#" + empId).prop("checked", false);
            $(".all-rshift").prop("checked", false);
          }
          var ids = JSON.parse($("#selectedRShifts").attr("data-ids") || "[]");
          var uniqueIds = makeListUnique(ids);
          toggleHighlight(uniqueIds);
          $("#selectedRShifts").attr("data-ids", JSON.stringify([]));

          count = [];
          tickRShiftCheckboxes(count);
        },
        error: function (xhr, status, error) {
          console.error("Error:", error);
        },
      });
    });
  });
</script>
