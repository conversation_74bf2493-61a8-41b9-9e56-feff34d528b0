{% load i18n %}
<div class="oh-hover-btn-container" style="width: 100%;">
    {% if widget.attrs.type != "text_area" %}
    <input type="{% if not widget.attrs.type %}{{ widget.type }}{% else %}{{widget.attrs.type}}{% endif %}" name="{{ widget.name }}"{% if widget.value != None %} value="{{ widget.value }}"{% endif %}{% include "django/forms/widgets/attrs.html" %}>
    {% else %}
    <textarea name="{{ widget.name }}"{% include "django/forms/widgets/attrs.html" %}>{% if widget.value != None %}{{ widget.value }}{% endif %}</textarea>
    <script>
        $(document).ready(function () {
            $("textarea").closest(".col-lg-6").removeClass("col-lg-6").addClass("col-lg-12")
        });
    </script>

    {% endif %}
    {% if form.df_user_has_change_perm or form.delete_dynamicfield %}
    <div class="oh-hover-btn-drawer">
        {% if form.df_user_has_change_perm %}
        <button
        hx-get="{% url "edit-verbose-name" widget.attrs.pk %}?df_model_path={{form.df_form_model_path}}"
        hx-target="[id='dfModalBody{{ form.df_form_model_path }}']"
        onclick="$(`[id='dfModal{{ form.df_form_model_path }}']:first`).addClass('oh-modal--show')"
        type="button"
        class="oh-hover-btn__small"
        onclick=""><ion-icon name="create-outline"></ion-icon></button>
        {% endif %}
        {% if form.df_user_has_delete_perm %}
        <button type="button" class="oh-hover-btn__small" onclick="confirmAction(this)"><ion-icon name="trash-outline"></ion-icon></button>
        <script>
            function confirmAction(element) {
                Swal.fire({
                    title: "{% trans 'Are you sure?' %}",
                    text: `{% trans "You won't be able to revert this!" %}`,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: "{% trans "Proceed" %}"
                }).then((result) => {
                    if (result.isConfirmed) {
                        $.ajax({
                            type: "post",
                            url: '{% url "remove-dynamic-field" %}',
                            data: {
                                csrfmiddlewaretoken: getCookie("csrftoken"),
                                pk: "{{widget.attrs.pk}}",
                            },
                            traditional:true,
                            success: function (response) {
                                Swal.fire(
                                    "{% trans 'Success' %}",
                                    "{% trans "Column will be permently removed from the table on the next service reload" %}",
                                    'success'
                                );
                                setTimeout(() => {
                                    window.location.reload()
                                }, 1500);
                            }

                        });
                    }
                });
            }
        </script>
        {% endif %}
    </div>
    {% endif %}
</div>
