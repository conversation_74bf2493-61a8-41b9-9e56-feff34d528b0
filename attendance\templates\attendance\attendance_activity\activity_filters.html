{% load static %} {% load i18n %}
<div class="oh-dropdown__filter-body">
    <div class="oh-accordion">
        <div class="oh-accordion-header">{% trans "Work Info" %}</div>
        <div class="oh-accordion-body">
            <div class="row">
                <div class="col-sm-12 col-md-12 col-lg-6">
                    <div class="oh-input-group">
                        <label class="oh-label" for="{{f.form.employee_id.id_for_label}}">{% trans "Employee" %}</label>
                        {{f.form.employee_id}}
                    </div>
                    <div class="oh-input-group">
                        <label class="oh-label" for="{{f.form.employee_id__employee_work_info__department_id.id_for_label}}">{% trans "Department" %}</label>
                        {{f.form.employee_id__employee_work_info__department_id}}
                    </div>
                    <div class="oh-input-group">
                        <label class="oh-label" for="{{f.form.employee_id__employee_work_info__shift_id.id_for_label}}">{% trans "Shift" %}</label>
                        {{f.form.employee_id__employee_work_info__shift_id}}
                    </div>
                    <div class="oh-input-group">
                        <label class="oh-label" for="{{f.form.employee_id__employee_work_info__reporting_manager_id.id_for_label}}">{% trans "Reporting Manager" %}</label>
                        {{f.form.employee_id__employee_work_info__reporting_manager_id}}
                    </div>
                </div>
                <div class="col-sm-12 col-md-12 col-lg-6">
                    <div class="oh-input-group">
                        <label class="oh-label" for="{{f.form.employee_id__employee_work_info__company_id.id_for_label}}">{% trans "Company" %}</label>
                        {{f.form.employee_id__employee_work_info__company_id}}
                    </div>
                    <div class="oh-input-group">
                        <label class="oh-label" for="{{f.form.employee_id__employee_work_info__job_position_id.id_for_label}}">{% trans "Job Position" %}</label>
                        {{f.form.employee_id__employee_work_info__job_position_id}}
                    </div>
                    <div class="oh-input-group">
                        <label class="oh-label" for="{{f.form.employee_id__employee_work_info__work_type_id.id_for_label}}">{% trans "Work Type" %}</label>
                        {{f.form.employee_id__employee_work_info__work_type_id}}
                    </div>
                    <div class="oh-input-group">
                        <label class="oh-label" for="{{f.form.employee_id__employee_work_info__location.id_for_label}}">{% trans "Work Location" %}</label>
                        {{f.form.employee_id__employee_work_info__location}}
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="oh-accordion">
        <div class="oh-accordion-header">{% trans "Attendance Activity" %}</div>
        <div class="oh-accordion-body">
            <div class="row">
                <div class="col-sm-12 col-md-12 col-lg-6">
                    <div class="oh-input-group">
                        <label class="oh-label" for="{{f.form.attendance_date.id_for_label}}">{% trans "Attendance Date" %}</label>
                        {{f.form.attendance_date}}
                    </div>
                    <div class="oh-input-group">
                        <label class="oh-label" for="{{f.form.clock_out_date.id_for_label}}">{% trans "Out Date" %}</label>
                        {{f.form.clock_out_date}}
                    </div>
                </div>
                <div class="col-sm-12 col-md-12 col-lg-6">
                    <div class="oh-input-group">
                        <label class="oh-label" for="{{f.form.clock_in_date.id_for_label}}">{% trans "In Date" %}</label>
                        {{f.form.clock_in_date}}
                    </div>
                    <div class="oh-input-group">
                        <label class="oh-label" for="{{f.form.shift_day.id_for_label}}">{% trans "Shift Day" %}</label>
                        {{f.form.shift_day}}
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="oh-accordion">
        <div class="oh-accordion-header">{% trans "Advanced" %}</div>
        <div class="oh-accordion-body">
            <div class="row">
                <div class="col-sm-12 col-md-12 col-lg-6">
                    <div class="oh-input-group">
                        <label class="oh-label" for="{{f.form.attendance_date_from.id_for_label}}">{% trans "Attendance From" %}</label>
                        {{f.form.attendance_date_from}}
                    </div>
                    <div class="oh-input-group">
                        <label class="oh-label" for="{{f.form.in_from.id_for_label}}">{% trans "In From" %}</label>
                        {{f.form.in_from}}
                    </div>
                    <div class="oh-input-group">
                        <label class="oh-label" for="{{f.form.out_from.id_for_label}}">{% trans "Out From" %}</label>
                        {{f.form.out_from}}
                    </div>
                </div>
                <div class="col-sm-12 col-md-12 col-lg-6">
                    <div class="oh-input-group">
                        <label class="oh-label" for="{{f.form.attendance_date_till.id_for_label}}">{% trans "Attendance Till" %}</label>
                        {{f.form.attendance_date_till}}
                    </div>
                    <div class="oh-input-group">
                        <label class="oh-label" for="{{f.form.in_till.id_for_label}}">{% trans "In Till" %}</label>
                        {{f.form.in_till}}
                    </div>
                    <div class="oh-input-group">
                        <label class="oh-label" for="{{f.form.out_till.id_for_label}}">{% trans "Out Till" %}</label>
                        {{f.form.out_till}}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="oh-dropdown__filter-footer">
    <button class="oh-btn oh-btn--secondary oh-btn--small w-100 filterButton" id="filterSubmit">
        {% trans "Filter" %}
    </button>
</div>
<script src="{% static '/base/filter.js' %}"></script>
