"""
Django Tools Registry for AI Assistant

This module provides Django-specific tools that the AI agent can use to interact
with the HRMS system.
"""

import json
import logging
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
from django.contrib.auth.models import User
from django.db.models import Q
from django.urls import reverse, NoReverseMatch

from employee.models import Employee, EmployeeWorkInformation
from attendance.models import Attendance
from leave.models import LeaveRequest, LeaveType
from payroll.models.models import Payslip
from base.models import Department, JobPosition, Company
from .security import PermissionChecker

logger = logging.getLogger(__name__)


class DjangoToolsRegistry:
    """Registry of Django tools available to the AI agent."""
    
    def __init__(self, user: User):
        self.user = user
        self.permission_checker = PermissionChecker(user)
        self._tools = {}
        self._register_tools()
    
    def _register_tools(self):
        """Register all available tools."""
        # Employee tools
        self._register_tool(
            "search_employees",
            self.search_employees,
            "Search for employees by name, department, location, or other criteria. "
            "Parameters: filters (dict with name, department, location, etc.)"
        )
        
        self._register_tool(
            "get_employee_details",
            self.get_employee_details,
            "Get detailed information about a specific employee. "
            "Parameters: employee_name or employee_id"
        )
        
        # Leave tools
        self._register_tool(
            "search_leave_requests",
            self.search_leave_requests,
            "Search for leave requests by employee, status, or date range. "
            "Parameters: filters (dict with employee_name, status, start_date, end_date)"
        )
        
        self._register_tool(
            "create_leave_request",
            self.create_leave_request,
            "Create a new leave request for an employee. "
            "Parameters: employee_name, leave_type, start_date, end_date, reason"
        )
        
        # Attendance tools
        self._register_tool(
            "search_attendance",
            self.search_attendance,
            "Search attendance records by employee and date range. "
            "Parameters: filters (dict with employee_name, start_date, end_date)"
        )
        
        # Payroll tools
        self._register_tool(
            "search_payroll",
            self.search_payroll,
            "Search payroll records by employee and period. "
            "Parameters: filters (dict with employee_name, month, year)"
        )
        
        self._register_tool(
            "get_payroll_summary",
            self.get_payroll_summary,
            "Get payroll summary for a specific employee or department. "
            "Parameters: employee_name or department"
        )
        
        # Navigation tools
        self._register_tool(
            "navigate_to_page",
            self.navigate_to_page,
            "Navigate to a specific page in the HRMS system. "
            "Parameters: page_name (dashboard, employees, leave, attendance, payroll)"
        )
        
        # Export tools
        self._register_tool(
            "export_data",
            self.export_data,
            "Export data from the system (employees, attendance, leave, payroll). "
            "Parameters: data_type, filters (optional)"
        )

        # Additional comprehensive tools
        self._register_tool(
            "get_department_employees",
            self.get_department_employees,
            "Get all employees in a specific department. "
            "Parameters: department_name"
        )

        self._register_tool(
            "get_employee_attendance_summary",
            self.get_employee_attendance_summary,
            "Get attendance summary for an employee for a specific period. "
            "Parameters: employee_name, start_date, end_date"
        )

        self._register_tool(
            "get_leave_balance",
            self.get_leave_balance,
            "Get leave balance for an employee. "
            "Parameters: employee_name"
        )

        self._register_tool(
            "run_payroll",
            self.run_payroll,
            "Run payroll for a specific department or all employees. "
            "Parameters: department (optional), month, year"
        )

        self._register_tool(
            "get_company_statistics",
            self.get_company_statistics,
            "Get company-wide statistics (employee count, departments, etc.). "
            "Parameters: none"
        )

        self._register_tool(
            "search_by_salary_range",
            self.search_by_salary_range,
            "Search employees by salary range. "
            "Parameters: min_salary, max_salary"
        )

        self._register_tool(
            "get_recent_joiners",
            self.get_recent_joiners,
            "Get employees who joined recently. "
            "Parameters: days (default 30)"
        )

        self._register_tool(
            "get_birthday_list",
            self.get_birthday_list,
            "Get employees with birthdays in current month or specific month. "
            "Parameters: month (optional)"
        )
    
    def _register_tool(self, name: str, func: Callable, description: str):
        """Register a tool with the registry."""
        self._tools[name] = {
            'function': func,
            'description': description
        }
    
    def get_all_tools(self) -> Dict[str, Dict[str, Any]]:
        """Get all registered tools."""
        return self._tools
    
    def get_tool(self, name: str) -> Optional[Dict[str, Any]]:
        """Get a specific tool by name."""
        return self._tools.get(name)
    
    # Employee Tools
    def search_employees(self, filters: str) -> str:
        """Search for employees based on filters."""
        try:
            if not self.permission_checker.can_view_employees():
                return "Permission denied: Cannot view employees"
            
            # Parse filters
            if isinstance(filters, str):
                try:
                    filters = json.loads(filters)
                except json.JSONDecodeError:
                    filters = {}
            
            queryset = Employee.objects.all()
            
            # Apply filters
            if filters.get('name'):
                queryset = queryset.filter(
                    Q(employee_first_name__icontains=filters['name']) |
                    Q(employee_last_name__icontains=filters['name'])
                )
            
            if filters.get('department'):
                queryset = queryset.filter(
                    employee_work_info__department__department__icontains=filters['department']
                )
            
            if filters.get('location'):
                queryset = queryset.filter(
                    employee_work_info__location__icontains=filters['location']
                )
            
            # Limit results
            employees = queryset[:20]
            
            result = []
            for emp in employees:
                result.append({
                    'id': emp.id,
                    'name': f"{emp.employee_first_name} {emp.employee_last_name or ''}".strip(),
                    'email': emp.email,
                    'department': emp.employee_work_info.department.department if hasattr(emp, 'employee_work_info') and emp.employee_work_info.department else 'N/A',
                    'location': emp.employee_work_info.location if hasattr(emp, 'employee_work_info') else 'N/A'
                })
            
            return json.dumps({
                'employees': result,
                'count': len(result),
                'total': queryset.count()
            })
            
        except Exception as e:
            logger.error(f"Error searching employees: {e}")
            return f"Error searching employees: {e}"
    
    def get_employee_details(self, employee_identifier: str) -> str:
        """Get detailed information about a specific employee."""
        try:
            if not self.permission_checker.can_view_employees():
                return "Permission denied: Cannot view employee details"
            
            # Try to find employee by name or ID
            employee = None
            if employee_identifier.isdigit():
                employee = Employee.objects.filter(id=int(employee_identifier)).first()
            else:
                employee = Employee.objects.filter(
                    Q(employee_first_name__icontains=employee_identifier) |
                    Q(employee_last_name__icontains=employee_identifier)
                ).first()
            
            if not employee:
                return f"Employee '{employee_identifier}' not found"
            
            details = {
                'id': employee.id,
                'name': f"{employee.employee_first_name} {employee.employee_last_name or ''}".strip(),
                'email': employee.email,
                'phone': employee.phone,
                'badge_id': employee.badge_id,
            }
            
            # Add work information if available
            if hasattr(employee, 'employee_work_info') and employee.employee_work_info:
                work_info = employee.employee_work_info
                details.update({
                    'department': work_info.department.department if work_info.department else 'N/A',
                    'job_position': work_info.job_position_id.job_position if work_info.job_position_id else 'N/A',
                    'location': work_info.location or 'N/A',
                    'joining_date': work_info.date_joining.isoformat() if work_info.date_joining else 'N/A',
                    'basic_salary': work_info.basic_salary or 0
                })
            
            return json.dumps(details)
            
        except Exception as e:
            logger.error(f"Error getting employee details: {e}")
            return f"Error getting employee details: {e}"
    
    # Leave Tools
    def search_leave_requests(self, filters: str) -> str:
        """Search for leave requests."""
        try:
            # Parse filters
            if isinstance(filters, str):
                try:
                    filters = json.loads(filters)
                except json.JSONDecodeError:
                    filters = {}
            
            queryset = LeaveRequest.objects.all()
            
            # Apply filters
            if filters.get('employee_name'):
                queryset = queryset.filter(
                    Q(employee_id__employee_first_name__icontains=filters['employee_name']) |
                    Q(employee_id__employee_last_name__icontains=filters['employee_name'])
                )
            
            if filters.get('status'):
                queryset = queryset.filter(status=filters['status'])
            
            if filters.get('start_date'):
                queryset = queryset.filter(start_date__gte=filters['start_date'])
            
            if filters.get('end_date'):
                queryset = queryset.filter(end_date__lte=filters['end_date'])
            
            # Limit results
            leave_requests = queryset[:20]
            
            result = []
            for leave in leave_requests:
                result.append({
                    'id': leave.id,
                    'employee': f"{leave.employee_id.employee_first_name} {leave.employee_id.employee_last_name or ''}".strip(),
                    'leave_type': leave.leave_type_id.name if leave.leave_type_id else 'N/A',
                    'start_date': leave.start_date.isoformat(),
                    'end_date': leave.end_date.isoformat(),
                    'status': leave.status,
                    'reason': leave.description or 'N/A'
                })
            
            return json.dumps({
                'leave_requests': result,
                'count': len(result)
            })
            
        except Exception as e:
            logger.error(f"Error searching leave requests: {e}")
            return f"Error searching leave requests: {e}"

    def search_attendance(self, filters: str) -> str:
        """Search attendance records based on filters."""
        try:
            # Parse filters
            if isinstance(filters, str):
                try:
                    filters = json.loads(filters)
                except json.JSONDecodeError:
                    filters = {}

            queryset = Attendance.objects.all()

            # Apply filters
            if filters.get('employee_name'):
                queryset = queryset.filter(
                    Q(employee_id__employee_first_name__icontains=filters['employee_name']) |
                    Q(employee_id__employee_last_name__icontains=filters['employee_name'])
                )

            if filters.get('date'):
                queryset = queryset.filter(attendance_date=filters['date'])

            if filters.get('start_date'):
                queryset = queryset.filter(attendance_date__gte=filters['start_date'])

            if filters.get('end_date'):
                queryset = queryset.filter(attendance_date__lte=filters['end_date'])

            # Limit results
            attendance_records = queryset[:20]

            result = []
            for attendance in attendance_records:
                result.append({
                    'id': attendance.id,
                    'employee': f"{attendance.employee_id.employee_first_name} {attendance.employee_id.employee_last_name or ''}".strip(),
                    'date': attendance.attendance_date.isoformat(),
                    'clock_in': attendance.attendance_clock_in_date.isoformat() if attendance.attendance_clock_in_date else 'N/A',
                    'clock_out': attendance.attendance_clock_out_date.isoformat() if attendance.attendance_clock_out_date else 'N/A',
                    'worked_hours': attendance.attendance_worked_hour or 0,
                    'status': 'Present' if attendance.attendance_worked_hour and attendance.attendance_worked_hour > 0 else 'Absent'
                })

            return json.dumps({
                'attendance_records': result,
                'count': len(result)
            })

        except Exception as e:
            logger.error(f"Error searching attendance: {e}")
            return f"Error searching attendance: {e}"

    def search_payroll(self, filters: str) -> str:
        """Search payroll records based on filters."""
        try:
            # Parse filters
            if isinstance(filters, str):
                try:
                    filters = json.loads(filters)
                except json.JSONDecodeError:
                    filters = {}

            queryset = Payslip.objects.all()

            # Apply filters
            if filters.get('employee_name'):
                queryset = queryset.filter(
                    Q(employee_id__employee_first_name__icontains=filters['employee_name']) |
                    Q(employee_id__employee_last_name__icontains=filters['employee_name'])
                )

            if filters.get('month'):
                queryset = queryset.filter(start_date__month=filters['month'])

            if filters.get('year'):
                queryset = queryset.filter(start_date__year=filters['year'])

            # Limit results
            payroll_records = queryset[:20]

            result = []
            for payslip in payroll_records:
                result.append({
                    'id': payslip.id,
                    'employee': f"{payslip.employee_id.employee_first_name} {payslip.employee_id.employee_last_name or ''}".strip(),
                    'period': f"{payslip.start_date.isoformat()} to {payslip.end_date.isoformat()}",
                    'basic_pay': payslip.basic_pay or 0,
                    'gross_pay': payslip.gross_pay or 0,
                    'deduction': payslip.deduction or 0,
                    'net_pay': payslip.net_pay or 0,
                    'status': payslip.status or 'Draft'
                })

            return json.dumps({
                'payroll_records': result,
                'count': len(result)
            })

        except Exception as e:
            logger.error(f"Error searching payroll: {e}")
            return f"Error searching payroll: {e}"
    
    def create_leave_request(self, employee_name: str, leave_type: str, start_date: str, end_date: str, reason: str = "") -> str:
        """Create a new leave request."""
        try:
            if not self.permission_checker.can_create_leave_request():
                return "Permission denied: Cannot create leave requests"
            
            # Find employee
            employee = Employee.objects.filter(
                Q(employee_first_name__icontains=employee_name) |
                Q(employee_last_name__icontains=employee_name)
            ).first()
            
            if not employee:
                return f"Employee '{employee_name}' not found"
            
            # Find leave type
            leave_type_obj = LeaveType.objects.filter(name__icontains=leave_type).first()
            if not leave_type_obj:
                return f"Leave type '{leave_type}' not found"
            
            # Create leave request (this would typically redirect to a form)
            return json.dumps({
                'action': 'redirect',
                'url': '/leave/leave-request-create/',
                'prefill_data': {
                    'employee_id': employee.id,
                    'leave_type_id': leave_type_obj.id,
                    'start_date': start_date,
                    'end_date': end_date,
                    'description': reason
                },
                'message': f"Opening leave request form for {employee.employee_first_name}"
            })
            
        except Exception as e:
            logger.error(f"Error creating leave request: {e}")
            return f"Error creating leave request: {e}"
    
    # Navigation Tools
    def navigate_to_page(self, page_name: str) -> str:
        """Navigate to a specific page."""
        try:
            page_mapping = {
                'dashboard': '/',
                'employees': '/employee/employee-view/',
                'leave': '/leave/leave-request-view/',
                'attendance': '/attendance/attendance-view/',
                'payroll': '/payroll/payslip-view/',
                'settings': '/settings/',
            }
            
            url = page_mapping.get(page_name.lower())
            if url:
                return json.dumps({
                    'action': 'redirect',
                    'url': url,
                    'message': f"Navigating to {page_name}"
                })
            else:
                return f"Page '{page_name}' not found. Available pages: {', '.join(page_mapping.keys())}"
                
        except Exception as e:
            logger.error(f"Error navigating to page: {e}")
            return f"Error navigating to page: {e}"
    
    # Export Tools
    def export_data(self, data_type: str, filters: str = "{}") -> str:
        """Export data from the system."""
        try:
            if not self.permission_checker.can_export_data():
                return "Permission denied: Cannot export data"

            export_mapping = {
                'employees': '/employee/employee-export/',
                'attendance': '/attendance/attendance-export/',
                'leave': '/leave/leave-export/',
                'payroll': '/payroll/payroll-export/',
            }

            url = export_mapping.get(data_type.lower())
            if url:
                return json.dumps({
                    'action': 'download',
                    'url': url,
                    'message': f"Preparing {data_type} export"
                })
            else:
                return f"Export type '{data_type}' not supported. Available types: {', '.join(export_mapping.keys())}"

        except Exception as e:
            logger.error(f"Error exporting data: {e}")
            return f"Error exporting data: {e}"

    # Additional comprehensive tools implementations
    def get_department_employees(self, department_name: str) -> str:
        """Get all employees in a specific department."""
        try:
            if not self.permission_checker.can_view_employees():
                return "Permission denied: Cannot view employees"

            employees = Employee.objects.filter(
                employee_work_info__department__department__icontains=department_name
            )[:50]  # Limit to 50 employees

            result = []
            for emp in employees:
                result.append({
                    'id': emp.id,
                    'name': f"{emp.employee_first_name} {emp.employee_last_name or ''}".strip(),
                    'email': emp.email,
                    'job_position': emp.employee_work_info.job_position_id.job_position if hasattr(emp, 'employee_work_info') and emp.employee_work_info.job_position_id else 'N/A'
                })

            return json.dumps({
                'department': department_name,
                'employees': result,
                'count': len(result)
            })

        except Exception as e:
            logger.error(f"Error getting department employees: {e}")
            return f"Error getting department employees: {e}"

    def get_employee_attendance_summary(self, employee_name: str, start_date: str, end_date: str) -> str:
        """Get attendance summary for an employee."""
        try:
            # Find employee
            employee = Employee.objects.filter(
                Q(employee_first_name__icontains=employee_name) |
                Q(employee_last_name__icontains=employee_name)
            ).first()

            if not employee:
                return f"Employee '{employee_name}' not found"

            # Get attendance records
            attendance_records = Attendance.objects.filter(
                employee_id=employee,
                attendance_date__range=[start_date, end_date]
            )

            total_days = attendance_records.count()
            present_days = attendance_records.filter(attendance_worked_hour__gt=0).count()
            absent_days = total_days - present_days

            summary = {
                'employee': f"{employee.employee_first_name} {employee.employee_last_name or ''}".strip(),
                'period': f"{start_date} to {end_date}",
                'total_days': total_days,
                'present_days': present_days,
                'absent_days': absent_days,
                'attendance_percentage': round((present_days / total_days * 100) if total_days > 0 else 0, 2)
            }

            return json.dumps(summary)

        except Exception as e:
            logger.error(f"Error getting attendance summary: {e}")
            return f"Error getting attendance summary: {e}"

    def get_leave_balance(self, employee_name: str) -> str:
        """Get leave balance for an employee."""
        try:
            # Find employee
            employee = Employee.objects.filter(
                Q(employee_first_name__icontains=employee_name) |
                Q(employee_last_name__icontains=employee_name)
            ).first()

            if not employee:
                return f"Employee '{employee_name}' not found"

            # Get leave types and balances (this would depend on your leave balance logic)
            leave_types = LeaveType.objects.all()
            balances = []

            for leave_type in leave_types:
                # Calculate used leaves this year
                used_leaves = LeaveRequest.objects.filter(
                    employee_id=employee,
                    leave_type_id=leave_type,
                    status='approved',
                    start_date__year=datetime.now().year
                ).count()

                remaining = max(0, leave_type.total_days - used_leaves)

                balances.append({
                    'leave_type': leave_type.name,
                    'total_allocated': leave_type.total_days,
                    'used': used_leaves,
                    'remaining': remaining
                })

            return json.dumps({
                'employee': f"{employee.employee_first_name} {employee.employee_last_name or ''}".strip(),
                'leave_balances': balances
            })

        except Exception as e:
            logger.error(f"Error getting leave balance: {e}")
            return f"Error getting leave balance: {e}"

    def run_payroll(self, department: str = "", month: str = "", year: str = "") -> str:
        """Run payroll for a specific department or all employees."""
        try:
            if not self.permission_checker.can_view_payroll():
                return "Permission denied: Cannot access payroll"

            # This would typically redirect to payroll processing page
            url = "/payroll/payslip-create/"
            params = []

            if department:
                params.append(f"department={department}")
            if month:
                params.append(f"month={month}")
            if year:
                params.append(f"year={year}")

            if params:
                url += f"?{'&'.join(params)}"

            return json.dumps({
                'action': 'redirect',
                'url': url,
                'message': f"Opening payroll processing for {department or 'all departments'}"
            })

        except Exception as e:
            logger.error(f"Error running payroll: {e}")
            return f"Error running payroll: {e}"

    def get_company_statistics(self) -> str:
        """Get company-wide statistics."""
        try:
            if not self.permission_checker.can_view_employees():
                return "Permission denied: Cannot view company statistics"

            total_employees = Employee.objects.count()
            departments = Department.objects.count()
            active_employees = Employee.objects.filter(is_active=True).count()

            # Get department-wise employee count
            dept_stats = []
            for dept in Department.objects.all()[:10]:  # Limit to 10 departments
                emp_count = Employee.objects.filter(
                    employee_work_info__department=dept
                ).count()
                dept_stats.append({
                    'department': dept.department,
                    'employee_count': emp_count
                })

            statistics = {
                'total_employees': total_employees,
                'active_employees': active_employees,
                'total_departments': departments,
                'department_breakdown': dept_stats
            }

            return json.dumps(statistics)

        except Exception as e:
            logger.error(f"Error getting company statistics: {e}")
            return f"Error getting company statistics: {e}"

    def search_by_salary_range(self, min_salary: str, max_salary: str) -> str:
        """Search employees by salary range."""
        try:
            if not self.permission_checker.can_view_employees():
                return "Permission denied: Cannot view employee salary information"

            min_sal = float(min_salary) if min_salary else 0
            max_sal = float(max_salary) if max_salary else *********

            employees = Employee.objects.filter(
                employee_work_info__basic_salary__gte=min_sal,
                employee_work_info__basic_salary__lte=max_sal
            )[:20]  # Limit to 20 employees

            result = []
            for emp in employees:
                result.append({
                    'id': emp.id,
                    'name': f"{emp.employee_first_name} {emp.employee_last_name or ''}".strip(),
                    'department': emp.employee_work_info.department.department if hasattr(emp, 'employee_work_info') and emp.employee_work_info.department else 'N/A',
                    'salary': emp.employee_work_info.basic_salary if hasattr(emp, 'employee_work_info') else 0
                })

            return json.dumps({
                'salary_range': f"{min_sal} - {max_sal}",
                'employees': result,
                'count': len(result)
            })

        except Exception as e:
            logger.error(f"Error searching by salary range: {e}")
            return f"Error searching by salary range: {e}"

    def get_recent_joiners(self, days: str = "30") -> str:
        """Get employees who joined recently."""
        try:
            if not self.permission_checker.can_view_employees():
                return "Permission denied: Cannot view employees"

            days_int = int(days) if days.isdigit() else 30
            cutoff_date = datetime.now().date() - timedelta(days=days_int)

            recent_joiners = Employee.objects.filter(
                employee_work_info__date_joining__gte=cutoff_date
            ).order_by('-employee_work_info__date_joining')[:20]

            result = []
            for emp in recent_joiners:
                result.append({
                    'id': emp.id,
                    'name': f"{emp.employee_first_name} {emp.employee_last_name or ''}".strip(),
                    'department': emp.employee_work_info.department.department if hasattr(emp, 'employee_work_info') and emp.employee_work_info.department else 'N/A',
                    'joining_date': emp.employee_work_info.date_joining.isoformat() if hasattr(emp, 'employee_work_info') and emp.employee_work_info.date_joining else 'N/A'
                })

            return json.dumps({
                'period': f"Last {days_int} days",
                'recent_joiners': result,
                'count': len(result)
            })

        except Exception as e:
            logger.error(f"Error getting recent joiners: {e}")
            return f"Error getting recent joiners: {e}"

    def get_birthday_list(self, month: str = "") -> str:
        """Get employees with birthdays in current month or specific month."""
        try:
            if not self.permission_checker.can_view_employees():
                return "Permission denied: Cannot view employees"

            if month:
                target_month = int(month) if month.isdigit() else datetime.now().month
            else:
                target_month = datetime.now().month

            employees_with_birthdays = Employee.objects.filter(
                dob__month=target_month
            ).order_by('dob__day')[:50]

            result = []
            for emp in employees_with_birthdays:
                result.append({
                    'id': emp.id,
                    'name': f"{emp.employee_first_name} {emp.employee_last_name or ''}".strip(),
                    'department': emp.employee_work_info.department.department if hasattr(emp, 'employee_work_info') and emp.employee_work_info.department else 'N/A',
                    'birthday': emp.dob.strftime('%B %d') if emp.dob else 'N/A',
                    'days_until_birthday': emp.days_until_birthday() if hasattr(emp, 'days_until_birthday') else 'N/A'
                })

            month_name = datetime(2024, target_month, 1).strftime('%B')

            return json.dumps({
                'month': month_name,
                'birthdays': result,
                'count': len(result)
            })

        except Exception as e:
            logger.error(f"Error getting birthday list: {e}")
            return f"Error getting birthday list: {e}"
