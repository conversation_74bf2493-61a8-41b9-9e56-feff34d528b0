{% extends 'index.html' %} {% load static %} {% load i18n %} {% block content %}

<section class="oh-wrapper oh-main__topbar">
    <div
        class="oh-main__titlebar oh-main__titlebar--left oh-d-flex-column--resp oh-mb-3--small"
    >
        <h1 class="oh-main__titlebar-title fw-bold">
            {% trans "Mail Templates" %}
        </h1>
    </div>
    {% if perms.base.add_eagloramailtemplate %}
    <div
        class="oh-main__titlebar oh-main__titlebar--right oh-d-flex-column--resp oh-mb-3--small"
    >
        <a
            hx-get="{% url 'create-mail-template' %}"
            data-toggle="oh-modal-toggle"
            data-target="#objectCreateModal"
            hx-target="#objectCreateModalTarget"
            class="oh-btn oh-btn--secondary ml-2"
            ><ion-icon
                name="add"
                class="mr-1 md hydrated"
                role="img"
                aria-label="add"
            ></ion-icon
            >{% trans "Create" %}</a
        >
    </div>
    {% endif %}
</section>

<div class="oh-wrapper oh-faq-cards">
    {% for template in templates %}
    <div class="oh-faq-card">
        {% if perms.base.delete_eagloramailtemplate %}
        <h3 class="oh-faq-card__title d-flex justify-content-between">
            <span style="max-width:75%">{{ template.title }}</span>
            <div>
                <a
                    hx-get="{% url 'duplicate-mail-template' template.id %}"
                    hx-target="#objectCreateModalTarget"
                    data-toggle="oh-modal-toggle"
                    data-target="#objectCreateModal"
                    title="{% trans 'Duplicate' %}"
                    style="cursor: pointer"
                >
                    <ion-icon name="copy-outline"></ion-icon>
                </a>
                <a
                    href="{% url 'delete-mail-template' %}?ids={{template.id}}"
                    class="text-danger"
                    style="cursor: pointer"
                    onclick="return confirm('Do you want to delete this template?')"
                    title="{% trans 'Edit' %}"
                >
                    <ion-icon name="trash-outline"></ion-icon>
                </a>
            </div>
        </h3>
        {% endif %}
        <div
            class="oh-faq-card__desc"
            style="max-height: 500px; padding-top: 10px"
        >
            <div
                style="max-height: 350px; overflow: hidden"
                class="truncated-text"
            >
                {{ template.body|safe }}
            </div>
        </div>
        {% if perms.base.change_eagloramailtemplate %}
        <a
            hx-get="{% url 'view-mail-template' template.id %}"
            hx-target="#objectCreateModalTarget"
            data-toggle="oh-modal-toggle"
            data-target="#objectCreateModal"
            class="oh-btn oh-btn--secondary oh-btn--block"
            >{% trans "View Template" %}</a
        >
        {% endif %}
    </div>
    {% endfor %}
</div>
{% endblock %}
