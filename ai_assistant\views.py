"""
AI Assistant Views

This module contains the Django views for the AI Assistant functionality.
"""

import json
import logging
import time
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from django.utils.decorators import method_decorator
from django.views import View
from django.core.exceptions import ValidationError

from utils.intent_extractor import extract_command_intent
from .models import ChatHistory, AIAssistantSettings
from .command_processor import CommandProcessor
from .security import SecurityValidator, InputSanitizer
from .langchain_agent import EagloraAIAgent

logger = logging.getLogger(__name__)


@method_decorator(csrf_exempt, name='dispatch')
@method_decorator(login_required, name='dispatch')
class AICommandView(View):
    """Handle AI Assistant command processing."""
    
    def post(self, request):
        """
        Process AI command from user input.

        Expected JSON payload:
        {
            "message": "user command text",
            "context": {"current_page": "/employees/", "current_module": "employee"}  # optional
        }

        Returns JSON response:
        {
            "action": "redirect" | "download" | "ajax_update" | "reply",
            "url": "/target/page/or/file",
            "prefill_data": {"field_name": "value"},
            "message": "Assistant's feedback",
            "intent": "detected_intent",
            "confidence": 0.8
        }
        """
        start_time = time.time()

        try:
            # Parse request data
            data = json.loads(request.body)
            user_message = data.get('message', '').strip()
            context = data.get('context', {})

            if not user_message:
                return JsonResponse({
                    'action': 'reply',
                    'message': 'Please provide a command or question.',
                    'intent': 'error',
                    'confidence': 0.0
                }, status=400)

            # Sanitize input
            user_message = InputSanitizer.sanitize_command(user_message)
            logger.info(f"Processing message from {request.user.username}: {user_message}")

            # Get AI assistant settings
            settings = AIAssistantSettings.get_settings()

            # Choose processing method based on settings
            if settings.enable_langchain:
                # Use LangChain agent for advanced reasoning
                agent = EagloraAIAgent(request.user)
                result = agent.process_message(user_message, context)
            else:
                # Fallback to rule-based intent extraction
                result = extract_command_intent(user_message)

                # Security validation
                security_validator = SecurityValidator(request.user)
                result = security_validator.validate_command(user_message, result)

                # Process command with security and permission checks
                processor = CommandProcessor(request.user)
                result = processor.process_command(result)

            # Sanitize filters if present
            if 'filters' in result:
                result['filters'] = InputSanitizer.sanitize_filters(result['filters'])

            # Determine action type based on intent
            action = self._determine_action(result)

            # Build response
            response_data = {
                'action': action,
                'url': result.get('redirect_url') or result.get('url'),
                'prefill_data': result.get('prefill_data', {}),
                'message': result.get('message', 'Command processed.'),
                'intent': result.get('intent', 'unknown'),
                'confidence': result.get('confidence', 0.0),
                'filters': result.get('filters', {}),
                'sort_by': result.get('sort_by'),
                'order': result.get('order', 'asc')
            }

            # Calculate processing time
            processing_time = time.time() - start_time

            # Save to chat history
            try:
                ChatHistory.objects.create(
                    user=request.user,
                    message=user_message,
                    response=response_data,
                    intent=result.get('intent', 'unknown'),
                    confidence=result.get('confidence', 0.0),
                    processing_time=processing_time
                )
            except Exception as e:
                logger.warning(f"Failed to save chat history: {e}")

            return JsonResponse(response_data)
            
        except json.JSONDecodeError:
            return JsonResponse({
                'action': 'reply',
                'message': 'Invalid request format. Please try again.',
                'intent': 'error',
                'confidence': 0.0
            }, status=400)
            
        except Exception as e:
            logger.error(f"Error processing AI command: {e}")
            return JsonResponse({
                'action': 'reply',
                'message': 'Sorry, I encountered an error processing your request. Please try again.',
                'intent': 'error',
                'confidence': 0.0
            }, status=500)
    
    def _determine_action(self, result):
        """Determine the appropriate action based on the intent result."""
        intent = result.get('intent', 'reply')
        redirect_url = result.get('redirect_url')
        
        if intent in ['search', 'navigate', 'fill_form', 'run_action'] and redirect_url:
            return 'redirect'
        elif intent == 'export':
            return 'download'
        elif intent == 'import' and redirect_url:
            return 'redirect'
        elif intent == 'sort' or intent == 'search':
            return 'ajax_update'
        else:
            return 'reply'


@login_required
@require_http_methods(["GET"])
def chat_history(request):
    """Get user's chat history."""
    try:
        history = ChatHistory.objects.filter(user=request.user)[:20]  # Last 20 messages
        
        history_data = []
        for chat in history:
            try:
                response_data = json.loads(chat.response)
            except json.JSONDecodeError:
                response_data = {'message': chat.response}
            
            history_data.append({
                'id': chat.id,
                'message': chat.message,
                'response': response_data,
                'intent': chat.intent,
                'confidence': chat.confidence,
                'created_at': chat.created_at.isoformat()
            })
        
        return JsonResponse({
            'history': history_data,
            'count': len(history_data)
        })
        
    except Exception as e:
        logger.error(f"Error fetching chat history: {e}")
        return JsonResponse({
            'error': 'Failed to fetch chat history',
            'history': [],
            'count': 0
        }, status=500)


@login_required
@require_http_methods(["POST"])
@csrf_exempt
def clear_chat_history(request):
    """Clear user's chat history."""
    try:
        deleted_count = ChatHistory.objects.filter(user=request.user).delete()[0]
        return JsonResponse({
            'message': f'Cleared {deleted_count} chat messages.',
            'success': True
        })
    except Exception as e:
        logger.error(f"Error clearing chat history: {e}")
        return JsonResponse({
            'error': 'Failed to clear chat history',
            'success': False
        }, status=500)


# Function-based view for backward compatibility
@csrf_exempt
@login_required
@require_http_methods(["POST"])
def ai_command(request):
    """Function-based view wrapper for AICommandView."""
    view = AICommandView()
    return view.post(request)


@login_required
def chat_history(request):
    """Display chat history for the current user."""
    from django.shortcuts import render
    from django.core.paginator import Paginator

    chat_history = ChatHistory.objects.filter(
        user=request.user
    ).order_by('-created_at')

    paginator = Paginator(chat_history, 20)  # Show 20 chats per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    return render(request, 'ai_assistant/chat_history.html', {
        'page_obj': page_obj,
        'chat_history': page_obj,
    })


@login_required
@require_http_methods(["POST"])
def clear_chat_history(request):
    """Clear chat history for the current user."""
    try:
        deleted_count = ChatHistory.objects.filter(user=request.user).delete()[0]
        return JsonResponse({
            'success': True,
            'message': f'Cleared {deleted_count} chat entries.',
            'deleted_count': deleted_count
        })
    except Exception as e:
        logger.error(f"Error clearing chat history: {e}")
        return JsonResponse({
            'success': False,
            'message': 'Failed to clear chat history.'
        }, status=500)
