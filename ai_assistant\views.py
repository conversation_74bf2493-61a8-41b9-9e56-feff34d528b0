"""
AI Assistant Views

This module contains the Django views for the AI Assistant functionality.
"""

import json
import logging
import time
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from django.utils.decorators import method_decorator
from django.views import View
from django.core.exceptions import ValidationError

from utils.intent_extractor import extract_command_intent
from .models import ChatHistory, AIAssistantSettings
from .command_processor import CommandProcessor
from .security import SecurityValidator, InputSanitizer
from .langchain_agent import EagloraAIAgent
from .advanced_intent_extractor import AdvancedIntentExtractor

logger = logging.getLogger(__name__)


@method_decorator(csrf_exempt, name='dispatch')
@method_decorator(login_required, name='dispatch')
class AICommandView(View):
    """Handle AI Assistant command processing."""
    
    def post(self, request):
        """
        Process AI command from user input.

        Expected JSON payload:
        {
            "message": "user command text",
            "context": {"current_page": "/employees/", "current_module": "employee"}  # optional
        }

        Returns JSON response:
        {
            "action": "redirect" | "download" | "ajax_update" | "reply",
            "url": "/target/page/or/file",
            "prefill_data": {"field_name": "value"},
            "message": "Assistant's feedback",
            "intent": "detected_intent",
            "confidence": 0.8
        }
        """
        start_time = time.time()

        try:
            # Parse request data
            data = json.loads(request.body)
            user_message = data.get('message', '').strip()
            context = data.get('context', {})

            if not user_message:
                return JsonResponse({
                    'action': 'reply',
                    'message': 'Please provide a command or question.',
                    'intent': 'error',
                    'confidence': 0.0
                }, status=400)

            # Sanitize input
            user_message = InputSanitizer.sanitize_command(user_message)
            print("user_message >>>>>>>>>>>", user_message)
            logger.info(f"Processing message from {request.user.username}: {user_message}")

            # Get AI assistant settings
            settings = AIAssistantSettings.get_settings()
            print("settings >>>>>>>>>>>", settings.enable_langchain)

            # Choose processing method based on settings
            if settings.enable_langchain:
                # Use LangChain agent for advanced reasoning
                agent = EagloraAIAgent(request.user)
                result = agent.process_message(user_message, context)
                print("result inside if condition>>>>>>>>>>>", result)
            else:
                # Use advanced intent extractor for better accuracy
                advanced_extractor = AdvancedIntentExtractor()
                result = advanced_extractor.extract_intent(user_message, context)

                # Security validation
                security_validator = SecurityValidator(request.user)
                result = security_validator.validate_command(user_message, result)

                # Process command with security and permission checks
                processor = CommandProcessor(request.user)
                result = processor.process_command(result)
                print("result inside else condition>>>>>>>>>>>", result)

            # Sanitize filters if present
            if 'filters' in result:
                result['filters'] = InputSanitizer.sanitize_filters(result['filters'])

            # Determine action type based on intent
            action = self._determine_action(result)
            print("action >>>>>>>>>>>", action)

            # Build response
            # Enhanced response building with confidence thresholding
            confidence = result.get('confidence', 0.0)

            # Confidence thresholding - if below 0.8, ask for clarification
            if confidence < 0.8 and result.get('intent') not in ['reply', 'error']:
                response_data = {
                    'action': 'reply',
                    'url': None,
                    'prefill_data': {},
                    'message': self._generate_clarification_message(result, user_message),
                    'intent': 'clarification',
                    'confidence': confidence,
                    'filters': {},
                    'sort_by': None,
                    'order': 'asc',
                    'suggestions': self._get_suggestions_for_intent(result.get('intent'))
                }
            else:
                response_data = {
                    'action': action,
                    'url': result.get('redirect_url') or result.get('url'),
                    'prefill_data': result.get('prefill_data', {}),
                    'message': result.get('message', 'Command processed.'),
                    'intent': result.get('intent', 'unknown'),
                    'confidence': confidence,
                    'filters': result.get('filters', {}),
                    'sort_by': result.get('sort_by'),
                    'order': result.get('order', 'asc')
                }

            # Calculate processing time
            processing_time = time.time() - start_time

            # Save to chat history
            try:
                ChatHistory.objects.create(
                    user=request.user,
                    message=user_message,
                    response=response_data,
                    intent=result.get('intent', 'unknown'),
                    confidence=result.get('confidence', 0.0),
                    processing_time=processing_time
                )
            except Exception as e:
                logger.warning(f"Failed to save chat history: {e}")

            return JsonResponse(response_data)
            
        except json.JSONDecodeError:
            return JsonResponse({
                'action': 'reply',
                'message': 'Invalid request format. Please try again.',
                'intent': 'error',
                'confidence': 0.0
            }, status=400)
            
        except Exception as e:
            logger.error(f"Error processing AI command: {e}")
            return JsonResponse({
                'action': 'reply',
                'message': 'Sorry, I encountered an error processing your request. Please try again.',
                'intent': 'error',
                'confidence': 0.0
            }, status=500)
    
    def _determine_action(self, result):
        """Determine the appropriate action based on the intent result."""
        intent = result.get('intent', 'reply')
        redirect_url = result.get('redirect_url')

        if intent in ['search', 'navigate', 'fill_form', 'run_action'] and redirect_url:
            return 'redirect'
        elif intent == 'export':
            return 'download'
        elif intent == 'import' and redirect_url:
            return 'redirect'
        elif intent == 'sort' or intent == 'search':
            return 'ajax_update'
        else:
            return 'reply'

    def _generate_clarification_message(self, result, user_message):
        """Generate a clarification message when confidence is low."""
        intent = result.get('intent', 'unknown')
        module = result.get('module', 'general')

        clarifications = {
            'search': f"I think you want to search for something. Do you want me to open the {module.title()} page?",
            'fill_form': f"I think you want to create or fill a form. Do you want me to open the {module.title()} creation form?",
            'run_action': f"I think you want to run an action. Do you want me to open the {module.title()} dashboard?",
            'export': f"I think you want to export data. Do you want me to open the {module.title()} export page?",
            'navigate': f"I think you want to navigate somewhere. Do you want me to open the {module.title()} page?",
        }

        return clarifications.get(intent, f"I'm not sure what you want to do with '{user_message}'. Could you be more specific?")

    def _get_suggestions_for_intent(self, intent):
        """Get suggestions for clarification based on intent."""
        suggestions = {
            'search': [
                "Show all employees",
                "View attendance records",
                "List leave requests",
                "Display payroll data"
            ],
            'fill_form': [
                "Apply leave for John",
                "Create new employee",
                "Add attendance record",
                "Open leave request form"
            ],
            'run_action': [
                "Run payroll for sales team",
                "Generate attendance report",
                "Process leave requests",
                "Export employee data"
            ],
            'navigate': [
                "Go to dashboard",
                "Open employee page",
                "Navigate to reports",
                "Show settings"
            ]
        }

        return suggestions.get(intent, [
            "Show all employees",
            "Apply leave",
            "View attendance",
            "Go to dashboard"
        ])


@login_required
@require_http_methods(["GET"])
def chat_history(request):
    """Get user's chat history."""
    try:
        history = ChatHistory.objects.filter(user=request.user)[:20]  # Last 20 messages
        
        history_data = []
        for chat in history:
            try:
                response_data = json.loads(chat.response)
            except json.JSONDecodeError:
                response_data = {'message': chat.response}
            
            history_data.append({
                'id': chat.id,
                'message': chat.message,
                'response': response_data,
                'intent': chat.intent,
                'confidence': chat.confidence,
                'created_at': chat.created_at.isoformat()
            })
        
        return JsonResponse({
            'history': history_data,
            'count': len(history_data)
        })
        
    except Exception as e:
        logger.error(f"Error fetching chat history: {e}")
        return JsonResponse({
            'error': 'Failed to fetch chat history',
            'history': [],
            'count': 0
        }, status=500)


@login_required
@require_http_methods(["POST"])
@csrf_exempt
def clear_chat_history(request):
    """Clear user's chat history."""
    try:
        deleted_count = ChatHistory.objects.filter(user=request.user).delete()[0]
        return JsonResponse({
            'message': f'Cleared {deleted_count} chat messages.',
            'success': True
        })
    except Exception as e:
        logger.error(f"Error clearing chat history: {e}")
        return JsonResponse({
            'error': 'Failed to clear chat history',
            'success': False
        }, status=500)


# Function-based view for backward compatibility
@csrf_exempt
@require_http_methods(["POST"])
def ai_command(request):
    """Function-based view wrapper for AICommandView."""
    import logging
    logger = logging.getLogger(__name__)
    logger.info(f"AI Command view called - User authenticated: {request.user.is_authenticated}")

    # Check if user is authenticated for API calls
    if not request.user.is_authenticated:
        logger.info("User not authenticated, returning 401")
        return JsonResponse({
            'success': False,
            'error': 'Authentication required',
            'message': 'Please login to use the AI assistant',
            'action': 'redirect',
            'url': '/login/'
        }, status=401)

    logger.info("User authenticated, processing request")
    view = AICommandView()
    return view.post(request)


# Test endpoint to check URL routing
@csrf_exempt
def test_endpoint(request):
    """Simple test endpoint to verify URL routing is working."""
    return JsonResponse({
        'status': 'success',
        'message': 'AI Assistant URL routing is working!',
        'method': request.method,
        'path': request.path
    })


@login_required
def chat_history(request):
    """Display chat history for the current user."""
    from django.shortcuts import render
    from django.core.paginator import Paginator

    chat_history = ChatHistory.objects.filter(
        user=request.user
    ).order_by('-created_at')

    paginator = Paginator(chat_history, 20)  # Show 20 chats per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    return render(request, 'ai_assistant/chat_history.html', {
        'page_obj': page_obj,
        'chat_history': page_obj,
    })


@login_required
@require_http_methods(["POST"])
def clear_chat_history(request):
    """Clear chat history for the current user."""
    try:
        deleted_count = ChatHistory.objects.filter(user=request.user).delete()[0]
        return JsonResponse({
            'success': True,
            'message': f'Cleared {deleted_count} chat entries.',
            'deleted_count': deleted_count
        })
    except Exception as e:
        logger.error(f"Error clearing chat history: {e}")
        return JsonResponse({
            'success': False,
            'message': 'Failed to clear chat history.'
        }, status=500)
