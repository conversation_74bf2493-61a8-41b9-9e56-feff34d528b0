{% load i18n %}
<div class="oh-modal__dialog-header">
  <h2 class="oh-modal__dialog-title" id="shiftRequestsExportLavel">{% trans 'Export Shift Requests' %}</h2>
  <button class="oh-modal__close" aria-label="Close"><ion-icon name="close-outline"></ion-icon></button>
</div>
<div class="oh-modal__dialog-body" id="shiftRequestsExportModalBody">
  <form action="{% url 'shift-request-info-export' %}" method="get" id="shiftRequestsExportForm"
        onsubmit="event.stopPropagation();$(this).parents().find('.oh-modal--show').last().toggleClass('oh-modal--show');"
        class="oh-profile-section"
    >
    {% csrf_token %}
    <div class="oh-dropdown__filter-body">
      <div class="oh-accordion">
        <div class="oh-accordion-header">
          {% trans 'Excel columns' %}
        </div>
        <div class="oh-accordion-body">
          <div class="row">
            <div class="col-sm-12 col-md-12 col-lg-6">
              <div class="oh-input-group">
                <label class="oh-label"><input type="checkbox" id="select-all-fields" /> {% trans 'Select All' %}</label>
              </div>
            </div>
          </div>
          <div class="row">
            {% for field in export_fields.selected_fields %}
              <div class="col-sm-12 col-md-12 col-lg-4">
                <div class="oh-input-group">
                  <label class="oh-label">{{ field }}</label>
                </div>
              </div>
            {% endfor %}
          </div>
        </div>
      </div>
      <div class="oh-accordion">
        <div class="oh-accordion-header">
          {% trans 'Work Info' %}
        </div>
        <div class="oh-accordion-body">
          <div class="row">
            <div class="col-sm-12 col-md-12 col-lg-6">
              <div class="oh-input-group">
                <label class="oh-label">{% trans 'Employee' %}</label>
                {{ export_filter.form.employee_id }}
              </div>
              <div class="oh-input-group">
                <label class="oh-label">{% trans 'Job Position' %}</label>
                {{ export_filter.form.employee_id__employee_work_info__job_position_id }}
              </div>
              <div class="oh-input-group">
                <label class="oh-label">{% trans 'Shift' %}</label>
                {{ export_filter.form.employee_id__employee_work_info__shift_id }}
              </div>
              <div class="oh-input-group">
                <label class="oh-label">{% trans 'Company' %}</label>
                {{ export_filter.form.employee_id__employee_work_info__company_id }}
              </div>
              <div class="oh-input-group">
                <label class="oh-label">{% trans 'Is Active' %}?</label>
                {{ export_filter.form.employee_id__is_active }}
              </div>
            </div>
            <div class="col-sm-12 col-md-12 col-lg-6">
              <div class="oh-input-group">
                <label class="oh-label">{% trans 'Department' %}</label>
                {{ export_filter.form.employee_id__employee_work_info__department_id }}
              </div>
              <div class="oh-input-group">
                <label class="oh-label">{% trans 'Job Role' %}</label>
                {{ export_filter.form.employee_id__employee_work_info__job_role_id }}
              </div>
              <div class="oh-input-group">
                <label class="oh-label">{% trans 'Work Type' %}</label>
                {{ export_filter.form.employee_id__employee_work_info__work_type_id }}
              </div>
              <div class="oh-input-group">
                <label class="oh-label">{% trans 'Reporting Manager' %}</label>
                {{ export_filter.form.employee_id__employee_work_info__reporting_manager_id }}
              </div>
              <div class="oh-input-group">
                <label class="oh-label">{% trans 'Gender' %}</label>
                {{ export_filter.form.employee_id__gender }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="oh-accordion">
        <div class="oh-accordion-header">
          {% trans 'Shift Request' %}
        </div>
        <div class="oh-accordion-body">
          <div class="row">
            <div class="col-sm-12 col-md-12 col-lg-6">
              <div class="oh-input-group">
                <label class="oh-label">{% trans 'Requested Date' %}</label>
                {{ export_filter.form.requested_date }}
              </div>
              <div class="oh-input-group">
                <label class="oh-label">{% trans 'Approved' %}?</label>
                {{ export_filter.form.approved }}
              </div>
              <div class="oh-input-group">
                <label class="oh-label">{% trans 'Canceled' %}?</label>
                {{ export_filter.form.canceled }}
              </div>
            </div>
            <div class="col-sm-12 col-md-12 col-lg-6">
              <div class="oh-input-group">
                <label class="oh-label">{% trans 'Requested Shift' %}</label>
                {{ export_filter.form.shift_id }}
              </div>
              <div class="oh-input-group">
                <label class="oh-label">{% trans 'Previous Shift' %}</label>
                {{ export_filter.form.previous_shift_id }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="oh-accordion">
        <div class="oh-accordion-header">
          {% trans 'Advanced' %}
        </div>
        <div class="oh-accordion-body">
          <div class="row">
            <div class="col-sm-12 col-md-12 col-lg-6">
              <div class="oh-input-group">
                <label class="oh-label">{% trans 'Requested Date From' %}</label>
                {{ export_filter.form.requested_date__gte }}
              </div>
            </div>
            <div class="col-sm-12 col-md-12 col-lg-6">
              <div class="oh-input-group">
                <label class="oh-label">{% trans 'Requested Date Till' %}</label>
                {{ export_filter.form.requested_date__lte }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="oh-modal__dialog-footer p-0 pt-4">
      <button type="submit" class="oh-btn oh-btn--secondary oh-btn--shadow">{% trans 'Export' %}</button>
    </div>
  </form>
</div>
