#!/usr/bin/env python
"""
Test script for AI Assistant functionality

This script tests the AI assistant without requiring <PERSON><PERSON><PERSON> to be running.
"""

import os
import sys
import django
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'eaglora.settings')
django.setup()

from django.contrib.auth.models import User
from ai_assistant.models import AIAssistantSettings, ChatHistory
from utils.intent_extractor import extract_command_intent


def test_intent_extraction():
    """Test the intent extraction functionality."""
    print("🧠 Testing Intent Extraction...")
    
    test_cases = [
        "Show all employees in Marketing department",
        "Apply leave for <PERSON> from July 10 to 12",
        "Export attendance data for June",
        "Run payroll for sales team",
        "Sort employees by highest salary",
        "Get payroll of Sethu",
        "Open dashboard",
    ]
    
    for test_case in test_cases:
        result = extract_command_intent(test_case)
        print(f"\n📝 Input: '{test_case}'")
        print(f"   Intent: {result['intent']}")
        print(f"   Module: {result['module']}")
        print(f"   Confidence: {result['confidence']:.2f}")
        print(f"   Message: {result['message']}")
        if result['filters']:
            print(f"   Filters: {result['filters']}")


def test_ai_settings():
    """Test AI assistant settings."""
    print("\n⚙️  Testing AI Assistant Settings...")
    
    # Get or create settings
    settings = AIAssistantSettings.get_settings()
    print(f"   Model: {settings.ollama_model}")
    print(f"   Temperature: {settings.temperature}")
    print(f"   Max Tokens: {settings.max_tokens}")
    print(f"   LangChain Enabled: {settings.enable_langchain}")
    print(f"   Active: {settings.is_active}")


def test_chat_history():
    """Test chat history functionality."""
    print("\n💬 Testing Chat History...")
    
    # Get or create a test user
    user, created = User.objects.get_or_create(
        username='test_user',
        defaults={'email': '<EMAIL>'}
    )
    
    if created:
        print("   Created test user")
    
    # Create a test chat entry
    test_response = {
        'action': 'redirect',
        'url': '/employee/employee-view/',
        'message': 'Showing all employees',
        'intent': 'search',
        'confidence': 0.8
    }
    
    chat = ChatHistory.objects.create(
        user=user,
        message='Show all employees',
        response=test_response,
        intent='search',
        confidence=0.8,
        processing_time=0.5
    )
    
    print(f"   Created chat entry: {chat}")
    print(f"   Response message: {chat.get_response_message()}")
    
    # Clean up
    chat.delete()
    if created:
        user.delete()


def test_api_endpoint():
    """Test the AI assistant API endpoint."""
    print("\n🌐 Testing API Endpoint...")
    
    from django.test import Client
    from django.contrib.auth.models import User
    
    # Create test client
    client = Client()
    
    # Create test user and login
    user = User.objects.create_user(
        username='api_test_user',
        password='testpass123',
        email='<EMAIL>'
    )
    
    client.login(username='api_test_user', password='testpass123')
    
    # Test API endpoint
    test_data = {
        'message': 'Show all employees in HR department',
        'context': {
            'current_page': '/dashboard/',
            'current_module': 'dashboard'
        }
    }
    
    response = client.post(
        '/ai/command/',
        data=json.dumps(test_data),
        content_type='application/json'
    )
    
    print(f"   Status Code: {response.status_code}")
    
    if response.status_code == 200:
        response_data = json.loads(response.content)
        print(f"   Action: {response_data.get('action')}")
        print(f"   Message: {response_data.get('message')}")
        print(f"   Intent: {response_data.get('intent')}")
        print(f"   Confidence: {response_data.get('confidence')}")
    else:
        print(f"   Error: {response.content}")
    
    # Clean up
    user.delete()


def main():
    """Run all tests."""
    print("🚀 AI Assistant Test Suite")
    print("=" * 50)
    
    try:
        test_intent_extraction()
        test_ai_settings()
        test_chat_history()
        test_api_endpoint()
        
        print("\n✅ All tests completed successfully!")
        print("\n🎉 Your AI Assistant is ready to use!")
        print("\nNext steps:")
        print("1. Visit http://127.0.0.1:8000/ in your browser")
        print("2. Login to your HRMS account")
        print("3. Look for the floating AI assistant button")
        print("4. Click it and try commands like:")
        print("   - 'Show all employees'")
        print("   - 'Apply leave for John'")
        print("   - 'Export attendance data'")
        print("   - 'Run payroll'")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
