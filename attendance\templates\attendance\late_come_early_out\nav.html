{% load i18n %} {% load basefilters %}
<section class="oh-wrapper oh-main__topbar " x-data="{searchShow: false}">
    <div class="oh-main__titlebar oh-main__titlebar--left">
        <h1 class="oh-main__titlebar-title fw-bold">
            <a href="{% url 'late-come-early-out-view' %}" class='text-dark'>
                {% trans "Late Come/Early Out" %}
            </a>
        </h1>

        <a class="oh-main__titlebar-search-toggle" role="button" aria-label="Toggle Search"
            @click="searchShow = !searchShow">
            <ion-icon name="search-outline" class="oh-main__titlebar-serach-icon"></ion-icon>
        </a>
    </div>
    <form hx-get='{% url "late-come-early-out-search" %}' id="filterForm" hx-swap='innerHTML' hx-target='#report-container'>
        <div class="oh-main__titlebar oh-main__titlebar--right">
            <div class="oh-input-group oh-input__search-group"
                :class="searchShow ? 'oh-input__search-group--show' : ''">
                <ion-icon name="search-outline" class="oh-input-group__icon oh-input-group__icon--left"></ion-icon>
                <input type="text" class="oh-input oh-input__icon" aria-label="Search Input" id="report-search"
                    name='search' placeholder="{% trans 'Search' %}" onkeyup="$('.filterButton')[0].click()" />
            </div>
            <div class="oh-main__titlebar-button-container">
                <div class="oh-dropdown" x-data="{open: false}">
                    <button class="oh-btn ml-2" @click="open = !open" onclick="event.preventDefault()">
                        <ion-icon name="filter" class="mr-1"></ion-icon>{% trans "Filter" %}<div id="filterCount"></div>
                    </button>
                    <div class="oh-dropdown__menu oh-dropdown__menu--right oh-dropdown__filter p-4" x-show="open"
                        @click.outside="open = false" style="display: none;">
                        {% include 'attendance/late_come_early_out/late_come_early_out_filters.html' %}
                    </div>
                </div>
                <div class="oh-dropdown" x-data="{open: false}">
                    <button class="oh-btn ml-2" @click="open = !open" onclick="event.preventDefault()">
                        <ion-icon name="library-outline" class="mr-1"></ion-icon>{% trans "Group By" %}
                        <div id="filterCount"></div>
                    </button>
                    <div class="oh-dropdown__menu oh-dropdown__menu--right oh-dropdown__filter p-4" x-show="open"
                        @click.outside="open = false" style="display: none">
                        <div class="oh-accordion">
                            <label for="id_field">{% trans "Group By" %}</label>
                            <div class="row">
                                <div class="col-sm-12 col-md-12 col-lg-6">
                                    <div class="oh-input-group">
                                        <label class="oh-label" for="id_field">{% trans "Field" %}</label>
                                    </div>
                                </div>
                                <div class="col-sm-12 col-md-12 col-lg-6">
                                    <div class="oh-input-group">
                                        <select class="oh-select mt-1 w-100" id="id_field" name="field"
                                            class="select2-selection select2-selection--single">
                                            {% for field in gp_fields %}
                                                <option value="{{ field.0 }}">{% trans field.1 %}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="oh-dropdown ml-2" x-data="{open: false}">
                <button class="oh-btn oh-btn--dropdown" @click="open = !open" @click.outside="open = false" onclick="event.preventDefault()">
                    {% trans "Actions" %}
                </button>
                <div class="oh-dropdown__menu oh-dropdown__menu--right" x-show="open" style="display: none">
                    <ul class="oh-dropdown__items">
                        {% if perms.attendance.change_attendancelatecomeearlyout or request.user|is_reportingmanager %}
                            <li class="oh-dropdown__item">
                                <a href="#" class="oh-dropdown__link" id="attendance-info-export"
                                    data-toggle="oh-modal-toggle" data-target="#objectCreateModal"
                                    hx-get="{% url 'late-come-early-out-info-export' %}"
                                    hx-target="#objectCreateModalTarget">{% trans "Export" %}</a>
                            </li>
                        {% endif %}
                        {% if perms.attendance.delete_attendancelatecomeearlyout %}
                            <li class="oh-dropdown__item">
                                <a href="#" id="lateComeBulkDelete" data-action="delete" class="oh-dropdown__link oh-dropdown__link--danger">{% trans "Delete" %}</a>
                            </li>
                        {% endif %}
                    </ul>
                </div>
            </div>
        </div>
    </form>
    <script>
        $('#attendance-search').keydown(function (e) {
            var val = $(this).val();
            $('.pg').attr('hx-vals', `{"search":${val}}`);
        });
        $(document).ready(function () {
            $('#id_field').on('change', function () {
                $('.filterButton')[0].click();
            })
        })
    </script>
</section>
