<div class="row">
  <div class="col-12 col-sm-12 col-md-12 col-lg-12">
    <div class="oh-card p-4">
      <div class="oh-card__body">
        <div class="oh-sticky-table oh-sticky-table--no-highlight">
          <div class="oh-sticky-table__table">
            <div class="oh-sticky-table__thead">
              <div class="oh-sticky-table__tr">
                <div class="oh-sticky-table__th">{% trans "Groups" %}</div>
                <div class="oh-sticky-table__th">{% trans "Employees" %}</div>
              </div>
            </div>
            <div class="oh-sticky-table__tbody">
              {% for gp in groups %}
              <div
                class="oh-sticky-table__tr oh-permission-table__tr oh-permission-table--collapsed"
              >
                <div class="oh-sticky-table__sd oh-permission-table--toggle">
                  <div class="d-flex align-items-center">
                    <button class="oh-permission-table__collapse me-2">
                      <ion-icon
                        class="oh-permission-table__collapse-down"
                        title="{% trans 'Reveal' %}"
                        name="chevron-down-outline"
                      ></ion-icon>
                      <ion-icon
                        class="oh-permission-table__collapse-up"
                        title="{% trans 'Collapse' %}"
                        name="chevron-up-outline"
                      ></ion-icon>
                    </button>
                    <span class="oh-permission-table__user">{{gp}}.</span>
                  </div>
                </div>
                <div class="oh-sticky-table__td">
                  <span class="perm-count"
                    >{{gp.user_set.all|length}} {% trans "Employees" %}</span
                  >
                  {% for user in gp.user_set.all %}
                  <span
                    class="oh-user-panel oh-collapse-panel"
                    data-type="user"
                  >
                    <div class="oh-profile oh-profile--md">
                      <div class="oh-profile__avatar mr-1">
                        <img
                          src="{{user.employee_get.get_avatar}}"
                          class="oh-profile__image"
                          alt=""
                        />
                      </div>
                      <span class="oh-profile__name oh-text--dark"
                        >{{user.employee_get}}</span
                      >
                    </div>
                    {% if perms.change_group %}
                    <form
                      action="{% url 'group-remove-user' user.id gp.id %}"
                      method="post"
                    >
                      {% csrf_token %}
                      <button
                        type="submit"
                        class="oh-user-panel__remove"
                        title="{% trans 'Remove' %}"
                      >
                        <ion-icon name="close-outline"></ion-icon>
                      </button>
                    </form>
                    {% endif %}
                  </span>
                  {% endfor %}
                </div>
              </div>
              {% endfor %}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

{% if groups %}
<div class="oh-pagination">
  <span class="oh-pagination__page">
    {% trans "Page" %} {{ groups.number }} {% trans "of" %} {{ groups.paginator.num_pages }}.
  </span>
  <nav class="oh-pagination__nav">
    <div class="oh-pagination__input-container me-3">
      <span class="oh-pagination__label me-1">{% trans "Page" %}</span>
      <input
        type="number"
        name="page"
        class="oh-pagination__input"
        value="{{groups.number}}"
        hx-get="{% url 'user-group-assign-view' %}?{{pd}}"
        hx-target="#view-container"
      />
      <span class="oh-pagination__label"
        >{% trans "of" %} {{employees.paginator.num_pages}}</span
      >
    </div>
    <ul class="oh-pagination__items">
      {% if groups.has_previous %}
      <li class="oh-pagination__item oh-pagination__item--wide">
        <a
          hx-target="#view-container"
          hx-get="{% url 'user-group-assign-view' %}?{{pd}}&page=1"
          class="oh-pagination__link"
          >{% trans "First" %}</a
        >
      </li>
      <li class="oh-pagination__item oh-pagination__item--wide">
        <a
          hx-target="#view-container"
          hx-get="{% url 'user-group-assign-view' %}?{{pd}}&page={{ groups.previous_page_number }}"
          class="oh-pagination__link"
          >{% trans "Previous" %}</a
        >
      </li>
      {% endif %} {% if groups.has_next %}
      <li class="oh-pagination__item oh-pagination__item--wide">
        <a
          hx-target="#view-container"
          hx-get="{% url 'user-group-assign-view' %}?{{pd}}&page={{ groups.next_page_number }}"
          class="oh-pagination__link"
          >{% trans "Next" %}</a
        >
      </li>
      <li class="oh-pagination__item oh-pagination__item--wide">
        <a
          hx-target="#view-container"
          hx-get="{% url 'user-group-assign-view' %}?{{pd}}&page={{ groups.paginator.num_pages }}"
          class="oh-pagination__link"
          >{% trans "Last" %}</a
        >
      </li>
      {% endif %}
    </ul>
  </nav>
</div>
{% endif %}
