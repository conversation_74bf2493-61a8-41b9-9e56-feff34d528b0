{% load i18n %}
{% load static %}
  <div class="oh-dropdown__filter-body">
    <div class="oh-accordion">
      <div class="oh-accordion-header">{% trans "Rotating Work Type" %}</div>
      <div class="oh-accordion-body">
        <div class="row">
          <div class="col-sm-12 col-md-12 col-lg-6">
            <div class="oh-input-group">
              <label class="oh-label">{% trans "Employee" %}</label>
              {{f.form.employee_id}}
            </div>
            <div class="oh-input-group">
              <label class="oh-label">{% trans "Based On" %}</label>
              {{f.form.based_on}}
            </div>
            <div class="oh-input-group">
              <label class="oh-label">{% trans "Current Shift" %}</label>
              {{f.form.current_work_type}}
            </div>
          </div>
          <div class="col-sm-12 col-md-12 col-lg-6">
            <div class="oh-input-group">
              <label class="oh-label">{% trans "Rotating Shift" %}</label>
              {{f.form.rotating_work_type_id}}
            </div>
            <div class="oh-input-group">
              <label class="oh-label">{% trans "Next Switch" %}</label>
              {{f.form.next_change_date}}
            </div>
            <div class="oh-input-group">
              <label class="oh-label">{% trans "Next Shift" %}</label>
              {{f.form.next_work_type}}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="oh-accordion">
      <div class="oh-accordion-header">{% trans "Work Info" %}</div>
      <div class="oh-accordion-body">
        <div class="row">
          <div class="col-sm-12 col-md-12 col-lg-6">
            <div class="oh-input-group">
              <label class="oh-label">{% trans "Company" %}</label>
              {{f.form.employee_id__employee_work_info__company_id}}
            </div>
            <div class="oh-input-group">
              <label class="oh-label">{% trans "Department" %}</label>
              {{f.form.employee_id__employee_work_info__department_id}}
            </div>
            <div class="oh-input-group">
              <label class="oh-label">{% trans "Shift" %}</label>
              {{f.form.employee_id__employee_work_info__shift_id}}
            </div>
          </div>
          <div class="col-sm-12 col-md-12 col-lg-6">
            <div class="oh-input-group">
              <label class="oh-label">{% trans "Reporting Manager" %}</label>
              {{f.form.employee_id__employee_work_info__reporting_manager_id}}
            </div>
            <div class="oh-input-group">
              <label class="oh-label">{% trans "Job Position" %}</label>
              {{f.form.employee_id__employee_work_info__job_position_id}}
            </div>
            <div class="oh-input-group">
              <label class="oh-label">{% trans "Work Type" %}</label>
              {{f.form.employee_id__employee_work_info__work_type_id}}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="oh-accordion">
      <div class="oh-accordion-header">{% trans "Advanced" %}</div>
      <div class="oh-accordion-body">
        <div class="row">
          <div class="col-sm-12 col-md-12 col-lg-6">
            <div class="oh-input-group">
              <div class="oh-input-group">
                <label class="oh-label">{% trans "Is Active" %}?</label>
                {{f.form.is_active}}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="oh-dropdown__filter-footer">
    <button
      class="oh-btn oh-btn--secondary oh-btn--small w-100 filterButton"
      id="employeeFilter"
    >
      {% trans "Filter" %}
    </button>
  </div>

<script src="{% static '/base/filter.js' %}"></script>
