{% load i18n %}
<script>
  function highlightFinger(fingers, handId){
    var hand = $("#" + handId)
    var designFingers = hand.find("[data-finger]")
    fingers.forEach(fingerId => {
      $.each(designFingers, function (indexInArray, finger) {
        dataFingerId = $(finger).attr("data-finger")
        var temp1 = toString(dataFingerId)
        var temp2 = toString(fingerId)
        if (temp2 === temp1) {
          $("#"+handId).find(`[data-finger=${fingerId}]`).attr("fill","green")
        }
      });
    });
  }
</script>
<div class="oh-sticky-table">
    <div class="oh-sticky-table__table oh-table--sortable">
      <div class="oh-sticky-table__thead">
        <div class="oh-sticky-table__tr">
          <div class="oh-sticky-table__th" style = "width:10px;">
            <div class="centered-div">
							<input type="checkbox" class="oh-input oh-input__checkbox all-bio-employee" title="Select All" data-device = "{{device_id}}"
									id = "allBioEmployee"/>
						</div>
          </div>
          <div class="oh-sticky-table__th">{% trans "Employee" %}</div>
          <div class="oh-sticky-table__th">{% trans "User ID" %}</div>
          <div class="oh-sticky-table__th">{% trans "Badge ID" %}</div>
          <div class="oh-sticky-table__th" style="width: 100px;">{% trans "Fingerprint" %}</div>
          <div class="oh-sticky-table__th">{% trans "Work Email" %}</div>
          <div class="oh-sticky-table__th">{% trans "Phone" %}</div>
          <div class="oh-sticky-table__th">{% trans "Job Position" %}</div>
          <div class="oh-sticky-table__th oh-sticky-table__right">{% trans "Actions" %}</div>
        </div>
      </div>
      {% for employee in employees %}
      <div class="oh-sticky-table__tbody ui-sortable">
        <div class="oh-sticky-table__tr ui-sortable-handle">
          <div class="oh-sticky-table__sd">
            <div class="centered-div">
              <input
              type="checkbox"
              id="{{employee.user_id}}"
              class="form-check-input all-bio-employee-row"
              />
            </div>
          </div>
          <div class="oh-sticky-table__td">{{employee.employee}}</div>
          <div class="oh-sticky-table__td">
           {{employee.user_id}} {{device_id.machine_ip}} {{device_id.port}}
          </div>
          <div class="oh-sticky-table__td">
           {{employee.badge_id}}

          </div>
          <div class="oh-sticky-table__td">
           <div class="d-flex justify-content-center text-align-center">
            <div  id="hand{{employee.user_id}}">
              <svg width="52" height="42" viewBox="0 0 52 42" fill="none" xmlns="http://www.w3.org/2000/svg" style="width: 60px;">
                <path d="M48.0984 41.1622H36.6278V32.6916L31.0249 27.1328V24.1769L35.0396 22.6989V16.8975H51.7161V29.3386L48.0984 32.6916V41.1622Z"fill="#bdc3c7" stroke="#7f8c8d" stroke-width="0.5"/>
                <rect x="44.3086"data-hand-target="hand{{employee.badge_id}}" data-finger="8" y="2.5" width="4.22059" height="14.1471"fill="#bdc3c7" stroke="#7f8c8d" stroke-width="0.5"/>
                <rect x="35.0439"data-hand-target="hand{{employee.badge_id}}" data-finger="6" y="3.86719" width="4.88235" height="12.7794"fill="#bdc3c7" stroke="#7f8c8d" stroke-width="0.5"/>
                <rect x="39.897" data-hand-target="hand{{employee.badge_id}}"data-finger="7" y="0.25" width="4.44118" height="16.3971"fill="#bdc3c7" stroke="#7f8c8d" stroke-width="0.5"/>
                <rect x="48.4561"data-hand-target="hand{{employee.badge_id}}" data-finger="9" y="7.35254" width="3.29412" height="9.29412"fill="#bdc3c7" stroke="#7f8c8d" stroke-width="0.5"/>
                <rect x="28.3124"data-hand-target="hand{{employee.badge_id}}" data-finger="5" y="15.2505" width="3.90269" height="9.07403" transform="rotate(-17.0853 28.3124 15.2505)"fill="#bdc3c7" stroke="#7f8c8d" stroke-width="0.5"/>
                <rect x="35.3237" y="16.1914" width="16.1029" height="1.67647"fill="#bdc3c7"/>
                <path d="M31.8825 25.8092L31.1987 23.5372L31.0884 23.2283L34.1766 22.1475L34.5957 23.6475L34.9707 24.221L35.059 24.6622L31.8825 25.8092Z"fill="#bdc3c7"/>
                <path d="M31.0884 23.2275L31.309 24.3746L31.5075 24.4849L32.3678 24.9261L31.7281 23.2275H31.0884Z"fill="#bdc3c7"/>

                <path d="M3.90157 41.1622H15.3722V32.6916L20.9751 27.1328V24.1769L16.9604 22.6989V16.8975H0.28392V29.3386L3.90157 32.6916V41.1622Z"fill="#bdc3c7" stroke="#7f8c8d" stroke-width="0.5"/>
                <rect x="-0.25" y="0.25"data-hand-target="hand{{employee.badge_id}}" data-finger="1" width="4.22059" height="14.1471" transform="matrix(-1 0 0 1 7.44141 2.25)"fill="#bdc3c7" stroke="#7f8c8d" stroke-width="0.5"/>
                <rect x="-0.25" y="0.25"data-hand-target="hand{{employee.badge_id}}" data-finger="3" width="4.88235" height="12.7794" transform="matrix(-1 0 0 1 16.7061 3.61719)"fill="#bdc3c7" stroke="#7f8c8d" stroke-width="0.5"/>
                <rect x="-0.25" y="0.25"data-hand-target="hand{{employee.badge_id}}" data-finger="2" width="4.44118" height="16.3971" transform="matrix(-1 0 0 1 11.853 0)"fill="#bdc3c7" stroke="#7f8c8d" stroke-width="0.5"/>
                <rect x="-0.25" y="0.25"data-hand-target="hand{{employee.badge_id}}" data-finger="0" width="3.29412" height="9.29412" transform="matrix(-1 0 0 1 3.29395 7.10254)"fill="#bdc3c7" stroke="#7f8c8d" stroke-width="0.5"/>
                <rect x="-0.312416" y="0.165518" data-hand-target="hand{{employee.badge_id}}" data-finger="4" width="3.90269" height="9.07403" transform="matrix(-0.955868 -0.293795 -0.293795 0.955868 23.4376 15.0005)"fill="#bdc3c7" stroke="#7f8c8d" stroke-width="0.5"/>
                <rect width="16.1029" height="1.67647" transform="matrix(-1 0 0 1 16.6763 16.1914)"fill="#bdc3c7"/>
                <path d="M20.1175 25.8092L20.8013 23.5372L20.9116 23.2283L17.8234 22.1475L17.4043 23.6475L17.0293 24.221L16.941 24.6622L20.1175 25.8092Z"fill="#bdc3c7"/>
                <path d="M20.9116 23.2275L20.691 24.3746L20.4925 24.4849L19.6322 24.9261L20.2719 23.2275H20.9116Z"fill="#bdc3c7"/>
              </svg>
            </div>
           </div>
          <script>
              highlightFinger({{employee.finger|safe}},"hand{{employee.user_id}}")
          </script>
          </div>
          <div class="oh-sticky-table__td">
          {{employee.work_email}}
          </div>
          <div class="oh-sticky-table__td">
          {{employee.phone}}

          </div>
          <div class="oh-sticky-table__td">
            {{employee.job_position}}
          </div>
          <div class="oh-sticky-table__td oh-sticky-table__right">
            <div class="oh-btn-group">
              {% comment %} <a
                onclick="event.stopPropagation();"
                class="oh-btn oh-btn--light-bkg w-100"
                href="{% url 'update-employee' employee.id %}"
                title='{% trans "Update" %}'
              >
                <ion-icon
                  name="create-outline"
                  role="img"
                  class="md hydrated"
                  style="color: blue"
                  aria-label="create outline"
                ></ion-icon>
              </a>
              {% endcomment %}
              <a
                class="oh-btn oh-btn--danger-outline oh-btn--light-bkg w-100"
                href="{% url 'delete-biometric-user' uid=employee.uid device_id=device_id %}"
                onclick = "event.preventDefault();event.stopPropagation(); confirm(`{% trans 'Do you want to delete this user from the biometric device?' %}`)"
                title='{% trans "Delete" %}'
              >
                <ion-icon
                  name="trash-outline"
                  role="img"
                  class="md hydrated"
                  aria-label="trash outline"
                ></ion-icon>
              </a>
            </div>
          </div>
        </div>
      </div>
      {% endfor %}
    </div>
  </div>

<div class="oh-pagination">
    <span
      class="oh-pagination__page"
      >
      {% trans "Page" %} {{ employees.number }} {% trans "of" %} {{ employees.paginator.num_pages }}.
      </span
    >
    <nav class="oh-pagination__nav">
      <div class="oh-pagination__input-container me-3">
        <span class="oh-pagination__label me-1">{% trans "Page" %}</span>
        <input
          type="number"
          name="page"
          class="oh-pagination__input"
          value="{{employees.number}}"
          hx-get="{% url 'search-employee-in-device' %}?{{pd}}&view=list&device={{device_id}}"
          hx-target="#section"
          min="1"
        />
        <span class="oh-pagination__label">{% trans "of" %} {{employees.paginator.num_pages}}</span>
      </div>
      <ul class="oh-pagination__items">
        {% if employees.has_previous %}
        <li class="oh-pagination__item oh-pagination__item--wide">
          <a hx-target='#section' hx-get="{% url 'search-employee-in-device' %}?{{pd}}&view=list&page=1&device={{device_id}}" class="oh-pagination__link">{% trans "First" %}</a>
        </li>
        <li class="oh-pagination__item oh-pagination__item--wide">
          <a hx-target='#section' hx-get="{% url 'search-employee-in-device' %}?{{pd}}&view=list&page={{ employees.previous_page_number }}&device={{device_id}}" class="oh-pagination__link">{% trans "Previous" %}</a>
        </li>
        {% endif %}
        {% if employees.has_next %}
        <li class="oh-pagination__item oh-pagination__item--wide">
          <a hx-target='#section' hx-get="{% url 'search-employee-in-device' %}?{{pd}}&view=list&page={{ employees.next_page_number }}&device={{device_id}}" class="oh-pagination__link">{% trans "Next" %}</a>
        </li>
        <li class="oh-pagination__item oh-pagination__item--wide">
          <a hx-target='#section' hx-get="{% url 'search-employee-in-device' %}?{{pd}}&view=list&page={{ employees.paginator.num_pages }}&device={{device_id}}" class="oh-pagination__link">{% trans "Last" %}</a>
        </li>
        {% endif %}

      </ul>
    </nav>
</div>
