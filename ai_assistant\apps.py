"""
AI Assistant App Configuration
"""

from django.apps import AppConfig
from django.utils.translation import gettext_lazy as _


class AiAssistantConfig(AppConfig):
    """Configuration for AI Assistant app."""
    
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'ai_assistant'
    verbose_name = _('AI Assistant')
    
    def ready(self):
        """Initialize the app when Django starts."""
        # Import signals if any
        try:
            from . import signals
        except ImportError:
            pass
        
        # Register URL patterns
        self._register_urls()
    
    def _register_urls(self):
        """Register AI assistant URLs with the main URL configuration."""
        try:
            from django.urls import include, path
            from eaglora.eaglora_settings import APPS
            from eaglora.urls import urlpatterns
            
            # Add this app to the APPS list if not already present
            if "ai_assistant" not in APPS:
                APPS.append("ai_assistant")
            
            # Add URL patterns
            ai_assistant_urls = path("ai/", include("ai_assistant.urls"))
            if ai_assistant_urls not in urlpatterns:
                urlpatterns.append(ai_assistant_urls)
                
        except ImportError:
            # Handle case where eaglora settings are not available
            pass
