{% load i18n %}
<div class="row">
	<div class="col-12 col-sm-12 col-md-12 col-lg-12">
		<div class="oh-card p-4">
			<div class="oh-card__body">
				<div class="oh-sticky-table oh-sticky-table--no-highlight">
					<div class="oh-sticky-table__table">
						<div class="oh-sticky-table__thead">
							<div class="oh-sticky-table__tr">
								<div class="oh-sticky-table__th">{% trans "Shift" %}</div>
								<div class="oh-sticky-table__th" style="width:236px;">{% trans "Days" %}</div>
							</div>
						</div>

						<div class="oh-sticky-table__tbody">
							{% for shift in shifts %}
								<div class="oh-sticky-table__tr oh-permission-table__tr oh-permission-table--collapsed"
									data-label="Schedule" data-count="{{shift.employeeshiftschedule_set.all|length}}">
									<div class="oh-sticky-table__sd oh-permission-table--toggle">
										<div class="d-flex align-items-center">
											<button class="oh-permission-table__collapse me-2"
												onclick="updateUserPanelCount(this);">
												<ion-icon class="oh-permission-table__collapse-down"
													title="{% trans 'Reveal' %}" name="chevron-down-outline"></ion-icon>
												<ion-icon class="oh-permission-table__collapse-up"
													title="{% trans 'Collapse' %}" name="chevron-up-outline"></ion-icon>
											</button>
											<span class="oh-permission-table__user">{{shift}}</span>
										</div>
									</div>
									<div class="oh-sticky-table__td">
										<span class="oh-permission-count">
											{{shift.employeeshiftschedule_set.all|length}}
											{% trans "Schedules" %}
										</span>
										{% for schedule in shift.employeeshiftschedule_set.all %}
											<div id="shiftSchedule{{schedule.id}}">
												<span class="oh-user-panel oh-collapse-panel">
													<div class="oh-profile oh-profile--md">
														<div class="oh-profile__avatar mr-1">
															<img src="https://ui-avatars.com/api/?name={{schedule.day}}&background=random" class="oh-profile__image" alt="Baby C." />
														</div>
														<span class="oh-profile__name oh-text--dark">{{schedule.day|capfirst}}</span>
													</div>
													<div>
														<div class="d-flex">
															{% if perms.base.change_employeeshiftschedule %}
																<div class="m-2">
																	<a data-toggle="oh-modal-toggle"
																		data-target="#objectUpdateModal"
																		hx-get="{% url 'employee-shift-schedule-update' schedule.id %}"
																		hx-target="#objectUpdateModalTarget"
																		class="oh-user-panel__remove" title="{% trans 'Edit' %}">
																		<ion-icon name="pencil-outline"></ion-icon>
																	</a>
																</div>
															{% endif %}
															{% if perms.base.delete_employeeshiftschedule %}
																<div class="mt-3 mr-2">
																	<form
																		hx-confirm="{% trans 'Are you sure you want to delete this schedule?' %}"
																		hx-post="{% url 'employee-shift-schedule-delete' schedule.id %}"
																		hx-on-htmx-after-request="reloadMessage(this);"
																		hx-target="#shiftSchedule{{schedule.id}}" hx-swap="outerHTML">
																		<button type="submit" style="
																							background: none;
																							color: inherit;
																							border: none;
																							padding: 0;
																							font: inherit;
																							cursor: pointer;
																							outline: none;
																						" title="{% trans 'Remove' %}">
																			<ion-icon name="close" class="text-danger"></ion-icon>
																		</button>
																		{% csrf_token %}
																	</form>
																</div>
															{% endif %}
														</div>
													</div>
												</span>
											</div>
										{% endfor %}
									</div>
								</div>
							{% endfor %}
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<script>

</script>
