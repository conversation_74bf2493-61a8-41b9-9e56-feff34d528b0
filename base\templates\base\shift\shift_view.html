{% load i18n %}{% load static %}
<div id='ohMessages'></div>
{% if shifts %}
	<div class="oh-sticky-table">
		<div class="oh-sticky-table__table oh-table--sortable">
			<div class="oh-sticky-table__thead">
				<div class="oh-sticky-table__tr">
					<div class="oh-sticky-table__th">{% trans "Shift" %}</div>
					<div class="oh-sticky-table__th">{% trans "Weekly Full Time" %}</div>
					<div class="oh-sticky-table__th">{% trans "Full Time" %}</div>
					<div class="oh-sticky-table__th">{% trans "Grace Time" %}</div>
					{% if perms.base.change_employeeshift or perms.base.delete_employeeshift %}
						<div class="oh-sticky-table__th">{% trans "Actions" %}</div>
					{% endif %}
				</div>
			</div>
			<div class="oh-sticky-table__tbody">
				{% for shift in shifts %}
					<div class="oh-sticky-table__tr" draggable="true" id="shiftTr{{shift.id}}">
						<div class="oh-sticky-table__td">{{shift}}</div>
						<div class="oh-sticky-table__td">{{shift.weekly_full_time}}</div>
						<div class="oh-sticky-table__td">{{shift.full_time}}</div>
						{% if shift.grace_time_id %}
							<div class="oh-sticky-table__td">
								<div class="d-flex">
									{{shift.grace_time_id}}
									<div class="oh-switch ml-2">
										{% if perms.attendance.change_gracetime %}
											<input type="checkbox" id="isActive" data-id="{{shift.grace_time_id.id}}"
												class="oh-switch__checkbox" {% if shift.grace_time_id.is_active %} checked {% endif %}
												onchange="updateGracetimeIsActivate(this)">
										{% else %}
											<input type="checkbox" id="isActive" class="oh-switch__checkbox" {% if shift.grace_time_id.is_active %} checked {% endif %} disabled>
										{% endif %}
									</div>
								</div>
							</div>
						{% else %}
							<div class="oh-sticky-table__td">{% trans "Nil" %}</div>
						{% endif %}
						{% if perms.base.change_employeeshift or perms.base.delete_employeeshift %}
							<div class="oh-sticky-table__td">
								<div class="oh-btn-group">
									{% if perms.base.change_employeeshift %}
										<a data-toggle="oh-modal-toggle" data-target="#shiftModal"
											hx-get="{% url 'employee-shift-update' shift.id %}" hx-target="#shiftForm" type="button"
											class="oh-btn oh-btn--light-bkg w-50" title="{% trans 'Edit' %}">
											<ion-icon name="create-outline"></ion-icon></a>
									{% endif %}
									{% if perms.base.delete_employeeshift %}
										<form hx-confirm="{% trans 'Are you sure you want to delete this shift?' %}"
											hx-post="{% url 'employee-shift-delete' shift.id %}" hx-target="#shiftTr{{shift.id}}"
											hx-swap="outerHTML" class="w-50" hx-on-htmx-after-request="reloadMessage(this);">
											{% csrf_token %}
											<button type="submit" class="oh-btn oh-btn--danger-outline oh-btn--light-bkg w-100"
												title="{% trans 'Remove' %}">
												<ion-icon name="trash-outline"></ion-icon>
											</button>
										</form>
									{% endif %}
								</div>
							</div>
						{% endif %}
					</div>
				{% endfor %}
			</div>
		</div>
	</div>
{% else %}
	<div style="display: flex; flex-direction: column; justify-content: center; align-items: center; height: 100%;">
		<img style="display: block; width: 15%; margin: 20px auto; filter: opacity(0.5);"
			src="{% static 'images/ui/employee_shift.png' %}" class="" alt="Page not found. 404." />
		<h5 class="oh-404__subtitle">{% trans "There is no employee shifts at this moment." %}</h5>
	</div>
{% endif %}


<script>
	function getCookie(name) {
		let cookieValue = null;
		if (document.cookie && document.cookie !== '') {
			const cookies = document.cookie.split(';');
			for (let i = 0; i < cookies.length; i++) {
				const cookie = cookies[i].trim();
				// Does this cookie string begin with the name we want?
				if (cookie.substring(0, name.length + 1) === (name + '=')) {
					cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
					break;
				}
			}
		}
		return cookieValue;
	}

	function updateGracetimeIsActivate(checkbox) {
		// Using jQuery to check if the checkbox is checked
		var isChecked = $(checkbox).prop('checked');
		var gracetimeId = $(checkbox).data('id');
		$.ajax({
			type: "POST",
			url: "{% url 'update-isactive-gracetime' %}",
			data: { 'isChecked': isChecked, 'gracetimeId': gracetimeId, 'csrfmiddlewaretoken': getCookie('csrftoken') },
			dataType: "json",
			success: function (response) {
				$("#ohMessages").append(`
                <div class="oh-alert-container">
                    <div class="oh-alert oh-alert--animated oh-alert--${response.type}">
                    ${response.message}
                    </div>
                </div>`);
				duration = 1500;
			}
		});
	}
</script>
