"""
Intent Extraction Module for AI Assistant

This module uses spaCy and rule-based patterns to extract intents, filters,
sorting preferences, and actions from natural language user commands.
"""

import re
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import calendar

# Import spaCy when available, fallback to basic processing if not
try:
    import spacy
    SPACY_AVAILABLE = True
    # Try to load English model, fallback to basic if not available
    try:
        nlp = spacy.load("en_core_web_sm")
    except OSError:
        # If model not available, use basic spacy
        nlp = spacy.blank("en")
except ImportError:
    SPACY_AVAILABLE = False
    nlp = None


class IntentExtractor:
    """Extract intents and parameters from natural language commands."""
    
    def __init__(self):
        # Intent patterns - order matters for priority
        self.intent_patterns = {
            'import': [
                r'\b(import|upload|load)\b.*\b(employees?|staff|data|list)\b.*\b(from|excel|csv|file)\b',
                r'\b(employees?|staff|data)\b.*\b(import|upload|load)\b',
                r'\b(import)\b.*\b(from)\b.*\b(excel|csv|file)\b',
            ],
            'export': [
                r'\b(export|download|save)\b.*\b(employee|staff|attendance|payroll|report|data|list)\b',
                r'\b(download|export)\b.*\b(report|data|list|attendance|employee)\b',
            ],
            'fill_form': [
                r'\b(open|create|fill|complete)\b.*\b(leave)\b.*\b(form|application|request)\b',
                r'\b(leave|vacation)\b.*\b(form|application|request)\b',
                r'\b(apply for|request)\b.*\b(leave|vacation)\b',
                r'\b(create|open)\b.*\b(form|application)\b',
            ],
            'run_action': [
                r'\b(run|execute|process|start)\b.*\b(payroll|salary|payment)\b',
                r'\b(generate|create|make)\b.*\b(payroll|salary|report)\b',
                r'\b(payroll)\b.*\b(for|of)\b',
            ],
            'search': [
                r'\b(show|find|search|get|list|display)\b.*\b(employees?|staff|workers?|people)\b',
                r'\b(show|list)\b.*\b(all)\b.*\b(employees?|staff)\b',
                r'\b(employees?|staff|workers?)\b.*\b(in|from|at)\b',
                r'\b(who|which)\b.*\b(employees?|staff|workers?)\b',
                r'\b(find)\b.*\b(employees?|staff)\b.*\b(from|in)\b',
            ],
            'sort': [
                r'\b(sort|order|arrange)\b.*\b(by|according)\b',
                r'\b(ascending|descending|asc|desc)\b',
                r'\b(sorted|ordered)\b.*\b(by)\b',
            ],
            'navigate': [
                r'\b(go to|navigate|show)\b.*\b(page|section|dashboard|settings)\b',
                r'\b(take me to|redirect to)\b',
                r'\b(go to)\b.*\b(dashboard|settings)\b',
            ],
        }
        
        # Location/Department patterns
        self.location_patterns = [
            r'\b(in|from|at)\s+([A-Za-z\s]+?)(?:\s|$)',
            r'\b(location|city|office|branch)[\s:]+([A-Za-z\s]+?)(?:\s|$)',
        ]

        # Exclude these from location detection
        self.location_exclusions = ['excel', 'csv', 'file', 'form', 'report', 'data']
        
        # Department patterns
        self.department_patterns = [
            r'\b(department|dept|team|division)[\s:]+([A-Za-z\s]+?)(?:\s|$)',
            r'\b(sales|marketing|hr|finance|it|engineering|operations|admin)\b',
        ]
        
        # Date patterns
        self.date_patterns = [
            r'\b(january|february|march|april|may|june|july|august|september|october|november|december)\b',
            r'\b(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)\b',
            r'\b(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{2,4})\b',
            r'\b(\d{1,2})\s+(january|february|march|april|may|june|july|august|september|october|november|december)\b',
        ]
        
        # Sorting patterns
        self.sort_patterns = {
            'salary': r'\b(salary|pay|wage|compensation)\b',
            'joining_date': r'\b(joining|hire|start)\s+(date|day)\b',
            'name': r'\b(name|alphabetical)\b',
            'department': r'\b(department|dept|team)\b',
            'experience': r'\b(experience|exp|years)\b',
        }
        
        # Order patterns
        self.order_patterns = {
            'asc': r'\b(ascending|asc|low to high|smallest|earliest)\b',
            'desc': r'\b(descending|desc|high to low|largest|latest|newest)\b',
        }

    def extract_command_intent(self, text: str) -> Dict[str, Any]:
        """
        Extract structured command intent from natural language text.
        
        Args:
            text: Natural language command from user
            
        Returns:
            Dictionary with extracted intent, module, filters, sort_by, order, 
            redirect_url, and prefill_data
        """
        text_lower = text.lower().strip()
        
        # Initialize result structure
        result = {
            'intent': 'reply',  # default intent
            'module': 'general',
            'filters': {},
            'sort_by': None,
            'order': 'asc',
            'redirect_url': None,
            'prefill_data': {},
            'message': 'I understand you want help, but I need more specific information.',
            'confidence': 0.0
        }
        
        # Extract intent
        intent, confidence = self._extract_intent(text_lower)
        result['intent'] = intent
        result['confidence'] = confidence
        
        # Extract module/context
        result['module'] = self._extract_module(text_lower)
        
        # Extract filters
        result['filters'] = self._extract_filters(text_lower)
        
        # Extract sorting preferences
        sort_by, order = self._extract_sorting(text_lower)
        result['sort_by'] = sort_by
        result['order'] = order
        
        # Generate appropriate response based on intent
        result = self._generate_response(result, text)
        
        return result
    
    def _extract_intent(self, text: str) -> tuple:
        """Extract the primary intent from text."""
        max_confidence = 0.0
        detected_intent = 'reply'

        for intent, patterns in self.intent_patterns.items():
            for pattern in patterns:
                if re.search(pattern, text, re.IGNORECASE):
                    confidence = 0.8  # Base confidence for pattern match

                    # Boost confidence for more specific patterns
                    if intent == 'search' and re.search(r'\b(employee|staff|worker)\b', text, re.IGNORECASE):
                        confidence = 0.9

                    if confidence > max_confidence:
                        max_confidence = confidence
                        detected_intent = intent

        return detected_intent, max_confidence
    
    def _extract_module(self, text: str) -> str:
        """Extract the HRMS module context."""
        if re.search(r'\b(employees?|staff|workers?|people)\b', text):
            return 'employee'
        elif re.search(r'\b(leave|vacation|holiday|time off)\b', text):
            return 'leave'
        elif re.search(r'\b(payroll|salary|pay|wage|compensation)\b', text):
            return 'payroll'
        elif re.search(r'\b(attendance|check in|check out|present|absent)\b', text):
            return 'attendance'
        elif re.search(r'\b(recruitment|hiring|candidate|interview)\b', text):
            return 'recruitment'
        else:
            return 'general'
    
    def _extract_filters(self, text: str) -> Dict[str, Any]:
        """Extract filter parameters from text."""
        filters = {}
        
        # Extract location
        for pattern in self.location_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                location = match.group(2).strip()
                if location and len(location) > 1 and location.lower() not in self.location_exclusions:
                    filters['location'] = location.title()
                    break
        
        # Extract department
        for pattern in self.department_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                if len(match.groups()) > 1:
                    dept = match.group(2).strip()
                else:
                    dept = match.group(0).strip()
                if dept and len(dept) > 1:
                    filters['department'] = dept.title()
                    break
        
        # Extract date/month
        for pattern in self.date_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                filters['date_filter'] = match.group(0)
                break
        
        return filters
    
    def _extract_sorting(self, text: str) -> tuple:
        """Extract sorting field and order."""
        sort_by = None
        order = 'asc'  # default
        
        # Find sorting field
        for field, pattern in self.sort_patterns.items():
            if re.search(pattern, text, re.IGNORECASE):
                sort_by = field
                break
        
        # Find sorting order
        for order_type, pattern in self.order_patterns.items():
            if re.search(pattern, text, re.IGNORECASE):
                order = order_type
                break
        
        return sort_by, order
    
    def _generate_response(self, result: Dict[str, Any], original_text: str) -> Dict[str, Any]:
        """Generate appropriate response based on extracted intent."""
        intent = result['intent']
        module = result['module']
        filters = result['filters']
        original_lower = original_text.lower()

        if intent == 'search' and module == 'employee':
            # Employee search with advanced filtering
            url_params = []
            if filters.get('location'):
                url_params.append(f"work_info__location__icontains={filters['location']}")
            if filters.get('department'):
                url_params.append(f"employee_work_info__department__department__icontains={filters['department']}")

            # Add sorting if specified
            if result.get('sort_by'):
                sort_field = self._map_sort_field(result['sort_by'])
                if sort_field:
                    order_prefix = '-' if result.get('order') == 'desc' else ''
                    url_params.append(f"ordering={order_prefix}{sort_field}")

            query_string = '&'.join(url_params)
            base_url = '/employee/employee-view/'
            result['redirect_url'] = f"{base_url}?{query_string}" if query_string else base_url

            # Generate descriptive message
            location_msg = f" in {filters.get('location')}" if filters.get('location') else ""
            dept_msg = f" from {filters.get('department')} department" if filters.get('department') else ""
            sort_msg = f" sorted by {result.get('sort_by', '')}" if result.get('sort_by') else ""
            result['message'] = f"Searching for employees{location_msg}{dept_msg}{sort_msg}..."

        elif intent == 'fill_form' and module == 'leave':
            # Enhanced leave form handling
            result['redirect_url'] = '/leave/request-creation'

            # Extract employee name if mentioned
            employee_match = re.search(r'\b(?:for|to)\s+([A-Za-z]+(?:\s+[A-Za-z]+)?)\b', original_lower)
            if employee_match:
                result['prefill_data']['employee_name'] = employee_match.group(1).title()

            # Extract date ranges
            date_from_match = re.search(r'\bfrom\s+([A-Za-z]+\s+\d{1,2}|\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})', original_lower)
            date_to_match = re.search(r'\bto\s+([A-Za-z]+\s+\d{1,2}|\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})', original_lower)

            if date_from_match:
                result['prefill_data']['start_date'] = date_from_match.group(1)
            if date_to_match:
                result['prefill_data']['end_date'] = date_to_match.group(1)

            result['message'] = "Opening leave application form..."

        elif intent == 'run_action' and module == 'payroll':
            # Enhanced payroll processing
            if 'run payroll' in original_lower or 'process payroll' in original_lower:
                result['redirect_url'] = '/payroll/view-contract/'
            elif 'generate payroll' in original_lower:
                result['redirect_url'] = '/payroll/contract-create'
            else:
                result['redirect_url'] = '/payroll/'

            if filters.get('department'):
                result['prefill_data']['department'] = filters['department']
            if filters.get('date_filter'):
                result['prefill_data']['period'] = filters['date_filter']

            dept_msg = f" for {filters.get('department')} department" if filters.get('department') else ""
            period_msg = f" for {filters.get('date_filter')}" if filters.get('date_filter') else ""
            result['message'] = f"Opening payroll processing{dept_msg}{period_msg}..."

        elif intent == 'export':
            # Enhanced export functionality
            if module == 'employee':
                result['redirect_url'] = '/employee/employee-export'
                result['message'] = "Preparing employee data export..."
            elif module == 'leave':
                result['redirect_url'] = '/leave/leave-requests-info-export'
                result['message'] = "Preparing leave requests export..."
            elif module == 'attendance':
                # Check if attendance export exists, fallback to attendance view
                result['redirect_url'] = '/attendance/attendance-export'
                result['message'] = "Preparing attendance report export..."
            elif module == 'payroll':
                result['redirect_url'] = '/payroll/payslip-export'  # Assuming this exists
                result['message'] = "Preparing payroll export..."
            else:
                result['message'] = "Export functionality available. Please specify what data you'd like to export (employees, leave, attendance, payroll)."

        elif intent == 'import':
            # Enhanced import functionality
            if module == 'employee':
                result['redirect_url'] = '/employee/employee-import'
                result['message'] = "Opening employee data import..."
            elif module == 'leave':
                result['redirect_url'] = '/leave/leave-import'  # If exists
                result['message'] = "Opening leave data import..."
            elif 'work info' in original_lower:
                result['redirect_url'] = '/employee/work-info-import'
                result['message'] = "Opening work information import..."
            else:
                result['message'] = "Import functionality available. Please specify what data you'd like to import (employees, work info, etc.)."

        elif intent == 'navigate':
            # Enhanced navigation
            result = self._handle_navigation(result, original_lower)

        elif intent == 'sort':
            # Handle sorting requests
            result = self._handle_sorting(result, original_lower)

        else:
            # Enhanced default response with suggestions
            result['message'] = self._generate_help_message(original_lower)

        return result

    def _map_sort_field(self, sort_by: str) -> str:
        """Map natural language sort fields to database fields."""
        field_mapping = {
            'salary': 'employee_work_info__basic_salary',
            'joining_date': 'employee_work_info__date_joining',
            'name': 'first_name',
            'department': 'employee_work_info__department__department',
            'experience': 'employee_work_info__experience',
        }
        return field_mapping.get(sort_by, '')

    def _handle_navigation(self, result: Dict[str, Any], text: str) -> Dict[str, Any]:
        """Handle navigation-specific commands."""
        if 'dashboard' in text:
            result['redirect_url'] = '/employee/dashboard'
            result['message'] = "Opening dashboard..."
        elif 'settings' in text:
            result['redirect_url'] = '/settings'
            result['message'] = "Opening settings..."
        elif 'employee' in text and 'view' in text:
            result['redirect_url'] = '/employee/employee-view/'
            result['message'] = "Opening employee list..."
        elif 'leave' in text and ('view' in text or 'list' in text):
            result['redirect_url'] = '/leave/request-view/'
            result['message'] = "Opening leave requests..."
        elif 'payroll' in text:
            result['redirect_url'] = '/payroll/'
            result['message'] = "Opening payroll section..."
        else:
            result['message'] = "Please specify which page you'd like to navigate to (dashboard, settings, employees, leave, payroll)."

        return result

    def _handle_sorting(self, result: Dict[str, Any], text: str) -> Dict[str, Any]:
        """Handle sorting-specific commands."""
        # This would typically trigger an AJAX update to re-sort current page
        result['action'] = 'ajax_update'
        result['sort_by'] = result.get('sort_by', 'name')
        result['order'] = result.get('order', 'asc')
        result['message'] = f"Sorting by {result['sort_by']} in {result['order']}ending order..."

        return result

    def _generate_help_message(self, text: str) -> str:
        """Generate contextual help message based on user input."""
        if any(word in text for word in ['help', 'what', 'how', 'can you']):
            return """I can help you with:

🔍 **Search**: "Show employees in Chennai", "Find staff from HR department"
📝 **Forms**: "Open leave form", "Create leave request for John from July 10 to 12"
💰 **Payroll**: "Run payroll for sales team", "Process payroll for June"
📊 **Export**: "Export employee data", "Download attendance report"
📥 **Import**: "Import employee list", "Upload work info from Excel"
🧭 **Navigate**: "Go to dashboard", "Open settings", "Show payroll section"

Try asking me something specific!"""
        else:
            return "I understand you want help, but I need more specific information. Try commands like 'Show all employees' or 'Open leave form'."


def extract_command_intent(text: str) -> Dict[str, Any]:
    """
    Main function to extract command intent from natural language text.
    
    Args:
        text: Natural language command from user
        
    Returns:
        Dictionary with extracted intent, module, filters, sort_by, order, 
        redirect_url, and prefill_data
    """
    extractor = IntentExtractor()
    return extractor.extract_command_intent(text)
