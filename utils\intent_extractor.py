"""
Intent Extractor for AI Assistant

This module provides basic intent extraction functionality as a fallback
when <PERSON><PERSON><PERSON><PERSON> is not available.
"""

import re
import json
from typing import Dict, Any, List
from datetime import datetime


def extract_command_intent(text: str) -> Dict[str, Any]:
    """
    Extract intent from user command text using rule-based patterns.

    Args:
        text: User's natural language command

    Returns:
        Dictionary containing intent, module, filters, etc.
    """
    text_lower = text.lower().strip()

    # Default response structure
    result = {
        'intent': 'reply',
        'module': 'general',
        'filters': {},
        'sort_by': None,
        'order': 'asc',
        'prefill_data': {},
        'redirect_url': None,
        'message': 'I understand your request.',
        'confidence': 0.5
    }

    # Intent patterns
    intent_patterns = {
        'search': [
            r'\b(show|list|find|search|get|view|display)\b',
            r'\b(all|employees|staff|people)\b',
            r'\b(attendance|leave|payroll)\b.*\b(records|data)\b',
        ],
        'fill_form': [
            r'\b(create|add|apply|fill|open)\b.*\b(form|request)\b',
            r'\b(apply|create)\b.*\b(leave|employee)\b',
            r'\b(new|add)\b.*\b(employee|staff)\b',
        ],
        'run_action': [
            r'\b(run|process|execute|start)\b.*\b(payroll|report)\b',
            r'\b(generate|create)\b.*\b(report|payslip)\b',
        ],
        'export': [
            r'\b(export|download|save)\b',
            r'\b(download|export)\b.*\b(data|file|report)\b',
        ],
        'navigate': [
            r'\b(go to|navigate|open)\b.*\b(page|dashboard|section)\b',
            r'\b(dashboard|home|main)\b',
        ],
        'sort': [
            r'\b(sort|order)\b.*\b(by|ascending|descending)\b',
            r'\b(highest|lowest|top|bottom)\b.*\b(salary|name)\b',
        ]
    }

    # Module patterns
    module_patterns = {
        'employee': [
            r'\b(employee|staff|worker|people|person)\b',
            r'\b(emp|hr|human resource)\b',
        ],
        'leave': [
            r'\b(leave|vacation|holiday|time off|absence)\b',
            r'\b(pto|sick leave|annual leave)\b',
        ],
        'attendance': [
            r'\b(attendance|present|absent|clock|punch)\b',
            r'\b(check in|check out|time sheet)\b',
        ],
        'payroll': [
            r'\b(payroll|salary|pay|wage|payslip)\b',
            r'\b(compensation|earnings|deduction)\b',
        ],
        'recruitment': [
            r'\b(recruitment|hiring|candidate|job|interview)\b',
            r'\b(applicant|position|vacancy)\b',
        ]
    }

    # Detect intent
    for intent, patterns in intent_patterns.items():
        for pattern in patterns:
            if re.search(pattern, text_lower):
                result['intent'] = intent
                result['confidence'] += 0.2
                break
        if result['intent'] != 'reply':
            break

    # Detect module
    for module, patterns in module_patterns.items():
        for pattern in patterns:
            if re.search(pattern, text_lower):
                result['module'] = module
                result['confidence'] += 0.1
                break
        if result['module'] != 'general':
            break

    # Extract filters
    result['filters'] = extract_filters(text_lower)
    if result['filters']:
        result['confidence'] += 0.1

    # Extract sorting preferences
    sort_info = extract_sorting(text_lower)
    if sort_info:
        result.update(sort_info)
        result['confidence'] += 0.1

    # Generate appropriate response message
    result['message'] = generate_response_message(result, text)

    # Ensure confidence doesn't exceed 1.0
    result['confidence'] = min(1.0, result['confidence'])

    return result


def extract_filters(text: str) -> Dict[str, Any]:
    """Extract filter criteria from text."""
    filters = {}

    # Department filters
    dept_match = re.search(r'\b(in|from|department|dept)\s+([a-zA-Z\s]+?)(?:\s|$)', text)
    if dept_match:
        filters['department'] = dept_match.group(2).strip()

    # Name filters
    name_patterns = [
        r'\b(for|of|named|employee)\s+([A-Z][a-zA-Z\s]+?)(?:\s|$)',
        r'\b([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\b(?=\s|$)',
    ]
    for pattern in name_patterns:
        name_match = re.search(pattern, text)
        if name_match:
            potential_name = name_match.group(-1).strip()
            # Simple validation for names (at least 2 characters, starts with capital)
            if len(potential_name) >= 2 and potential_name[0].isupper():
                filters['name'] = potential_name
                break

    # Location filters
    location_match = re.search(r'\b(in|at|location|office)\s+([a-zA-Z\s]+?)(?:\s|$)', text)
    if location_match:
        filters['location'] = location_match.group(2).strip()

    # Date filters
    date_patterns = [
        r'\b(from|since|after)\s+(\d{1,2}[/-]\d{1,2}[/-]\d{2,4})\b',
        r'\b(for|in)\s+(january|february|march|april|may|june|july|august|september|october|november|december)\b',
        r'\b(for|in)\s+(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)\b',
        r'\b(this|last|next)\s+(month|week|year)\b',
    ]
    for pattern in date_patterns:
        date_match = re.search(pattern, text, re.IGNORECASE)
        if date_match:
            filters['date'] = date_match.group(2)
            break

    # Status filters
    status_patterns = [
        r'\b(pending|approved|rejected|active|inactive)\b',
        r'\b(status|state)\s+([a-zA-Z]+)\b',
    ]
    for pattern in status_patterns:
        status_match = re.search(pattern, text)
        if status_match:
            # Use the last group if multiple groups, otherwise use the whole match
            if status_match.lastindex and status_match.lastindex > 0:
                filters['status'] = status_match.group(status_match.lastindex)
            else:
                filters['status'] = status_match.group(0)
            break

    return filters


def extract_sorting(text: str) -> Dict[str, Any]:
    """Extract sorting preferences from text."""
    sort_info = {}

    # Sort field patterns
    sort_patterns = {
        'name': r'\b(name|alphabetical|alpha)\b',
        'salary': r'\b(salary|pay|wage|compensation)\b',
        'date': r'\b(date|time|recent|latest)\b',
        'department': r'\b(department|dept)\b',
        'experience': r'\b(experience|exp|seniority)\b',
    }

    for field, pattern in sort_patterns.items():
        if re.search(pattern, text):
            sort_info['sort_by'] = field
            break

    # Sort order patterns
    if re.search(r'\b(highest|descending|desc|top|max|largest)\b', text):
        sort_info['order'] = 'desc'
    elif re.search(r'\b(lowest|ascending|asc|bottom|min|smallest)\b', text):
        sort_info['order'] = 'asc'

    return sort_info


def generate_response_message(result: Dict[str, Any], original_text: str) -> str:
    """Generate an appropriate response message based on the extracted intent."""
    intent = result['intent']
    module = result['module']
    filters = result['filters']

    if intent == 'search':
        if module == 'employee':
            if filters.get('department'):
                return f"Searching for employees in {filters['department']} department"
            elif filters.get('name'):
                return f"Searching for employee: {filters['name']}"
            else:
                return "Showing all employees"
        elif module == 'leave':
            return "Showing leave requests"
        elif module == 'attendance':
            return "Showing attendance records"
        elif module == 'payroll':
            return "Showing payroll information"
        else:
            return "Searching for the requested information"

    elif intent == 'fill_form':
        if module == 'leave':
            return "Opening leave request form"
        elif module == 'employee':
            return "Opening employee creation form"
        else:
            return "Opening the requested form"

    elif intent == 'run_action':
        if module == 'payroll':
            return "Opening payroll processing"
        else:
            return "Executing the requested action"

    elif intent == 'export':
        return f"Preparing to export {module} data"

    elif intent == 'navigate':
        return "Navigating to the requested page"

    elif intent == 'sort':
        sort_by = result.get('sort_by', 'name')
        order = result.get('order', 'asc')
        return f"Sorting by {sort_by} in {order}ending order"

    else:
        return "I understand your request and will help you with that."


# Backward compatibility functions
def extract_intent(text: str) -> str:
    """Extract just the intent from text."""
    result = extract_command_intent(text)
    return result['intent']


def extract_module(text: str) -> str:
    """Extract just the module from text."""
    result = extract_command_intent(text)
    return result['module']


def extract_entities(text: str) -> Dict[str, Any]:
    """Extract entities (filters) from text."""
    result = extract_command_intent(text)
    return result['filters']