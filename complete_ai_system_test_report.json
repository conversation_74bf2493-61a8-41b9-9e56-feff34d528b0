{"test_execution": {"timestamp": "2025-07-13T09:21:49.738459", "total_duration": 0, "environment": "development"}, "results": {"natural_language_converter": [{"input": "Show all employees", "success": true, "api_request": {"url": "/api/employee/", "method": "GET", "filters": {}, "search": null, "sort_by": null, "order": "asc", "body": {}, "confidence": 0.8666666666666667, "metadata": {"parsed_intent": "list", "detected_entities": {}, "endpoint_match": 0.3333333333333333, "processing_time": "2025-07-13T09:21:44.738055"}}, "execution_time": 0.0073587894439697266}, {"input": "List employees from HR department", "success": true, "api_request": {"url": "/api/employee/", "method": "GET", "filters": {"department__icontains": "HR"}, "search": null, "sort_by": null, "order": "asc", "body": {}, "confidence": 0.9400000000000001, "metadata": {"parsed_intent": "list", "detected_entities": {"departments": ["hr"]}, "endpoint_match": 0.2, "processing_time": "2025-07-13T09:21:44.739057"}}, "execution_time": 0.0}, {"input": "Create employee <PERSON> <NAME_EMAIL>", "success": true, "api_request": {"url": "/api/employee/", "method": "POST", "filters": {"email": "<EMAIL>"}, "search": null, "sort_by": null, "order": "asc", "body": {"email": "<EMAIL>"}, "confidence": 0.9285714285714286, "metadata": {"parsed_intent": "create", "detected_entities": {"emails": ["<EMAIL>"]}, "endpoint_match": 0.14285714285714285, "processing_time": "2025-07-13T09:21:44.740057"}}, "execution_time": 0.0009996891021728516}, {"input": "Apply leave for Sarah from July 1 to July 5", "success": true, "api_request": {"url": "/api/leave/", "method": "POST", "filters": {}, "search": null, "sort_by": null, "order": "asc", "body": {}, "confidence": 1.0, "metadata": {"parsed_intent": "create", "detected_entities": {"numbers": [1, 5]}, "endpoint_match": 0.1, "processing_time": "2025-07-13T09:21:44.742057"}}, "execution_time": 0.0020003318786621094}, {"input": "Export attendance data", "success": true, "api_request": {"url": "/api/attendance/export/", "method": "GET", "filters": {}, "search": null, "sort_by": null, "order": "asc", "body": {}, "confidence": 0.8666666666666667, "metadata": {"parsed_intent": "export", "detected_entities": {}, "endpoint_match": 0.3333333333333333, "processing_time": "2025-07-13T09:21:44.742568"}}, "execution_time": 0.0}, {"input": "Find employees with highest salary", "success": true, "api_request": {"url": "/api/employee/", "method": "GET", "filters": {}, "search": "employees", "sort_by": "salary", "order": "desc", "body": {}, "confidence": 0.8400000000000001, "metadata": {"parsed_intent": "list", "detected_entities": {}, "endpoint_match": 0.2, "processing_time": "2025-07-13T09:21:44.743631"}}, "execution_time": 0.0}, {"input": "Show leave requests sorted by date", "success": true, "api_request": {"url": "/api/leave/", "method": "GET", "filters": {}, "search": null, "sort_by": "created_at", "order": "asc", "body": {}, "confidence": 0.8333333333333334, "metadata": {"parsed_intent": "list", "detected_entities": {}, "endpoint_match": 0.16666666666666666, "processing_time": "2025-07-13T09:21:44.744585"}}, "execution_time": 0.0}, {"input": "Mark attendance for <PERSON> today", "success": true, "api_request": {"url": "/api/attendance/", "method": "GET", "filters": {}, "search": null, "sort_by": null, "order": "asc", "body": {}, "confidence": 0.9400000000000001, "metadata": {"parsed_intent": "list", "detected_entities": {}, "endpoint_match": 0.2, "processing_time": "2025-07-13T09:21:44.746096"}}, "execution_time": 0.0005133152008056641}, {"input": "Generate payroll report for June", "success": true, "api_request": {"url": "/api/employee/", "method": "GET", "filters": {}, "search": null, "sort_by": null, "order": "asc", "body": {}, "confidence": 0.8800000000000001, "metadata": {"parsed_intent": "export", "detected_entities": {}, "endpoint_match": 0.4, "processing_time": "2025-07-13T09:21:44.747109"}}, "execution_time": 0.001013040542602539}, {"input": "List pending leave applications", "success": true, "api_request": {"url": "/api/leave/", "method": "GET", "filters": {}, "search": null, "sort_by": null, "order": "asc", "body": {}, "confidence": 0.8500000000000001, "metadata": {"parsed_intent": "list", "detected_entities": {}, "endpoint_match": 0.25, "processing_time": "2025-07-13T09:21:44.748104"}}, "execution_time": 0.000995635986328125}], "api_metadata_extractor": false, "api_executor": true, "end_to_end_workflows": [{"workflow": "Show all employees", "success": true, "api_request": {"url": "/api/employee/", "method": "GET", "filters": {}, "search": null, "sort_by": null, "order": "asc", "body": {}, "confidence": 0.8666666666666667, "metadata": {"parsed_intent": "list", "detected_entities": {}, "endpoint_match": 0.3333333333333333, "processing_time": "2025-07-13T09:21:47.280933"}}, "execution_result": {"success": false, "status_code": 404, "headers": {"Date": "Sun, 13 Jul 2025 03:51:49 GMT", "Server": "WSGIServer/0.2 CPython/3.10.11", "Content-Type": "text/html; charset=utf-8", "X-Frame-Options": "SAMEORIGIN", "Vary": "Accept-Language, origin, <PERSON>ie", "Content-Language": "en", "Content-Length": "67801", "X-Content-Type-Options": "nosniff", "Referrer-Policy": "same-origin", "Cross-Origin-Opener-Policy": "same-origin", "Set-Cookie": "sessionid=xfgx90bk3fagl9efznoqdbbg4dr0pyyg; expires=Sun, 27 Jul 2025 03:51:49 GMT; HttpOnly; Max-Age=1209600; Path=/; SameSite=Lax"}, "url": "http://localhost:8000/api/employee/", "data": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta http-equiv=\"content-type\" content=\"text/html; charset=utf-8\">\n  <title>Page not found at /api/employee/</title>\n  <meta name=\"robots\" content=\"NONE,NOARCHIVE\">\n  <style type=\"text/css\">\n    html * { padding:0; margin:0; }\n    body * { padding:10px 20px; }\n    body * * { padding:0; }\n    body { font:small sans-serif; background:#eee; color:#000; }\n    body>div { border-bottom:1px solid #ddd; }\n    h1 { font-weight:normal; margin-bottom:.4em; }\n    h1 span { font-size:60%; color:#666; font-weight:normal; }\n    table { border:none; border-collapse: collapse; width:100%; }\n    td, th { vertical-align:top; padding:2px 3px; }\n    th { width:12em; text-align:right; color:#666; padding-right:.5em; }\n    #info { background:#f6f6f6; }\n    #info ol { margin: 0.5em 4em; }\n    #info ol li { font-family: monospace; }\n    #summary { background: #ffc; }\n    #explanation { background:#eee; border-bottom: 0px none; }\n    pre.exception_value { font-family: sans-serif; color: #575757; font-size: 1.5em; margin: 10px 0 10px 0; }\n  </style>\n</head>\n<body>\n  <div id=\"summary\">\n    <h1>Page not found <span>(404)</span></h1>\n    \n    <table class=\"meta\">\n      <tr>\n        <th>Request Method:</th>\n        <td>GET</td>\n      </tr>\n      <tr>\n        <th>Request URL:</th>\n        <td>http://localhost:8000/api/employee/</td>\n      </tr>\n      \n    </table>\n  </div>\n  <div id=\"info\">\n    \n      <p>\n      Using the URLconf defined in <code>eaglora.urls</code>,\n      Django tried these URL patterns, in this order:\n      </p>\n      <ol>\n        \n          <li>\n            \n                admin/\n                \n            \n          </li>\n        \n          <li>\n            \n                accounts/\n                \n            \n          </li>\n        \n          <li>\n            \n                accounts/\n                \n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                \n                [name='home-page']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                initialize-database\n                [name='initialize-database']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                load-demo-database\n                [name='load-demo-database']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                initialize-database-user\n                [name='initialize-database-user']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                initialize-database-company\n                [name='initialize-database-company']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                initialize-database-department\n                [name='initialize-database-department']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                initialize-department-edit/&lt;int:obj_id&gt;\n                [name='initialize-department-edit']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                initialize-department-delete/&lt;int:obj_id&gt;\n                [name='initialize-department-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                initialize-database-job-position\n                [name='initialize-database-job-position']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                initialize-job-position-edit/&lt;int:obj_id&gt;\n                [name='initialize-job-position-edit']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                initialize-job-position-delete/&lt;int:obj_id&gt;\n                [name='initialize-job-position-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                404\n                [name='404']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                login/\n                [name='login']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                forgot-password\n                [name='forgot-password']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                employee-reset-password\n                [name='employee-reset-password']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                reset-send-success\n                [name='reset-send-success']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                change-password\n                [name='change-password']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                change-username\n                [name='change-username']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                two-factor\n                [name='two-factor']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                send-otp\n                [name='send-otp']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                logout\n                [name='logout']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings\n                [name='settings']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/user-group-create/\n                [name='user-group-create']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/user-group-view/\n                [name='user-group-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/user-group-search/\n                [name='user-group-search']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                user-group-delete/&lt;int:obj_id&gt;/\n                [name='user-group-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                group-permission-remove/&lt;int:pid&gt;/&lt;int:gid&gt;/\n                [name='group-permission-remove']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                user-group-assign-view\n                [name='user-group-assign-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/user-group-assign/\n                [name='user-group-assign']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                group-remove-user/&lt;int:uid&gt;/&lt;int:gid&gt;/\n                [name='group-remove-user']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/employee-permission-assign/\n                [name='employee-permission-assign']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                employee-permission-search\n                [name='permission-search']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                update-user-permission\n                [name='update-user-permission']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                update-group-permission\n                [name='update-group-permission']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                permission-table\n                [name='permission-table']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/mail-server-conf/\n                [name='mail-server-conf']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/mail-server-create-update/\n                [name='mail-server-create-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/mail-server-test-email/\n                [name='mail-server-test-email']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                mail-server-delete\n                [name='mail-server-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                replace-primary-mail\n                [name='replace-primary-mail']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                configuration/view-mail-templates/\n                [name='view-mail-templates']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                view-mail-template/&lt;int:obj_id&gt;/\n                [name='view-mail-template']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                create-mail-template/\n                [name='create-mail-template']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                duplicate-mail-template/&lt;int:obj_id&gt;/\n                [name='duplicate-mail-template']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                delete-mail-template/\n                [name='delete-mail-template']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/company-create/\n                [name='company-create']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/company-view/\n                [name='company-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/company-update/&lt;int:id&gt;/\n                [name='company-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/company-delete/&lt;int:obj_id&gt;/\n                [name='company-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/department-view/\n                [name='department-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/department-creation/\n                [name='department-creation']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/department-update/&lt;int:id&gt;/\n                [name='department-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                department-delete/&lt;int:obj_id&gt;/\n                [name='department-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/job-position-creation/\n                [name='job-position-creation']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/job-position-view/\n                [name='job-position-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/job-position-update/&lt;int:id&gt;/\n                [name='job-position-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                job-position-delete/&lt;int:obj_id&gt;/\n                [name='job-position-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/job-role-create/\n                [name='job-role-create']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/job-role-view/\n                [name='job-role-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/job-role-update/&lt;int:id&gt;/\n                [name='job-role-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                job-role-delete/&lt;int:obj_id&gt;/\n                [name='job-role-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/work-type-view/\n                [name='work-type-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/work-type-create/\n                [name='work-type-create']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/work-type-update/&lt;int:id&gt;/\n                [name='work-type-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                work-type-delete/&lt;int:obj_id&gt;/\n                [name='work-type-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                add-remove-work-type-fields\n                [name='add-remove-work-type-fields']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/rotating-work-type-create/\n                [name='rotating-work-type-create']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/rotating-work-type-view/\n                [name='rotating-work-type-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/rotating-work-type-update/&lt;int:id&gt;/\n                [name='rotating-work-type-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-work-type-delete/&lt;int:obj_id&gt;/\n                [name='rotating-work-type-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                employee/rotating-work-type-assign/\n                [name='rotating-work-type-assign']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-work-type-assign-add\n                [name='rotating-work-type-assign-add']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-work-type-assign-view\n                [name='rotating-work-type-assign-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-work-type-assign-export\n                [name='rotating-work-type-assign-export']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/rotating-work-type-assign-update/&lt;int:id&gt;/\n                [name='rotating-work-type-assign-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-work-type-assign-duplicate/&lt;int:obj_id&gt;/\n                [name='rotating-work-type-assign-duplicate']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-work-type-assign-archive/&lt;int:obj_id&gt;/\n                [name='rotating-work-type-assign-archive']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-work-type-assign-bulk-archive\n                [name='rotating-shift-work-type-bulk-archive']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-work-type-assign-bulk-delete\n                [name='rotating-shift-work-type-bulk-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-work-type-assign-delete/&lt;int:obj_id&gt;/\n                [name='rotating-work-type-assign-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/employee-type-view/\n                [name='employee-type-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/employee-type-create/\n                [name='employee-type-create']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/employee-type-update/&lt;int:id&gt;/\n                [name='employee-type-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                employee-type-delete/&lt;int:obj_id&gt;/\n                [name='employee-type-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/employee-shift-view/\n                [name='employee-shift-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/employee-shift-create/\n                [name='employee-shift-create']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/employee-shift-update/&lt;int:id&gt;/\n                [name='employee-shift-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                employee-shift-delete/&lt;int:obj_id&gt;/\n                [name='employee-shift-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/employee-shift-schedule-view/\n                [name='employee-shift-schedule-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/employee-shift-schedule-create/\n                [name='employee-shift-schedule-create']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/employee-shift-schedule-update/&lt;int:id&gt;/\n                [name='employee-shift-schedule-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                employee-shift-schedule-delete/&lt;int:obj_id&gt;/\n                [name='employee-shift-schedule-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/rotating-shift-create/\n                [name='rotating-shift-create']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                add-remove-shift-fields\n                [name='add-remove-shift-fields']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/rotating-shift-view/\n                [name='rotating-shift-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/rotating-shift-update/&lt;int:id&gt;/\n                [name='rotating-shift-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-shift-delete/&lt;int:obj_id&gt;/\n                [name='rotating-shift-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                employee/rotating-shift-assign/\n                [name='rotating-shift-assign']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-shift-assign-add\n                [name='rotating-shift-assign-add']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-shift-assign-view\n                [name='rotating-shift-assign-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-shift-assign-info-export\n                [name='rotating-shift-assign-info-export']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-shift-assign-info-import\n                [name='rotating-shift-assign-info-import']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/rotating-shift-assign-update/&lt;int:id&gt;/\n                [name='rotating-shift-assign-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-shift-assign-duplicate/&lt;int:obj_id&gt;/\n                [name='rotating-shift-assign-duplicate']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-shift-assign-archive/&lt;int:obj_id&gt;/\n                [name='rotating-shift-assign-archive']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-shift-assign-bulk-archive\n                [name='rotating-shift-assign-bulk-archive']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-shift-assign-bulk-delete\n                [name='rotating-shift-assign-bulk-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-shift-assign-delete/&lt;int:obj_id&gt;/\n                [name='rotating-shift-assign-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                work-type-request\n                [name='work-type-request']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                work-type-request-duplicate/&lt;int:obj_id&gt;/\n                [name='work-type-request-duplicate']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                employee/work-type-request-view/\n                [name='work-type-request-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                work-type-request-info-export\n                [name='work-type-request-info-export']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                work-type-request-search\n                [name='work-type-request-search']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                work-type-request-cancel/&lt;int:id&gt;/\n                [name='work-type-request-cancel']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                work-type-request-bulk-cancel\n                [name='work-type-request-bulk-cancel']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                work-type-request-approve/&lt;int:id&gt;/\n                [name='work-type-request-approve']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                work-type-request-bulk-approve\n                [name='work-type-request-bulk-approve']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                work-type-request-update/&lt;int:work_type_request_id&gt;/\n                [name='work-type-request-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                work-type-request-delete/&lt;int:obj_id&gt;/\n                [name='work-type-request-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                work-type-request-single-view/&lt;int:obj_id&gt;/\n                [name='work-type-request-single-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                work-type-request-bulk-delete\n                [name='work-type-request-bulk-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-request\n                [name='shift-request']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-request-duplicate/&lt;int:obj_id&gt;/\n                [name='shift-request-duplicate']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-request-reallocate\n                [name='shift-request-reallocate']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                update-employee-allocation\n                [name='update-employee-allocation']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                employee/shift-request-view/\n                [name='shift-request-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-request-info-export\n                [name='shift-request-info-export']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-request-search\n                [name='shift-request-search']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-request-details/&lt;int:id&gt;/\n                [name='shift-request-details']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-allocation-request-details/&lt;int:id&gt;/\n                [name='shift-allocation-request-details']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-request-update/&lt;int:shift_request_id&gt;/\n                [name='shift-request-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-allocation-request-update/&lt;int:shift_request_id&gt;/\n                [name='shift-allocation-request-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-request-cancel/&lt;int:id&gt;/\n                [name='shift-request-cancel']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-allocation-request-cancel/&lt;int:id&gt;/\n                [name='shift-allocation-request-cancel']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-request-bulk-cancel\n                [name='shift-request-bulk-cancel']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-request-approve/&lt;int:id&gt;/\n                [name='shift-request-approve']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-allocation-request-approve/&lt;int:id&gt;/\n                [name='shift-allocation-request-approve']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-request-bulk-approve\n                [name='shift-request-bulk-approve']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-request-delete/&lt;int:id&gt;/\n                [name='shift-request-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-request-bulk-delete\n                [name='shift-request-bulk-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                notifications\n                [name='notifications']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                clear-notifications\n                [name='clear-notifications']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                delete-all-notifications\n                [name='delete-all-notifications']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                read-notifications\n                [name='read-notifications']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                mark-as-read-notification/&lt;int:notification_id&gt;\n                [name='mark-as-read-notification']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                mark-as-read-notification-json/\n                [name='mark-as-read-notification-json']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                all-notifications\n                [name='all-notifications']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                delete-notifications/&lt;id&gt;/\n                [name='delete-notifications']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/general-settings/\n                [name='general-settings']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/date-settings/\n                [name='date-settings']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/save-date/\n                [name='save_date_format']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/get-date-format/\n                [name='get-date-format']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/save-time/\n                [name='save_time_format']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/get-time-format/\n                [name='get-time-format']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                history-field-settings\n                [name='history-field-settings']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                enable-account-block-unblock\n                [name='enable-account-block-unblock']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                enable-profile-edit-feature\n                [name='enable-profile-edit-feature']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rwork-individual-view/&lt;int:instance_id&gt;/\n                [name='rwork-individual-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rshit-individual-view/&lt;int:instance_id&gt;/\n                [name='rshift-individual-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-select/\n                [name='shift-select']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-select-filter/\n                [name='shift-select-filter']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                work-type-select/\n                [name='work-type-select']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                work-type-filter/\n                [name='work-type-select-filter']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                r-shift-select/\n                [name='r-shift-select']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                r-shift-select-filter/\n                [name='r-shift-select-filter']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                r-work-type-select/\n                [name='r-work-type-select']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                r-work-type-filter/\n                [name='r-work-type-select-filter']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/tag-view/\n                [name='tag-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/helpdesk-tag-view/\n                [name='helpdesk-tag-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                tag-create\n                [name='tag-create']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                tag-update/&lt;int:tag_id&gt;\n                [name='tag-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                tag-delete/&lt;int:obj_id&gt;\n                [name='tag-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                audit-tag-create\n                [name='audit-tag-create']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                audit-tag-update/&lt;int:tag_id&gt;\n                [name='audit-tag-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                audit-tag-delete/&lt;int:obj_id&gt;\n                [name='audit-tag-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                configuration/multiple-approval-condition\n                [name='multiple-approval-condition']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                configuration/condition-value-fields\n                [name='condition-value-fields']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                configuration/add-more-approval-managers\n                [name='add-more-approval-managers']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                configuration/remove-approval-manager\n                [name='remove-approval-manager']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                configuration/hx-multiple-approval-condition\n                [name='hx-multiple-approval-condition']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                multiple-level-approval-create\n                [name='multiple-level-approval-create']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                multiple-level-approval-edit/&lt;int:condition_id&gt;\n                [name='multiple-level-approval-edit']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                multiple-level-approval-delete/&lt;int:condition_id&gt;\n                [name='multiple-level-approval-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-request-add-comment/&lt;int:shift_id&gt;/\n                [name='shift-request-add-comment']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                view-shift-comment/&lt;int:shift_id&gt;/\n                [name='view-shift-comment']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                delete-shift-comment-file/\n                [name='delete-shift-comment-file']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                view-work-type-comment/&lt;int:work_type_id&gt;/\n                [name='view-work-type-comment']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                delete-work-type-comment-file/\n                [name='delete-work-type-comment-file']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-request-delete-comment/&lt;int:comment_id&gt;/\n                [name='shift-request-delete-comment']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                worktype-request-add-comment/&lt;int:worktype_id&gt;/\n                [name='worktype-request-add-comment']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                worktype-request-delete-comment/&lt;int:comment_id&gt;/\n                [name='worktype-request-delete-comment']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                dashboard-shift-request\n                [name='dashboard-shift-request']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                dashboard-work-type-request\n                [name='dashboard-work-type-request']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/pagination-settings-view/\n                [name='pagination-settings-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/action-type/\n                [name='action-type']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                action-type-create\n                [name='action-type-create']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                action-type-update/&lt;int:act_id&gt;\n                [name='action-type-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                action-type-delete/&lt;int:act_id&gt;\n                [name='action-type-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                pagination-settings-view\n                [name='pagination-settings-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                announcement-list\n                [name='announcement-list']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                create-announcement\n                [name='create-announcement']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                delete-announcement/&lt;int:anoun_id&gt;\n                [name='delete-announcement']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                update-announcement/&lt;int:anoun_id&gt;\n                [name='update-announcement']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                remove-announcement-file/&lt;int:obj_id&gt;/&lt;int:attachment_id&gt;\n                [name='remove-announcement-file']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                announcement-add-comment/&lt;int:anoun_id&gt;/\n                [name='announcement-add-comment']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                announcement-view-comment/&lt;int:anoun_id&gt;/\n                [name='announcement-view-comment']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                announcement-single-view/&lt;int:anoun_id&gt;\n                [name='announcement-single-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                announcement-single-view/\n                [name='announcement-single-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                announcement-delete-comment/&lt;int:comment_id&gt;/\n                [name='announcement-delete-comment']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                announcement-viewed-by\n                [name='announcement-viewed-by']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                driver-viewed\n                [name='driver-viewed']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                dashboard-components-toggle\n                [name='dashboard-components-toggle']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                employee-chart-show\n                [name='employee-chart-show']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/enable-biometric-attendance/\n                [name='enable-biometric-attendance']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/activate-biometric-attendance\n                [name='activate-biometric-attendance']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                emp-workinfo-complete\n                [name='emp-workinfo-complete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                get-eaglora-installed-apps/\n                [name='get-eaglora-installed-apps']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                configuration/holiday-view\n                [name='holiday-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                configuration/holidays-excel-template\n                [name='holidays-excel-template']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                holidays-info-import\n                [name='holidays-info-import']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                holiday-info-export\n                [name='holiday-info-export']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                get-upcoming-holidays\n                [name='get-upcoming-holidays']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                holiday-creation\n                [name='holiday-creation']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                holiday-update/&lt;int:obj_id&gt;\n                [name='holiday-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                duplicate-holiday/&lt;int:obj_id&gt;\n                [name='duplicate-holiday']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                holiday-delete/&lt;int:obj_id&gt;\n                [name='holiday-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                holidays-bulk-delete\n                [name='holidays-bulk-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                holiday-filter\n                [name='holiday-filter']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                holiday-select/\n                [name='holiday-select']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                holiday-select-filter/\n                [name='holiday-select-filter']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                company-leave-creation\n                [name='company-leave-creation']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                configuration/company-leave-view\n                [name='company-leave-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                company-leave-update/&lt;int:id&gt;\n                [name='company-leave-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                company-leave-delete/&lt;int:id&gt;\n                [name='company-leave-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                company-leave-filter\n                [name='company-leave-filter']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                view-penalties\n                [name='view-penalties']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                ^media/(?P&lt;path&gt;.*)$\n                [name='protected_media']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                update-selected-company\n                [name='update-selected-company']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                configuration/mail-automations\n                [name='mail-automations']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                mail-automations-nav\n                [name='mail-automations-nav']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                create-automation\n                [name='create-automation']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                update-automation/&lt;int:pk&gt;/\n                [name='update-automation']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                mail-automations-list-view\n                [name='mail-automations-list-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                get-to-mail-field\n                [name='get-to-mail-field']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                automation-detailed-view/&lt;int:pk&gt;/\n                [name='automation-detailed-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                delete-automation/&lt;int:pk&gt;/\n                [name='delete-automation']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                load-automations\n                [name='load-automations']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                refresh-automations\n                [name='refresh-automations']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                toggle-columns\n                [name='toggle-columns']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                active-tab\n                [name='active-tab']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                active-group\n                [name='cbv-active-group']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                reload-field\n                [name='reload-field']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                reload-messages\n                [name='reload-messages']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                saved-filter/\n                [name='saved-filter']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                saved-filter/&lt;int:pk&gt;/\n                [name='saved-filter-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                delete-saved-filter/&lt;int:pk&gt;/\n                [name='delete-saved-filter']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                active-hnv-view-type/\n                [name='active-hnv-view-type']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                search-in-instance-ids\n                [name='search-in-instance-ids']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                last-applied-filter\n                [name='last-applied-filter']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                generic-delete\n                [name='generic-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                eaglora-history-revert/&lt;int:pk&gt;/&lt;int:history_id&gt;/\n                [name='history-revert']\n            \n          </li>\n        \n          <li>\n            \n                employee/\n                \n            \n          </li>\n        \n          <li>\n            \n                ai/\n                \n            \n          </li>\n        \n          <li>\n            \n                eaglora-widget/\n                \n            \n          </li>\n        \n          <li>\n            \n                ^inbox/notifications/\n                \n            \n          </li>\n        \n          <li>\n            \n                i18n/\n                \n            \n          </li>\n        \n          <li>\n            \n                health/\n                \n            \n          </li>\n        \n          <li>\n            \n                recruitment/\n                \n            \n          </li>\n        \n          <li>\n            \n                leave/\n                \n            \n          </li>\n        \n          <li>\n            \n                pms/\n                \n            \n          </li>\n        \n          <li>\n            \n                onboarding/\n                \n            \n          </li>\n        \n          <li>\n            \n                asset/\n                \n            \n          </li>\n        \n          <li>\n            \n                attendance/\n                \n            \n          </li>\n        \n          <li>\n            \n                payroll/\n                \n            \n          </li>\n        \n          <li>\n            \n                report/\n                \n            \n          </li>\n        \n          <li>\n            \n                ai/\n                \n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                user-accessibility/\n                [name='user-accessibility']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                get-initial-accessibility-data\n                [name='get-initial-accessibility-data']\n            \n          </li>\n        \n          <li>\n            \n                biometric/\n                \n            \n          </li>\n        \n          <li>\n            \n                helpdesk/\n                \n            \n          </li>\n        \n          <li>\n            \n                offboarding/\n                \n            \n          </li>\n        \n          <li>\n            \n                backup/\n                \n            \n          </li>\n        \n          <li>\n            \n                project/\n                \n            \n          </li>\n        \n          <li>\n            \n                project/\n                \n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                auth/\n                \n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                asset/\n                \n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                base/\n                \n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                employees/\n                [name='api-employees-list']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                employees/&lt;int:pk&gt;/\n                [name='api-employee-detail']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                employee-type/&lt;int:pk&gt;\n                [name='api-employees']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                employee-type/\n                [name='api-employees']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                list/employees/\n                [name='api-employee-list-detailed']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                employee-bank-details/&lt;int:pk&gt;/\n                [name='api-employee-bank-details-detail']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                employee-work-information/\n                [name='api-employee-work-information-list']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                employee-work-information/&lt;int:pk&gt;/\n                [name='api-employee-work-information-detail']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                employee-work-info-export/\n                [name='api-employee-work-info-export']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                employee-work-info-import/\n                [name='api-employee-work-info-import']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                employee-bulk-update/\n                [name='api-employee-bulk-update']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                disciplinary-action/\n                [name='api-disciplinary-action-list']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                disciplinary-action/&lt;int:pk&gt;/\n                [name='api-disciplinary-action-detail']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                disciplinary-action-type/\n                [name='api-disciplinary-action-type']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                disciplinary-action-type/&lt;int:pk&gt;/\n                [name='api-disciplinary-action-type']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                policies/\n                [name='api-policy-list']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                policies/&lt;int:pk&gt;/\n                [name='api-policy-detail']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                document-request/\n                [name='api-document-request-list']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                document-request/&lt;int:pk&gt;/\n                [name='api-document-request-detail']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                document-bulk-approve-reject/\n                [name='api-document-bulk-approve-reject']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                document-request-approve-reject/&lt;int:id&gt;/&lt;str:status&gt;/\n                [name='api-document-request-approve-reject']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                documents/\n                [name='api-document-list']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                documents/&lt;int:pk&gt;/\n                [name='api-document-detail']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                employee-bulk- archive/&lt;str:is_active&gt;/\n                [name='api-employee-bulk-archive']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                employee-archive/&lt;int:id&gt;/&lt;str:is_active&gt;/\n                [name='api-employee-archive']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                employee-selector/\n                [name='api-employee-selector']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                manager-check/\n                [name='api-manager-check']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                notifications/\n                \n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                payroll/\n                \n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                attendance/\n                \n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                leave/\n                \n            \n          </li>\n        \n          <li>\n            \n                api/geofencing/\n                \n            \n          </li>\n        \n          <li>\n            \n                api/facedetection/\n                \n            \n          </li>\n        \n      </ol>\n      <p>\n        \n          The current path, <code>api/employee/</code>,\n        \n        didn’t match any of these.\n      </p>\n    \n  </div>\n\n  <div id=\"explanation\">\n    <p>\n      You’re seeing this error because you have <code>DEBUG = True</code> in\n      your Django settings file. Change that to <code>False</code>, and Django\n      will display a standard 404 page.\n    </p>\n  </div>\n</body>\n</html>\n", "content_type": "text", "summary": "Received 67797 characters of text data", "error": {"message": "HTTP 404: Not Found", "status_code": 404, "response_text": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta http-equiv=\"content-type\" content=\"text/html; charset=utf-8\">\n  <title>Page not found at /api/employee/</title>\n  <meta name=\"robots\" content=\"NONE,NOARCHIVE\">\n  <style type=\"text/css\">\n    html * { padding:0; margin:0; }\n    body * { padding:10px 20px; }\n    body * * { padding:0; }\n    body { font:small sans-serif; background:#eee; color:#000; }\n    body>div { border-bottom:1px solid #ddd; }\n    h1 { font-weight:normal; margin-bottom:.4em; }\n    h"}, "execution_metadata": {"execution_time": 2.211214065551758, "timestamp": "2025-07-13T09:21:49.492147", "user": "anonymous", "request_id": "req_20250713_092149_anon_0"}}}, {"workflow": "List employees from HR department", "success": true, "api_request": {"url": "/api/employee/", "method": "GET", "filters": {"department__icontains": "HR"}, "search": null, "sort_by": null, "order": "asc", "body": {}, "confidence": 0.9400000000000001, "metadata": {"parsed_intent": "list", "detected_entities": {"departments": ["hr"]}, "endpoint_match": 0.2, "processing_time": "2025-07-13T09:21:49.493178"}}, "execution_result": {"success": false, "status_code": 404, "headers": {"Date": "Sun, 13 Jul 2025 03:51:49 GMT", "Server": "WSGIServer/0.2 CPython/3.10.11", "Content-Type": "text/html; charset=utf-8", "X-Frame-Options": "SAMEORIGIN", "Vary": "Accept-Language, origin, <PERSON>ie", "Content-Language": "en", "Content-Length": "67826", "X-Content-Type-Options": "nosniff", "Referrer-Policy": "same-origin", "Cross-Origin-Opener-Policy": "same-origin"}, "url": "http://localhost:8000/api/employee/?department__icontains=HR", "data": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta http-equiv=\"content-type\" content=\"text/html; charset=utf-8\">\n  <title>Page not found at /api/employee/</title>\n  <meta name=\"robots\" content=\"NONE,NOARCHIVE\">\n  <style type=\"text/css\">\n    html * { padding:0; margin:0; }\n    body * { padding:10px 20px; }\n    body * * { padding:0; }\n    body { font:small sans-serif; background:#eee; color:#000; }\n    body>div { border-bottom:1px solid #ddd; }\n    h1 { font-weight:normal; margin-bottom:.4em; }\n    h1 span { font-size:60%; color:#666; font-weight:normal; }\n    table { border:none; border-collapse: collapse; width:100%; }\n    td, th { vertical-align:top; padding:2px 3px; }\n    th { width:12em; text-align:right; color:#666; padding-right:.5em; }\n    #info { background:#f6f6f6; }\n    #info ol { margin: 0.5em 4em; }\n    #info ol li { font-family: monospace; }\n    #summary { background: #ffc; }\n    #explanation { background:#eee; border-bottom: 0px none; }\n    pre.exception_value { font-family: sans-serif; color: #575757; font-size: 1.5em; margin: 10px 0 10px 0; }\n  </style>\n</head>\n<body>\n  <div id=\"summary\">\n    <h1>Page not found <span>(404)</span></h1>\n    \n    <table class=\"meta\">\n      <tr>\n        <th>Request Method:</th>\n        <td>GET</td>\n      </tr>\n      <tr>\n        <th>Request URL:</th>\n        <td>http://localhost:8000/api/employee/?department__icontains=HR</td>\n      </tr>\n      \n    </table>\n  </div>\n  <div id=\"info\">\n    \n      <p>\n      Using the URLconf defined in <code>eaglora.urls</code>,\n      Django tried these URL patterns, in this order:\n      </p>\n      <ol>\n        \n          <li>\n            \n                admin/\n                \n            \n          </li>\n        \n          <li>\n            \n                accounts/\n                \n            \n          </li>\n        \n          <li>\n            \n                accounts/\n                \n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                \n                [name='home-page']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                initialize-database\n                [name='initialize-database']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                load-demo-database\n                [name='load-demo-database']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                initialize-database-user\n                [name='initialize-database-user']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                initialize-database-company\n                [name='initialize-database-company']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                initialize-database-department\n                [name='initialize-database-department']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                initialize-department-edit/&lt;int:obj_id&gt;\n                [name='initialize-department-edit']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                initialize-department-delete/&lt;int:obj_id&gt;\n                [name='initialize-department-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                initialize-database-job-position\n                [name='initialize-database-job-position']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                initialize-job-position-edit/&lt;int:obj_id&gt;\n                [name='initialize-job-position-edit']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                initialize-job-position-delete/&lt;int:obj_id&gt;\n                [name='initialize-job-position-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                404\n                [name='404']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                login/\n                [name='login']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                forgot-password\n                [name='forgot-password']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                employee-reset-password\n                [name='employee-reset-password']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                reset-send-success\n                [name='reset-send-success']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                change-password\n                [name='change-password']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                change-username\n                [name='change-username']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                two-factor\n                [name='two-factor']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                send-otp\n                [name='send-otp']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                logout\n                [name='logout']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings\n                [name='settings']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/user-group-create/\n                [name='user-group-create']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/user-group-view/\n                [name='user-group-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/user-group-search/\n                [name='user-group-search']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                user-group-delete/&lt;int:obj_id&gt;/\n                [name='user-group-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                group-permission-remove/&lt;int:pid&gt;/&lt;int:gid&gt;/\n                [name='group-permission-remove']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                user-group-assign-view\n                [name='user-group-assign-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/user-group-assign/\n                [name='user-group-assign']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                group-remove-user/&lt;int:uid&gt;/&lt;int:gid&gt;/\n                [name='group-remove-user']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/employee-permission-assign/\n                [name='employee-permission-assign']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                employee-permission-search\n                [name='permission-search']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                update-user-permission\n                [name='update-user-permission']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                update-group-permission\n                [name='update-group-permission']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                permission-table\n                [name='permission-table']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/mail-server-conf/\n                [name='mail-server-conf']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/mail-server-create-update/\n                [name='mail-server-create-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/mail-server-test-email/\n                [name='mail-server-test-email']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                mail-server-delete\n                [name='mail-server-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                replace-primary-mail\n                [name='replace-primary-mail']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                configuration/view-mail-templates/\n                [name='view-mail-templates']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                view-mail-template/&lt;int:obj_id&gt;/\n                [name='view-mail-template']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                create-mail-template/\n                [name='create-mail-template']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                duplicate-mail-template/&lt;int:obj_id&gt;/\n                [name='duplicate-mail-template']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                delete-mail-template/\n                [name='delete-mail-template']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/company-create/\n                [name='company-create']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/company-view/\n                [name='company-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/company-update/&lt;int:id&gt;/\n                [name='company-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/company-delete/&lt;int:obj_id&gt;/\n                [name='company-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/department-view/\n                [name='department-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/department-creation/\n                [name='department-creation']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/department-update/&lt;int:id&gt;/\n                [name='department-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                department-delete/&lt;int:obj_id&gt;/\n                [name='department-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/job-position-creation/\n                [name='job-position-creation']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/job-position-view/\n                [name='job-position-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/job-position-update/&lt;int:id&gt;/\n                [name='job-position-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                job-position-delete/&lt;int:obj_id&gt;/\n                [name='job-position-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/job-role-create/\n                [name='job-role-create']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/job-role-view/\n                [name='job-role-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/job-role-update/&lt;int:id&gt;/\n                [name='job-role-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                job-role-delete/&lt;int:obj_id&gt;/\n                [name='job-role-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/work-type-view/\n                [name='work-type-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/work-type-create/\n                [name='work-type-create']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/work-type-update/&lt;int:id&gt;/\n                [name='work-type-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                work-type-delete/&lt;int:obj_id&gt;/\n                [name='work-type-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                add-remove-work-type-fields\n                [name='add-remove-work-type-fields']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/rotating-work-type-create/\n                [name='rotating-work-type-create']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/rotating-work-type-view/\n                [name='rotating-work-type-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/rotating-work-type-update/&lt;int:id&gt;/\n                [name='rotating-work-type-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-work-type-delete/&lt;int:obj_id&gt;/\n                [name='rotating-work-type-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                employee/rotating-work-type-assign/\n                [name='rotating-work-type-assign']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-work-type-assign-add\n                [name='rotating-work-type-assign-add']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-work-type-assign-view\n                [name='rotating-work-type-assign-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-work-type-assign-export\n                [name='rotating-work-type-assign-export']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/rotating-work-type-assign-update/&lt;int:id&gt;/\n                [name='rotating-work-type-assign-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-work-type-assign-duplicate/&lt;int:obj_id&gt;/\n                [name='rotating-work-type-assign-duplicate']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-work-type-assign-archive/&lt;int:obj_id&gt;/\n                [name='rotating-work-type-assign-archive']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-work-type-assign-bulk-archive\n                [name='rotating-shift-work-type-bulk-archive']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-work-type-assign-bulk-delete\n                [name='rotating-shift-work-type-bulk-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-work-type-assign-delete/&lt;int:obj_id&gt;/\n                [name='rotating-work-type-assign-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/employee-type-view/\n                [name='employee-type-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/employee-type-create/\n                [name='employee-type-create']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/employee-type-update/&lt;int:id&gt;/\n                [name='employee-type-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                employee-type-delete/&lt;int:obj_id&gt;/\n                [name='employee-type-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/employee-shift-view/\n                [name='employee-shift-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/employee-shift-create/\n                [name='employee-shift-create']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/employee-shift-update/&lt;int:id&gt;/\n                [name='employee-shift-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                employee-shift-delete/&lt;int:obj_id&gt;/\n                [name='employee-shift-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/employee-shift-schedule-view/\n                [name='employee-shift-schedule-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/employee-shift-schedule-create/\n                [name='employee-shift-schedule-create']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/employee-shift-schedule-update/&lt;int:id&gt;/\n                [name='employee-shift-schedule-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                employee-shift-schedule-delete/&lt;int:obj_id&gt;/\n                [name='employee-shift-schedule-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/rotating-shift-create/\n                [name='rotating-shift-create']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                add-remove-shift-fields\n                [name='add-remove-shift-fields']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/rotating-shift-view/\n                [name='rotating-shift-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/rotating-shift-update/&lt;int:id&gt;/\n                [name='rotating-shift-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-shift-delete/&lt;int:obj_id&gt;/\n                [name='rotating-shift-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                employee/rotating-shift-assign/\n                [name='rotating-shift-assign']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-shift-assign-add\n                [name='rotating-shift-assign-add']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-shift-assign-view\n                [name='rotating-shift-assign-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-shift-assign-info-export\n                [name='rotating-shift-assign-info-export']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-shift-assign-info-import\n                [name='rotating-shift-assign-info-import']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/rotating-shift-assign-update/&lt;int:id&gt;/\n                [name='rotating-shift-assign-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-shift-assign-duplicate/&lt;int:obj_id&gt;/\n                [name='rotating-shift-assign-duplicate']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-shift-assign-archive/&lt;int:obj_id&gt;/\n                [name='rotating-shift-assign-archive']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-shift-assign-bulk-archive\n                [name='rotating-shift-assign-bulk-archive']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-shift-assign-bulk-delete\n                [name='rotating-shift-assign-bulk-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-shift-assign-delete/&lt;int:obj_id&gt;/\n                [name='rotating-shift-assign-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                work-type-request\n                [name='work-type-request']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                work-type-request-duplicate/&lt;int:obj_id&gt;/\n                [name='work-type-request-duplicate']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                employee/work-type-request-view/\n                [name='work-type-request-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                work-type-request-info-export\n                [name='work-type-request-info-export']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                work-type-request-search\n                [name='work-type-request-search']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                work-type-request-cancel/&lt;int:id&gt;/\n                [name='work-type-request-cancel']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                work-type-request-bulk-cancel\n                [name='work-type-request-bulk-cancel']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                work-type-request-approve/&lt;int:id&gt;/\n                [name='work-type-request-approve']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                work-type-request-bulk-approve\n                [name='work-type-request-bulk-approve']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                work-type-request-update/&lt;int:work_type_request_id&gt;/\n                [name='work-type-request-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                work-type-request-delete/&lt;int:obj_id&gt;/\n                [name='work-type-request-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                work-type-request-single-view/&lt;int:obj_id&gt;/\n                [name='work-type-request-single-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                work-type-request-bulk-delete\n                [name='work-type-request-bulk-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-request\n                [name='shift-request']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-request-duplicate/&lt;int:obj_id&gt;/\n                [name='shift-request-duplicate']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-request-reallocate\n                [name='shift-request-reallocate']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                update-employee-allocation\n                [name='update-employee-allocation']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                employee/shift-request-view/\n                [name='shift-request-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-request-info-export\n                [name='shift-request-info-export']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-request-search\n                [name='shift-request-search']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-request-details/&lt;int:id&gt;/\n                [name='shift-request-details']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-allocation-request-details/&lt;int:id&gt;/\n                [name='shift-allocation-request-details']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-request-update/&lt;int:shift_request_id&gt;/\n                [name='shift-request-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-allocation-request-update/&lt;int:shift_request_id&gt;/\n                [name='shift-allocation-request-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-request-cancel/&lt;int:id&gt;/\n                [name='shift-request-cancel']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-allocation-request-cancel/&lt;int:id&gt;/\n                [name='shift-allocation-request-cancel']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-request-bulk-cancel\n                [name='shift-request-bulk-cancel']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-request-approve/&lt;int:id&gt;/\n                [name='shift-request-approve']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-allocation-request-approve/&lt;int:id&gt;/\n                [name='shift-allocation-request-approve']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-request-bulk-approve\n                [name='shift-request-bulk-approve']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-request-delete/&lt;int:id&gt;/\n                [name='shift-request-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-request-bulk-delete\n                [name='shift-request-bulk-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                notifications\n                [name='notifications']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                clear-notifications\n                [name='clear-notifications']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                delete-all-notifications\n                [name='delete-all-notifications']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                read-notifications\n                [name='read-notifications']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                mark-as-read-notification/&lt;int:notification_id&gt;\n                [name='mark-as-read-notification']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                mark-as-read-notification-json/\n                [name='mark-as-read-notification-json']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                all-notifications\n                [name='all-notifications']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                delete-notifications/&lt;id&gt;/\n                [name='delete-notifications']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/general-settings/\n                [name='general-settings']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/date-settings/\n                [name='date-settings']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/save-date/\n                [name='save_date_format']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/get-date-format/\n                [name='get-date-format']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/save-time/\n                [name='save_time_format']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/get-time-format/\n                [name='get-time-format']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                history-field-settings\n                [name='history-field-settings']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                enable-account-block-unblock\n                [name='enable-account-block-unblock']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                enable-profile-edit-feature\n                [name='enable-profile-edit-feature']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rwork-individual-view/&lt;int:instance_id&gt;/\n                [name='rwork-individual-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rshit-individual-view/&lt;int:instance_id&gt;/\n                [name='rshift-individual-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-select/\n                [name='shift-select']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-select-filter/\n                [name='shift-select-filter']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                work-type-select/\n                [name='work-type-select']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                work-type-filter/\n                [name='work-type-select-filter']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                r-shift-select/\n                [name='r-shift-select']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                r-shift-select-filter/\n                [name='r-shift-select-filter']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                r-work-type-select/\n                [name='r-work-type-select']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                r-work-type-filter/\n                [name='r-work-type-select-filter']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/tag-view/\n                [name='tag-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/helpdesk-tag-view/\n                [name='helpdesk-tag-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                tag-create\n                [name='tag-create']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                tag-update/&lt;int:tag_id&gt;\n                [name='tag-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                tag-delete/&lt;int:obj_id&gt;\n                [name='tag-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                audit-tag-create\n                [name='audit-tag-create']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                audit-tag-update/&lt;int:tag_id&gt;\n                [name='audit-tag-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                audit-tag-delete/&lt;int:obj_id&gt;\n                [name='audit-tag-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                configuration/multiple-approval-condition\n                [name='multiple-approval-condition']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                configuration/condition-value-fields\n                [name='condition-value-fields']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                configuration/add-more-approval-managers\n                [name='add-more-approval-managers']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                configuration/remove-approval-manager\n                [name='remove-approval-manager']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                configuration/hx-multiple-approval-condition\n                [name='hx-multiple-approval-condition']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                multiple-level-approval-create\n                [name='multiple-level-approval-create']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                multiple-level-approval-edit/&lt;int:condition_id&gt;\n                [name='multiple-level-approval-edit']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                multiple-level-approval-delete/&lt;int:condition_id&gt;\n                [name='multiple-level-approval-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-request-add-comment/&lt;int:shift_id&gt;/\n                [name='shift-request-add-comment']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                view-shift-comment/&lt;int:shift_id&gt;/\n                [name='view-shift-comment']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                delete-shift-comment-file/\n                [name='delete-shift-comment-file']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                view-work-type-comment/&lt;int:work_type_id&gt;/\n                [name='view-work-type-comment']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                delete-work-type-comment-file/\n                [name='delete-work-type-comment-file']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-request-delete-comment/&lt;int:comment_id&gt;/\n                [name='shift-request-delete-comment']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                worktype-request-add-comment/&lt;int:worktype_id&gt;/\n                [name='worktype-request-add-comment']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                worktype-request-delete-comment/&lt;int:comment_id&gt;/\n                [name='worktype-request-delete-comment']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                dashboard-shift-request\n                [name='dashboard-shift-request']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                dashboard-work-type-request\n                [name='dashboard-work-type-request']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/pagination-settings-view/\n                [name='pagination-settings-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/action-type/\n                [name='action-type']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                action-type-create\n                [name='action-type-create']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                action-type-update/&lt;int:act_id&gt;\n                [name='action-type-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                action-type-delete/&lt;int:act_id&gt;\n                [name='action-type-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                pagination-settings-view\n                [name='pagination-settings-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                announcement-list\n                [name='announcement-list']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                create-announcement\n                [name='create-announcement']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                delete-announcement/&lt;int:anoun_id&gt;\n                [name='delete-announcement']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                update-announcement/&lt;int:anoun_id&gt;\n                [name='update-announcement']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                remove-announcement-file/&lt;int:obj_id&gt;/&lt;int:attachment_id&gt;\n                [name='remove-announcement-file']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                announcement-add-comment/&lt;int:anoun_id&gt;/\n                [name='announcement-add-comment']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                announcement-view-comment/&lt;int:anoun_id&gt;/\n                [name='announcement-view-comment']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                announcement-single-view/&lt;int:anoun_id&gt;\n                [name='announcement-single-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                announcement-single-view/\n                [name='announcement-single-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                announcement-delete-comment/&lt;int:comment_id&gt;/\n                [name='announcement-delete-comment']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                announcement-viewed-by\n                [name='announcement-viewed-by']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                driver-viewed\n                [name='driver-viewed']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                dashboard-components-toggle\n                [name='dashboard-components-toggle']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                employee-chart-show\n                [name='employee-chart-show']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/enable-biometric-attendance/\n                [name='enable-biometric-attendance']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/activate-biometric-attendance\n                [name='activate-biometric-attendance']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                emp-workinfo-complete\n                [name='emp-workinfo-complete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                get-eaglora-installed-apps/\n                [name='get-eaglora-installed-apps']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                configuration/holiday-view\n                [name='holiday-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                configuration/holidays-excel-template\n                [name='holidays-excel-template']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                holidays-info-import\n                [name='holidays-info-import']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                holiday-info-export\n                [name='holiday-info-export']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                get-upcoming-holidays\n                [name='get-upcoming-holidays']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                holiday-creation\n                [name='holiday-creation']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                holiday-update/&lt;int:obj_id&gt;\n                [name='holiday-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                duplicate-holiday/&lt;int:obj_id&gt;\n                [name='duplicate-holiday']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                holiday-delete/&lt;int:obj_id&gt;\n                [name='holiday-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                holidays-bulk-delete\n                [name='holidays-bulk-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                holiday-filter\n                [name='holiday-filter']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                holiday-select/\n                [name='holiday-select']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                holiday-select-filter/\n                [name='holiday-select-filter']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                company-leave-creation\n                [name='company-leave-creation']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                configuration/company-leave-view\n                [name='company-leave-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                company-leave-update/&lt;int:id&gt;\n                [name='company-leave-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                company-leave-delete/&lt;int:id&gt;\n                [name='company-leave-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                company-leave-filter\n                [name='company-leave-filter']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                view-penalties\n                [name='view-penalties']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                ^media/(?P&lt;path&gt;.*)$\n                [name='protected_media']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                update-selected-company\n                [name='update-selected-company']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                configuration/mail-automations\n                [name='mail-automations']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                mail-automations-nav\n                [name='mail-automations-nav']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                create-automation\n                [name='create-automation']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                update-automation/&lt;int:pk&gt;/\n                [name='update-automation']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                mail-automations-list-view\n                [name='mail-automations-list-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                get-to-mail-field\n                [name='get-to-mail-field']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                automation-detailed-view/&lt;int:pk&gt;/\n                [name='automation-detailed-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                delete-automation/&lt;int:pk&gt;/\n                [name='delete-automation']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                load-automations\n                [name='load-automations']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                refresh-automations\n                [name='refresh-automations']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                toggle-columns\n                [name='toggle-columns']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                active-tab\n                [name='active-tab']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                active-group\n                [name='cbv-active-group']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                reload-field\n                [name='reload-field']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                reload-messages\n                [name='reload-messages']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                saved-filter/\n                [name='saved-filter']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                saved-filter/&lt;int:pk&gt;/\n                [name='saved-filter-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                delete-saved-filter/&lt;int:pk&gt;/\n                [name='delete-saved-filter']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                active-hnv-view-type/\n                [name='active-hnv-view-type']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                search-in-instance-ids\n                [name='search-in-instance-ids']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                last-applied-filter\n                [name='last-applied-filter']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                generic-delete\n                [name='generic-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                eaglora-history-revert/&lt;int:pk&gt;/&lt;int:history_id&gt;/\n                [name='history-revert']\n            \n          </li>\n        \n          <li>\n            \n                employee/\n                \n            \n          </li>\n        \n          <li>\n            \n                ai/\n                \n            \n          </li>\n        \n          <li>\n            \n                eaglora-widget/\n                \n            \n          </li>\n        \n          <li>\n            \n                ^inbox/notifications/\n                \n            \n          </li>\n        \n          <li>\n            \n                i18n/\n                \n            \n          </li>\n        \n          <li>\n            \n                health/\n                \n            \n          </li>\n        \n          <li>\n            \n                recruitment/\n                \n            \n          </li>\n        \n          <li>\n            \n                leave/\n                \n            \n          </li>\n        \n          <li>\n            \n                pms/\n                \n            \n          </li>\n        \n          <li>\n            \n                onboarding/\n                \n            \n          </li>\n        \n          <li>\n            \n                asset/\n                \n            \n          </li>\n        \n          <li>\n            \n                attendance/\n                \n            \n          </li>\n        \n          <li>\n            \n                payroll/\n                \n            \n          </li>\n        \n          <li>\n            \n                report/\n                \n            \n          </li>\n        \n          <li>\n            \n                ai/\n                \n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                user-accessibility/\n                [name='user-accessibility']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                get-initial-accessibility-data\n                [name='get-initial-accessibility-data']\n            \n          </li>\n        \n          <li>\n            \n                biometric/\n                \n            \n          </li>\n        \n          <li>\n            \n                helpdesk/\n                \n            \n          </li>\n        \n          <li>\n            \n                offboarding/\n                \n            \n          </li>\n        \n          <li>\n            \n                backup/\n                \n            \n          </li>\n        \n          <li>\n            \n                project/\n                \n            \n          </li>\n        \n          <li>\n            \n                project/\n                \n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                auth/\n                \n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                asset/\n                \n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                base/\n                \n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                employees/\n                [name='api-employees-list']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                employees/&lt;int:pk&gt;/\n                [name='api-employee-detail']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                employee-type/&lt;int:pk&gt;\n                [name='api-employees']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                employee-type/\n                [name='api-employees']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                list/employees/\n                [name='api-employee-list-detailed']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                employee-bank-details/&lt;int:pk&gt;/\n                [name='api-employee-bank-details-detail']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                employee-work-information/\n                [name='api-employee-work-information-list']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                employee-work-information/&lt;int:pk&gt;/\n                [name='api-employee-work-information-detail']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                employee-work-info-export/\n                [name='api-employee-work-info-export']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                employee-work-info-import/\n                [name='api-employee-work-info-import']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                employee-bulk-update/\n                [name='api-employee-bulk-update']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                disciplinary-action/\n                [name='api-disciplinary-action-list']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                disciplinary-action/&lt;int:pk&gt;/\n                [name='api-disciplinary-action-detail']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                disciplinary-action-type/\n                [name='api-disciplinary-action-type']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                disciplinary-action-type/&lt;int:pk&gt;/\n                [name='api-disciplinary-action-type']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                policies/\n                [name='api-policy-list']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                policies/&lt;int:pk&gt;/\n                [name='api-policy-detail']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                document-request/\n                [name='api-document-request-list']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                document-request/&lt;int:pk&gt;/\n                [name='api-document-request-detail']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                document-bulk-approve-reject/\n                [name='api-document-bulk-approve-reject']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                document-request-approve-reject/&lt;int:id&gt;/&lt;str:status&gt;/\n                [name='api-document-request-approve-reject']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                documents/\n                [name='api-document-list']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                documents/&lt;int:pk&gt;/\n                [name='api-document-detail']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                employee-bulk- archive/&lt;str:is_active&gt;/\n                [name='api-employee-bulk-archive']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                employee-archive/&lt;int:id&gt;/&lt;str:is_active&gt;/\n                [name='api-employee-archive']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                employee-selector/\n                [name='api-employee-selector']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                manager-check/\n                [name='api-manager-check']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                notifications/\n                \n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                payroll/\n                \n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                attendance/\n                \n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                leave/\n                \n            \n          </li>\n        \n          <li>\n            \n                api/geofencing/\n                \n            \n          </li>\n        \n          <li>\n            \n                api/facedetection/\n                \n            \n          </li>\n        \n      </ol>\n      <p>\n        \n          The current path, <code>api/employee/</code>,\n        \n        didn’t match any of these.\n      </p>\n    \n  </div>\n\n  <div id=\"explanation\">\n    <p>\n      You’re seeing this error because you have <code>DEBUG = True</code> in\n      your Django settings file. Change that to <code>False</code>, and Django\n      will display a standard 404 page.\n    </p>\n  </div>\n</body>\n</html>\n", "content_type": "text", "summary": "Received 67822 characters of text data", "error": {"message": "HTTP 404: Not Found", "status_code": 404, "response_text": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta http-equiv=\"content-type\" content=\"text/html; charset=utf-8\">\n  <title>Page not found at /api/employee/</title>\n  <meta name=\"robots\" content=\"NONE,NOARCHIVE\">\n  <style type=\"text/css\">\n    html * { padding:0; margin:0; }\n    body * { padding:10px 20px; }\n    body * * { padding:0; }\n    body { font:small sans-serif; background:#eee; color:#000; }\n    body>div { border-bottom:1px solid #ddd; }\n    h1 { font-weight:normal; margin-bottom:.4em; }\n    h"}, "execution_metadata": {"execution_time": 0.11346125602722168, "timestamp": "2025-07-13T09:21:49.606640", "user": "anonymous", "request_id": "req_20250713_092149_anon_1"}}}, {"workflow": "Export employee data", "success": true, "api_request": {"url": "/api/employee/export/", "method": "GET", "filters": {}, "search": null, "sort_by": null, "order": "asc", "body": {}, "confidence": 0.8666666666666667, "metadata": {"parsed_intent": "export", "detected_entities": {}, "endpoint_match": 0.3333333333333333, "processing_time": "2025-07-13T09:21:49.607640"}}, "execution_result": {"success": false, "status_code": 404, "headers": {"Date": "Sun, 13 Jul 2025 03:51:49 GMT", "Server": "WSGIServer/0.2 CPython/3.10.11", "Content-Type": "text/html; charset=utf-8", "X-Frame-Options": "SAMEORIGIN", "Vary": "Accept-Language, origin, <PERSON>ie", "Content-Language": "en", "Content-Length": "67822", "X-Content-Type-Options": "nosniff", "Referrer-Policy": "same-origin", "Cross-Origin-Opener-Policy": "same-origin"}, "url": "http://localhost:8000/api/employee/export/", "data": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta http-equiv=\"content-type\" content=\"text/html; charset=utf-8\">\n  <title>Page not found at /api/employee/export/</title>\n  <meta name=\"robots\" content=\"NONE,NOARCHIVE\">\n  <style type=\"text/css\">\n    html * { padding:0; margin:0; }\n    body * { padding:10px 20px; }\n    body * * { padding:0; }\n    body { font:small sans-serif; background:#eee; color:#000; }\n    body>div { border-bottom:1px solid #ddd; }\n    h1 { font-weight:normal; margin-bottom:.4em; }\n    h1 span { font-size:60%; color:#666; font-weight:normal; }\n    table { border:none; border-collapse: collapse; width:100%; }\n    td, th { vertical-align:top; padding:2px 3px; }\n    th { width:12em; text-align:right; color:#666; padding-right:.5em; }\n    #info { background:#f6f6f6; }\n    #info ol { margin: 0.5em 4em; }\n    #info ol li { font-family: monospace; }\n    #summary { background: #ffc; }\n    #explanation { background:#eee; border-bottom: 0px none; }\n    pre.exception_value { font-family: sans-serif; color: #575757; font-size: 1.5em; margin: 10px 0 10px 0; }\n  </style>\n</head>\n<body>\n  <div id=\"summary\">\n    <h1>Page not found <span>(404)</span></h1>\n    \n    <table class=\"meta\">\n      <tr>\n        <th>Request Method:</th>\n        <td>GET</td>\n      </tr>\n      <tr>\n        <th>Request URL:</th>\n        <td>http://localhost:8000/api/employee/export/</td>\n      </tr>\n      \n    </table>\n  </div>\n  <div id=\"info\">\n    \n      <p>\n      Using the URLconf defined in <code>eaglora.urls</code>,\n      Django tried these URL patterns, in this order:\n      </p>\n      <ol>\n        \n          <li>\n            \n                admin/\n                \n            \n          </li>\n        \n          <li>\n            \n                accounts/\n                \n            \n          </li>\n        \n          <li>\n            \n                accounts/\n                \n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                \n                [name='home-page']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                initialize-database\n                [name='initialize-database']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                load-demo-database\n                [name='load-demo-database']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                initialize-database-user\n                [name='initialize-database-user']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                initialize-database-company\n                [name='initialize-database-company']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                initialize-database-department\n                [name='initialize-database-department']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                initialize-department-edit/&lt;int:obj_id&gt;\n                [name='initialize-department-edit']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                initialize-department-delete/&lt;int:obj_id&gt;\n                [name='initialize-department-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                initialize-database-job-position\n                [name='initialize-database-job-position']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                initialize-job-position-edit/&lt;int:obj_id&gt;\n                [name='initialize-job-position-edit']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                initialize-job-position-delete/&lt;int:obj_id&gt;\n                [name='initialize-job-position-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                404\n                [name='404']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                login/\n                [name='login']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                forgot-password\n                [name='forgot-password']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                employee-reset-password\n                [name='employee-reset-password']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                reset-send-success\n                [name='reset-send-success']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                change-password\n                [name='change-password']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                change-username\n                [name='change-username']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                two-factor\n                [name='two-factor']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                send-otp\n                [name='send-otp']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                logout\n                [name='logout']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings\n                [name='settings']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/user-group-create/\n                [name='user-group-create']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/user-group-view/\n                [name='user-group-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/user-group-search/\n                [name='user-group-search']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                user-group-delete/&lt;int:obj_id&gt;/\n                [name='user-group-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                group-permission-remove/&lt;int:pid&gt;/&lt;int:gid&gt;/\n                [name='group-permission-remove']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                user-group-assign-view\n                [name='user-group-assign-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/user-group-assign/\n                [name='user-group-assign']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                group-remove-user/&lt;int:uid&gt;/&lt;int:gid&gt;/\n                [name='group-remove-user']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/employee-permission-assign/\n                [name='employee-permission-assign']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                employee-permission-search\n                [name='permission-search']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                update-user-permission\n                [name='update-user-permission']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                update-group-permission\n                [name='update-group-permission']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                permission-table\n                [name='permission-table']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/mail-server-conf/\n                [name='mail-server-conf']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/mail-server-create-update/\n                [name='mail-server-create-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/mail-server-test-email/\n                [name='mail-server-test-email']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                mail-server-delete\n                [name='mail-server-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                replace-primary-mail\n                [name='replace-primary-mail']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                configuration/view-mail-templates/\n                [name='view-mail-templates']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                view-mail-template/&lt;int:obj_id&gt;/\n                [name='view-mail-template']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                create-mail-template/\n                [name='create-mail-template']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                duplicate-mail-template/&lt;int:obj_id&gt;/\n                [name='duplicate-mail-template']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                delete-mail-template/\n                [name='delete-mail-template']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/company-create/\n                [name='company-create']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/company-view/\n                [name='company-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/company-update/&lt;int:id&gt;/\n                [name='company-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/company-delete/&lt;int:obj_id&gt;/\n                [name='company-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/department-view/\n                [name='department-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/department-creation/\n                [name='department-creation']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/department-update/&lt;int:id&gt;/\n                [name='department-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                department-delete/&lt;int:obj_id&gt;/\n                [name='department-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/job-position-creation/\n                [name='job-position-creation']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/job-position-view/\n                [name='job-position-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/job-position-update/&lt;int:id&gt;/\n                [name='job-position-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                job-position-delete/&lt;int:obj_id&gt;/\n                [name='job-position-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/job-role-create/\n                [name='job-role-create']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/job-role-view/\n                [name='job-role-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/job-role-update/&lt;int:id&gt;/\n                [name='job-role-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                job-role-delete/&lt;int:obj_id&gt;/\n                [name='job-role-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/work-type-view/\n                [name='work-type-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/work-type-create/\n                [name='work-type-create']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/work-type-update/&lt;int:id&gt;/\n                [name='work-type-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                work-type-delete/&lt;int:obj_id&gt;/\n                [name='work-type-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                add-remove-work-type-fields\n                [name='add-remove-work-type-fields']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/rotating-work-type-create/\n                [name='rotating-work-type-create']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/rotating-work-type-view/\n                [name='rotating-work-type-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/rotating-work-type-update/&lt;int:id&gt;/\n                [name='rotating-work-type-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-work-type-delete/&lt;int:obj_id&gt;/\n                [name='rotating-work-type-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                employee/rotating-work-type-assign/\n                [name='rotating-work-type-assign']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-work-type-assign-add\n                [name='rotating-work-type-assign-add']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-work-type-assign-view\n                [name='rotating-work-type-assign-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-work-type-assign-export\n                [name='rotating-work-type-assign-export']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/rotating-work-type-assign-update/&lt;int:id&gt;/\n                [name='rotating-work-type-assign-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-work-type-assign-duplicate/&lt;int:obj_id&gt;/\n                [name='rotating-work-type-assign-duplicate']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-work-type-assign-archive/&lt;int:obj_id&gt;/\n                [name='rotating-work-type-assign-archive']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-work-type-assign-bulk-archive\n                [name='rotating-shift-work-type-bulk-archive']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-work-type-assign-bulk-delete\n                [name='rotating-shift-work-type-bulk-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-work-type-assign-delete/&lt;int:obj_id&gt;/\n                [name='rotating-work-type-assign-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/employee-type-view/\n                [name='employee-type-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/employee-type-create/\n                [name='employee-type-create']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/employee-type-update/&lt;int:id&gt;/\n                [name='employee-type-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                employee-type-delete/&lt;int:obj_id&gt;/\n                [name='employee-type-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/employee-shift-view/\n                [name='employee-shift-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/employee-shift-create/\n                [name='employee-shift-create']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/employee-shift-update/&lt;int:id&gt;/\n                [name='employee-shift-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                employee-shift-delete/&lt;int:obj_id&gt;/\n                [name='employee-shift-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/employee-shift-schedule-view/\n                [name='employee-shift-schedule-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/employee-shift-schedule-create/\n                [name='employee-shift-schedule-create']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/employee-shift-schedule-update/&lt;int:id&gt;/\n                [name='employee-shift-schedule-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                employee-shift-schedule-delete/&lt;int:obj_id&gt;/\n                [name='employee-shift-schedule-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/rotating-shift-create/\n                [name='rotating-shift-create']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                add-remove-shift-fields\n                [name='add-remove-shift-fields']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/rotating-shift-view/\n                [name='rotating-shift-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/rotating-shift-update/&lt;int:id&gt;/\n                [name='rotating-shift-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-shift-delete/&lt;int:obj_id&gt;/\n                [name='rotating-shift-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                employee/rotating-shift-assign/\n                [name='rotating-shift-assign']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-shift-assign-add\n                [name='rotating-shift-assign-add']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-shift-assign-view\n                [name='rotating-shift-assign-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-shift-assign-info-export\n                [name='rotating-shift-assign-info-export']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-shift-assign-info-import\n                [name='rotating-shift-assign-info-import']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/rotating-shift-assign-update/&lt;int:id&gt;/\n                [name='rotating-shift-assign-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-shift-assign-duplicate/&lt;int:obj_id&gt;/\n                [name='rotating-shift-assign-duplicate']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-shift-assign-archive/&lt;int:obj_id&gt;/\n                [name='rotating-shift-assign-archive']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-shift-assign-bulk-archive\n                [name='rotating-shift-assign-bulk-archive']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-shift-assign-bulk-delete\n                [name='rotating-shift-assign-bulk-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rotating-shift-assign-delete/&lt;int:obj_id&gt;/\n                [name='rotating-shift-assign-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                work-type-request\n                [name='work-type-request']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                work-type-request-duplicate/&lt;int:obj_id&gt;/\n                [name='work-type-request-duplicate']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                employee/work-type-request-view/\n                [name='work-type-request-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                work-type-request-info-export\n                [name='work-type-request-info-export']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                work-type-request-search\n                [name='work-type-request-search']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                work-type-request-cancel/&lt;int:id&gt;/\n                [name='work-type-request-cancel']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                work-type-request-bulk-cancel\n                [name='work-type-request-bulk-cancel']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                work-type-request-approve/&lt;int:id&gt;/\n                [name='work-type-request-approve']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                work-type-request-bulk-approve\n                [name='work-type-request-bulk-approve']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                work-type-request-update/&lt;int:work_type_request_id&gt;/\n                [name='work-type-request-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                work-type-request-delete/&lt;int:obj_id&gt;/\n                [name='work-type-request-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                work-type-request-single-view/&lt;int:obj_id&gt;/\n                [name='work-type-request-single-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                work-type-request-bulk-delete\n                [name='work-type-request-bulk-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-request\n                [name='shift-request']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-request-duplicate/&lt;int:obj_id&gt;/\n                [name='shift-request-duplicate']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-request-reallocate\n                [name='shift-request-reallocate']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                update-employee-allocation\n                [name='update-employee-allocation']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                employee/shift-request-view/\n                [name='shift-request-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-request-info-export\n                [name='shift-request-info-export']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-request-search\n                [name='shift-request-search']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-request-details/&lt;int:id&gt;/\n                [name='shift-request-details']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-allocation-request-details/&lt;int:id&gt;/\n                [name='shift-allocation-request-details']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-request-update/&lt;int:shift_request_id&gt;/\n                [name='shift-request-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-allocation-request-update/&lt;int:shift_request_id&gt;/\n                [name='shift-allocation-request-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-request-cancel/&lt;int:id&gt;/\n                [name='shift-request-cancel']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-allocation-request-cancel/&lt;int:id&gt;/\n                [name='shift-allocation-request-cancel']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-request-bulk-cancel\n                [name='shift-request-bulk-cancel']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-request-approve/&lt;int:id&gt;/\n                [name='shift-request-approve']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-allocation-request-approve/&lt;int:id&gt;/\n                [name='shift-allocation-request-approve']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-request-bulk-approve\n                [name='shift-request-bulk-approve']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-request-delete/&lt;int:id&gt;/\n                [name='shift-request-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-request-bulk-delete\n                [name='shift-request-bulk-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                notifications\n                [name='notifications']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                clear-notifications\n                [name='clear-notifications']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                delete-all-notifications\n                [name='delete-all-notifications']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                read-notifications\n                [name='read-notifications']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                mark-as-read-notification/&lt;int:notification_id&gt;\n                [name='mark-as-read-notification']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                mark-as-read-notification-json/\n                [name='mark-as-read-notification-json']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                all-notifications\n                [name='all-notifications']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                delete-notifications/&lt;id&gt;/\n                [name='delete-notifications']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/general-settings/\n                [name='general-settings']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/date-settings/\n                [name='date-settings']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/save-date/\n                [name='save_date_format']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/get-date-format/\n                [name='get-date-format']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/save-time/\n                [name='save_time_format']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/get-time-format/\n                [name='get-time-format']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                history-field-settings\n                [name='history-field-settings']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                enable-account-block-unblock\n                [name='enable-account-block-unblock']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                enable-profile-edit-feature\n                [name='enable-profile-edit-feature']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rwork-individual-view/&lt;int:instance_id&gt;/\n                [name='rwork-individual-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                rshit-individual-view/&lt;int:instance_id&gt;/\n                [name='rshift-individual-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-select/\n                [name='shift-select']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-select-filter/\n                [name='shift-select-filter']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                work-type-select/\n                [name='work-type-select']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                work-type-filter/\n                [name='work-type-select-filter']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                r-shift-select/\n                [name='r-shift-select']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                r-shift-select-filter/\n                [name='r-shift-select-filter']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                r-work-type-select/\n                [name='r-work-type-select']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                r-work-type-filter/\n                [name='r-work-type-select-filter']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/tag-view/\n                [name='tag-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/helpdesk-tag-view/\n                [name='helpdesk-tag-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                tag-create\n                [name='tag-create']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                tag-update/&lt;int:tag_id&gt;\n                [name='tag-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                tag-delete/&lt;int:obj_id&gt;\n                [name='tag-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                audit-tag-create\n                [name='audit-tag-create']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                audit-tag-update/&lt;int:tag_id&gt;\n                [name='audit-tag-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                audit-tag-delete/&lt;int:obj_id&gt;\n                [name='audit-tag-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                configuration/multiple-approval-condition\n                [name='multiple-approval-condition']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                configuration/condition-value-fields\n                [name='condition-value-fields']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                configuration/add-more-approval-managers\n                [name='add-more-approval-managers']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                configuration/remove-approval-manager\n                [name='remove-approval-manager']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                configuration/hx-multiple-approval-condition\n                [name='hx-multiple-approval-condition']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                multiple-level-approval-create\n                [name='multiple-level-approval-create']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                multiple-level-approval-edit/&lt;int:condition_id&gt;\n                [name='multiple-level-approval-edit']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                multiple-level-approval-delete/&lt;int:condition_id&gt;\n                [name='multiple-level-approval-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-request-add-comment/&lt;int:shift_id&gt;/\n                [name='shift-request-add-comment']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                view-shift-comment/&lt;int:shift_id&gt;/\n                [name='view-shift-comment']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                delete-shift-comment-file/\n                [name='delete-shift-comment-file']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                view-work-type-comment/&lt;int:work_type_id&gt;/\n                [name='view-work-type-comment']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                delete-work-type-comment-file/\n                [name='delete-work-type-comment-file']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                shift-request-delete-comment/&lt;int:comment_id&gt;/\n                [name='shift-request-delete-comment']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                worktype-request-add-comment/&lt;int:worktype_id&gt;/\n                [name='worktype-request-add-comment']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                worktype-request-delete-comment/&lt;int:comment_id&gt;/\n                [name='worktype-request-delete-comment']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                dashboard-shift-request\n                [name='dashboard-shift-request']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                dashboard-work-type-request\n                [name='dashboard-work-type-request']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/pagination-settings-view/\n                [name='pagination-settings-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/action-type/\n                [name='action-type']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                action-type-create\n                [name='action-type-create']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                action-type-update/&lt;int:act_id&gt;\n                [name='action-type-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                action-type-delete/&lt;int:act_id&gt;\n                [name='action-type-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                pagination-settings-view\n                [name='pagination-settings-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                announcement-list\n                [name='announcement-list']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                create-announcement\n                [name='create-announcement']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                delete-announcement/&lt;int:anoun_id&gt;\n                [name='delete-announcement']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                update-announcement/&lt;int:anoun_id&gt;\n                [name='update-announcement']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                remove-announcement-file/&lt;int:obj_id&gt;/&lt;int:attachment_id&gt;\n                [name='remove-announcement-file']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                announcement-add-comment/&lt;int:anoun_id&gt;/\n                [name='announcement-add-comment']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                announcement-view-comment/&lt;int:anoun_id&gt;/\n                [name='announcement-view-comment']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                announcement-single-view/&lt;int:anoun_id&gt;\n                [name='announcement-single-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                announcement-single-view/\n                [name='announcement-single-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                announcement-delete-comment/&lt;int:comment_id&gt;/\n                [name='announcement-delete-comment']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                announcement-viewed-by\n                [name='announcement-viewed-by']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                driver-viewed\n                [name='driver-viewed']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                dashboard-components-toggle\n                [name='dashboard-components-toggle']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                employee-chart-show\n                [name='employee-chart-show']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/enable-biometric-attendance/\n                [name='enable-biometric-attendance']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                settings/activate-biometric-attendance\n                [name='activate-biometric-attendance']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                emp-workinfo-complete\n                [name='emp-workinfo-complete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                get-eaglora-installed-apps/\n                [name='get-eaglora-installed-apps']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                configuration/holiday-view\n                [name='holiday-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                configuration/holidays-excel-template\n                [name='holidays-excel-template']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                holidays-info-import\n                [name='holidays-info-import']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                holiday-info-export\n                [name='holiday-info-export']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                get-upcoming-holidays\n                [name='get-upcoming-holidays']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                holiday-creation\n                [name='holiday-creation']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                holiday-update/&lt;int:obj_id&gt;\n                [name='holiday-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                duplicate-holiday/&lt;int:obj_id&gt;\n                [name='duplicate-holiday']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                holiday-delete/&lt;int:obj_id&gt;\n                [name='holiday-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                holidays-bulk-delete\n                [name='holidays-bulk-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                holiday-filter\n                [name='holiday-filter']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                holiday-select/\n                [name='holiday-select']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                holiday-select-filter/\n                [name='holiday-select-filter']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                company-leave-creation\n                [name='company-leave-creation']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                configuration/company-leave-view\n                [name='company-leave-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                company-leave-update/&lt;int:id&gt;\n                [name='company-leave-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                company-leave-delete/&lt;int:id&gt;\n                [name='company-leave-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                company-leave-filter\n                [name='company-leave-filter']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                view-penalties\n                [name='view-penalties']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                ^media/(?P&lt;path&gt;.*)$\n                [name='protected_media']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                update-selected-company\n                [name='update-selected-company']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                configuration/mail-automations\n                [name='mail-automations']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                mail-automations-nav\n                [name='mail-automations-nav']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                create-automation\n                [name='create-automation']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                update-automation/&lt;int:pk&gt;/\n                [name='update-automation']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                mail-automations-list-view\n                [name='mail-automations-list-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                get-to-mail-field\n                [name='get-to-mail-field']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                automation-detailed-view/&lt;int:pk&gt;/\n                [name='automation-detailed-view']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                delete-automation/&lt;int:pk&gt;/\n                [name='delete-automation']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                load-automations\n                [name='load-automations']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                refresh-automations\n                [name='refresh-automations']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                toggle-columns\n                [name='toggle-columns']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                active-tab\n                [name='active-tab']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                active-group\n                [name='cbv-active-group']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                reload-field\n                [name='reload-field']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                reload-messages\n                [name='reload-messages']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                saved-filter/\n                [name='saved-filter']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                saved-filter/&lt;int:pk&gt;/\n                [name='saved-filter-update']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                delete-saved-filter/&lt;int:pk&gt;/\n                [name='delete-saved-filter']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                active-hnv-view-type/\n                [name='active-hnv-view-type']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                search-in-instance-ids\n                [name='search-in-instance-ids']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                last-applied-filter\n                [name='last-applied-filter']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                generic-delete\n                [name='generic-delete']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                eaglora-history-revert/&lt;int:pk&gt;/&lt;int:history_id&gt;/\n                [name='history-revert']\n            \n          </li>\n        \n          <li>\n            \n                employee/\n                \n            \n          </li>\n        \n          <li>\n            \n                ai/\n                \n            \n          </li>\n        \n          <li>\n            \n                eaglora-widget/\n                \n            \n          </li>\n        \n          <li>\n            \n                ^inbox/notifications/\n                \n            \n          </li>\n        \n          <li>\n            \n                i18n/\n                \n            \n          </li>\n        \n          <li>\n            \n                health/\n                \n            \n          </li>\n        \n          <li>\n            \n                recruitment/\n                \n            \n          </li>\n        \n          <li>\n            \n                leave/\n                \n            \n          </li>\n        \n          <li>\n            \n                pms/\n                \n            \n          </li>\n        \n          <li>\n            \n                onboarding/\n                \n            \n          </li>\n        \n          <li>\n            \n                asset/\n                \n            \n          </li>\n        \n          <li>\n            \n                attendance/\n                \n            \n          </li>\n        \n          <li>\n            \n                payroll/\n                \n            \n          </li>\n        \n          <li>\n            \n                report/\n                \n            \n          </li>\n        \n          <li>\n            \n                ai/\n                \n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                user-accessibility/\n                [name='user-accessibility']\n            \n          </li>\n        \n          <li>\n            \n                \n                \n            \n                get-initial-accessibility-data\n                [name='get-initial-accessibility-data']\n            \n          </li>\n        \n          <li>\n            \n                biometric/\n                \n            \n          </li>\n        \n          <li>\n            \n                helpdesk/\n                \n            \n          </li>\n        \n          <li>\n            \n                offboarding/\n                \n            \n          </li>\n        \n          <li>\n            \n                backup/\n                \n            \n          </li>\n        \n          <li>\n            \n                project/\n                \n            \n          </li>\n        \n          <li>\n            \n                project/\n                \n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                auth/\n                \n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                asset/\n                \n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                base/\n                \n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                employees/\n                [name='api-employees-list']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                employees/&lt;int:pk&gt;/\n                [name='api-employee-detail']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                employee-type/&lt;int:pk&gt;\n                [name='api-employees']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                employee-type/\n                [name='api-employees']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                list/employees/\n                [name='api-employee-list-detailed']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                employee-bank-details/&lt;int:pk&gt;/\n                [name='api-employee-bank-details-detail']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                employee-work-information/\n                [name='api-employee-work-information-list']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                employee-work-information/&lt;int:pk&gt;/\n                [name='api-employee-work-information-detail']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                employee-work-info-export/\n                [name='api-employee-work-info-export']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                employee-work-info-import/\n                [name='api-employee-work-info-import']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                employee-bulk-update/\n                [name='api-employee-bulk-update']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                disciplinary-action/\n                [name='api-disciplinary-action-list']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                disciplinary-action/&lt;int:pk&gt;/\n                [name='api-disciplinary-action-detail']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                disciplinary-action-type/\n                [name='api-disciplinary-action-type']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                disciplinary-action-type/&lt;int:pk&gt;/\n                [name='api-disciplinary-action-type']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                policies/\n                [name='api-policy-list']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                policies/&lt;int:pk&gt;/\n                [name='api-policy-detail']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                document-request/\n                [name='api-document-request-list']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                document-request/&lt;int:pk&gt;/\n                [name='api-document-request-detail']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                document-bulk-approve-reject/\n                [name='api-document-bulk-approve-reject']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                document-request-approve-reject/&lt;int:id&gt;/&lt;str:status&gt;/\n                [name='api-document-request-approve-reject']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                documents/\n                [name='api-document-list']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                documents/&lt;int:pk&gt;/\n                [name='api-document-detail']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                employee-bulk- archive/&lt;str:is_active&gt;/\n                [name='api-employee-bulk-archive']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                employee-archive/&lt;int:id&gt;/&lt;str:is_active&gt;/\n                [name='api-employee-archive']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                employee-selector/\n                [name='api-employee-selector']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                employee/\n                \n            \n                manager-check/\n                [name='api-manager-check']\n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                notifications/\n                \n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                payroll/\n                \n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                attendance/\n                \n            \n          </li>\n        \n          <li>\n            \n                api/\n                \n            \n                leave/\n                \n            \n          </li>\n        \n          <li>\n            \n                api/geofencing/\n                \n            \n          </li>\n        \n          <li>\n            \n                api/facedetection/\n                \n            \n          </li>\n        \n      </ol>\n      <p>\n        \n          The current path, <code>api/employee/export/</code>,\n        \n        didn’t match any of these.\n      </p>\n    \n  </div>\n\n  <div id=\"explanation\">\n    <p>\n      You’re seeing this error because you have <code>DEBUG = True</code> in\n      your Django settings file. Change that to <code>False</code>, and Django\n      will display a standard 404 page.\n    </p>\n  </div>\n</body>\n</html>\n", "content_type": "text", "summary": "Received 67818 characters of text data", "error": {"message": "HTTP 404: Not Found", "status_code": 404, "response_text": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta http-equiv=\"content-type\" content=\"text/html; charset=utf-8\">\n  <title>Page not found at /api/employee/export/</title>\n  <meta name=\"robots\" content=\"NONE,NOARCHIVE\">\n  <style type=\"text/css\">\n    html * { padding:0; margin:0; }\n    body * { padding:10px 20px; }\n    body * * { padding:0; }\n    body { font:small sans-serif; background:#eee; color:#000; }\n    body>div { border-bottom:1px solid #ddd; }\n    h1 { font-weight:normal; margin-bottom:.4em; "}, "execution_metadata": {"execution_time": 0.11146116256713867, "timestamp": "2025-07-13T09:21:49.719101", "user": "anonymous", "request_id": "req_20250713_092149_anon_2"}}}], "comprehensive_suite": {"success": false, "message": "Comprehensive test suite completed"}}, "summary": {"total_tests": 13, "successful_tests": 13, "failed_tests": 0, "overall_success_rate": 100.0}, "recommendations": ["🎉 Excellent! System is ready for production."]}