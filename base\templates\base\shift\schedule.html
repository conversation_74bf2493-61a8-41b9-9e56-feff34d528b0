{% extends 'settings.html' %}
{% load i18n %}
{% block settings %}{% load static %}
<div class="oh-inner-sidebar-content">
    {% if perms.base.view_employeeshiftschedule %}
        <div class="oh-inner-sidebar-content__header d-flex justify-content-between align-items-center">
            <h2 class="oh-inner-sidebar-content__title">{% trans "Shift Schedule" %}</h2>
            {% if perms.base.add_employeeshiftschedule %}
                <button
                    class="oh-btn oh-btn--secondary oh-btn--shadow"
                    data-toggle="oh-modal-toggle"
                    data-target="#objectCreateModal"
                    hx-get="{% url 'employee-shift-schedule-create' %}"
                    hx-target="#objectCreateModalTarget"
                >
                    <ion-icon name="add-outline" class="me-1"></ion-icon>
                    {% trans "Create" %}
                </button>
            {% endif %}
        </div>
        {% if shift_schedule %}
            {% include 'base/shift/schedule_view.html' %}
        {% else %}
            <div style="display: flex; flex-direction: column; justify-content: center; align-items: center; height: 100%;">
                <img style="display: block; width: 15%; margin: 20px auto; filter: opacity(0.5);" src="{% static 'images/ui/shift_schedule.png' %}" class="" alt="Page not found. 404." />
                <h5 class="oh-404__subtitle">{% trans "There is no shift schedule at this moment." %}</h5>
            </div>
        {% endif %}
    {% endif %}
</div>
<script>
    function toggleDivVisibility(element) {
        var isChecked = $(element).prop('checked');
        if (isChecked) {
            $(element).closest('form').find('#id_auto_punch_out_time_parent_div', '#id_auto_punch_out_time').show();
        } else {
            $(element).closest('form').find('#id_auto_punch_out_time_parent_div', '#id_auto_punch_out_time').hide();
        }
    }
</script>
{% endblock settings %}
