"""
Template tags for AI Assistant
"""

from django import template
from django.utils.safestring import mark_safe
from django.urls import reverse

from ..models import AIAssistantSettings

register = template.Library()


@register.inclusion_tag('ai_assistant/chatbox.html', takes_context=True)
def ai_chatbox(context):
    """
    Render the AI assistant chatbox.
    
    Usage: {% ai_chatbox %}
    """
    request = context.get('request')
    user = request.user if request else None
    
    # Check if AI assistant is enabled
    settings = AIAssistantSettings.get_settings()
    
    return {
        'user': user,
        'settings': settings,
        'ai_command_url': reverse('ai_assistant:ai-command'),
        'is_authenticated': user.is_authenticated if user else False,
        'current_page': request.path if request else '',
        'current_module': context.get('current_module', ''),
    }


@register.simple_tag
def ai_assistant_enabled():
    """
    Check if AI assistant is enabled.
    
    Usage: {% ai_assistant_enabled as enabled %}
    """
    settings = AIAssistantSettings.get_settings()
    return settings.is_active if settings else False


@register.simple_tag
def ai_assistant_model():
    """
    Get the current AI model being used.
    
    Usage: {% ai_assistant_model as model %}
    """
    settings = AIAssistantSettings.get_settings()
    return settings.ollama_model if settings else 'mistral'


@register.filter
def ai_confidence_color(confidence):
    """
    Get color class based on confidence score.
    
    Usage: {{ confidence|ai_confidence_color }}
    """
    try:
        conf = float(confidence)
        if conf >= 0.8:
            return 'text-success'
        elif conf >= 0.6:
            return 'text-warning'
        else:
            return 'text-danger'
    except (ValueError, TypeError):
        return 'text-muted'


@register.filter
def ai_intent_icon(intent):
    """
    Get icon class based on intent.
    
    Usage: {{ intent|ai_intent_icon }}
    """
    icon_mapping = {
        'search': 'fas fa-search',
        'fill_form': 'fas fa-edit',
        'run_action': 'fas fa-play',
        'export': 'fas fa-download',
        'import': 'fas fa-upload',
        'navigate': 'fas fa-compass',
        'sort': 'fas fa-sort',
        'reply': 'fas fa-comment',
        'error': 'fas fa-exclamation-triangle',
    }
    return icon_mapping.get(intent, 'fas fa-question')


@register.inclusion_tag('ai_assistant/chat_history_widget.html')
def ai_chat_history(user, limit=10):
    """
    Render chat history widget.
    
    Usage: {% ai_chat_history user 5 %}
    """
    from ..models import ChatHistory
    
    if not user or not user.is_authenticated:
        return {'chat_history': []}
    
    chat_history = ChatHistory.objects.filter(
        user=user
    ).order_by('-created_at')[:limit]
    
    return {
        'chat_history': chat_history,
        'user': user,
    }
