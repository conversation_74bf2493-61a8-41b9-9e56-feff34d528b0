import pandas as pd
from django.urls import path
from django.template.response import TemplateResponse
from django.contrib import admin
from .models import Sales
from django.utils.html import format_html
from django.urls import reverse


import json
from django.http import JsonResponse

from django.urls import reverse

from pivot import PivotTableMixin
# class SalesAdmin(admin.ModelAdmin):
#     list_display = ("region", "product", "amount", "date", "images")

#     def changelist_view(self, request, extra_context=None):
#         extra_context = extra_context or {}
#         extra_context['pivot_url'] = reverse('admin:sales-pivot')
#         return super().changelist_view(request, extra_context=extra_context)

#     def get_urls(self):
#         urls = super().get_urls()
#         custom_urls = [
#             path('pivot/', self.admin_site.admin_view(self.pivot_view), name='sales-pivot'),
#             path('pivot-data/', self.admin_site.admin_view(self.pivot_data), name='sales-pivot-data'),
#         ]
#         return custom_urls + urls

#     def pivot_view(self, request):
#         context = dict(
#             self.admin_site.each_context(request),
#             title='Interactive Sales Pivot Table',
#         )
#         return TemplateResponse(request, "admin/sales_pivot_dynamic.html", context)

#     def pivot_data(self, request):
#         qs = Sales.objects.all().values('region', 'product', 'amount', 'date', 'images')
#         return JsonResponse(list(qs), safe=False)


class SalesAdmin(PivotTableMixin, admin.ModelAdmin):
    list_display = ("region", "product", "amount", "date", "images")
    pivot_fields = ['region', 'product', 'amount', 'date', 'images']

admin.site.register(Sales, SalesAdmin)