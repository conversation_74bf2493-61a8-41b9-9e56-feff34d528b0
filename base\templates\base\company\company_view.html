{% load i18n %}
<div class="oh-sticky-table">
	<div class="oh-sticky-table__table oh-table--sortable">
		<div class="oh-sticky-table__thead">
			<div class="oh-sticky-table__tr">
				<div class="oh-sticky-table__th">{% trans "Company" %}</div>
				<div class="oh-sticky-table__th">{% trans "Is Hq" %}</div>
				<div class="oh-sticky-table__th">{% trans "Address" %}</div>
				<div class="oh-sticky-table__th">{% trans "Country" %}</div>
				<div class="oh-sticky-table__th">{% trans "State" %}</div>
				<div class="oh-sticky-table__th">{% trans "City" %}</div>
				<div class="oh-sticky-table__th">{% trans "Zip" %}</div>
				{% if perms.base.change_company or perms.base.delete_company %}
					<div class="oh-sticky-table__th oh-sticky-table__right">{% trans "Actions" %}</div>
				{% endif %}
			</div>
		</div>
		<div class="oh-sticky-table__tbody">
			{% for company in companies %}
			<div class="oh-sticky-table__tr" draggable="true">
				<div class="oh-sticky-table__sd">
					<div class="mr-1">
						<img src="{{company.icon.url}}" style="width: 30px; border-radius: 100%"
							class="oh-profile__image" alt="Username" />{{company.company}}
					</div>
				</div>
				<div class="oh-sticky-table__td">{{company.hq}}</div>
				<div class="oh-sticky-table__td">{{company.address}}</div>
				<div class="oh-sticky-table__td">{{company.country}}</div>
				<div class="oh-sticky-table__td">{{company.state}}</div>
				<div class="oh-sticky-table__td">{{company.city}}</div>
				<div class="oh-sticky-table__td">{{company.zip}}</div>
				{% if perms.base.change_company or perms.base.delete_company %}
					<div class="oh-sticky-table__td oh-sticky-table__right">
						<div class="oh-btn-group">
							{% if perms.base.change_company %}
								<button data-toggle="oh-modal-toggle" data-target="#objectUpdateModal"
									hx-get="{% url 'company-update' company.id %}" hx-target="#objectUpdateModalTarget"
									class="oh-btn oh-btn--light-bkg w-50" title="{% trans 'Edit' %}">
									<ion-icon name="create-outline"></ion-icon>
								</button>
							{% endif %}
							{% if perms.base.delete_company %}
								<form action="{% url 'company-delete' company.id %}"
									onsubmit="return confirm('{% trans "Are you sure you want to delete this company?" %}');"
									method='post' class='w-50'>
									{% csrf_token %}
									<button type="submit" class="oh-btn oh-btn--danger-outline oh-btn--light-bkg w-100"
										title="{% trans 'Remove' %}">
										<ion-icon name="trash-outline"></ion-icon>
									</button>
								</form>
							{% endif %}
						</div>
					</div>
				{% endif %}
			</div>
			{% endfor %}
		</div>
	</div>
</div>
