"""
AI Assistant URL Configuration
"""

from django.urls import path
from . import views

app_name = 'ai_assistant'

urlpatterns = [
    # Main AI command endpoint
    path('command/', views.AICommandView.as_view(), name='ai-command'),

    # Function-based view for backward compatibility
    path('command-func/', views.ai_command, name='ai-command-func'),

    # Chat history views
    path('history/', views.chat_history, name='chat-history'),
    path('history/clear/', views.clear_chat_history, name='clear-history'),
]