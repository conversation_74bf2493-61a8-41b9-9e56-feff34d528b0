"""
AI Assistant URL Configuration
"""

from django.urls import path
from . import views

app_name = 'ai_assistant'

urlpatterns = [
    # Test endpoint (no auth required)
    path('test/', views.test_endpoint, name='ai-test'),

    # Main AI command endpoint
    path('command/', views.AICommandView.as_view(), name='ai-command'),

    # Function-based view for backward compatibility
    path('command-func/', views.ai_command, name='ai-command-func'),

    # Chat history views
    path('history/', views.chat_history, name='chat-history'),
    path('history/clear/', views.clear_chat_history, name='clear-history'),

    # Natural Language API Converter Endpoints with Comprehensive Metadata
    path(
        'api/convert/',
        views.NaturalLanguageAPIView.as_view(),
        name='nl_api_convert',
        kwargs={
            "description": "Convert natural language to structured API request",
            "filters": ["confidence", "intent", "module", "timestamp"],
            "body_fields": ["natural_language", "context", "execute", "user_id"],
            "search_fields": ["natural_language", "intent"],
            "sort_fields": ["timestamp", "confidence", "execution_time"],
            "method": "POST",
            "response_format": "json",
            "required_fields": ["natural_language"],
            "field_types": {
                "natural_language": "string",
                "context": "object",
                "execute": "boolean",
                "confidence": "float"
            },
            "example_request": {
                "natural_language": "Show all employees from HR department",
                "context": {"current_page": "/employee/", "user_role": "admin"},
                "execute": False
            },
            "example_response": {
                "url": "/api/employee/",
                "method": "GET",
                "filters": {"department__icontains": "hr"},
                "confidence": 0.95
            }
        }
    ),

    path(
        'api/execute/',
        views.APIExecutorView.as_view(),
        name='api_execute',
        kwargs={
            "description": "Execute API request generated from natural language",
            "filters": ["status", "method", "url", "execution_time"],
            "body_fields": ["api_request", "user_context", "timeout"],
            "search_fields": ["url", "method", "status"],
            "sort_fields": ["execution_time", "timestamp", "status_code"],
            "method": "POST",
            "response_format": "json",
            "required_fields": ["api_request"],
            "field_types": {
                "api_request": "object",
                "user_context": "object",
                "timeout": "integer"
            },
            "example_request": {
                "api_request": {
                    "url": "/api/employee/",
                    "method": "GET",
                    "filters": {"department": "hr"}
                },
                "user_context": {"user_id": 1},
                "timeout": 30
            }
        }
    ),

    path(
        'api/batch/',
        views.BatchProcessorView.as_view(),
        name='batch_process',
        kwargs={
            "description": "Process multiple natural language requests in batch",
            "filters": ["batch_size", "success_rate", "status"],
            "body_fields": ["requests", "execute_all", "context", "parallel"],
            "search_fields": ["requests"],
            "sort_fields": ["timestamp", "success_rate", "total_execution_time"],
            "method": "POST",
            "response_format": "json",
            "required_fields": ["requests"],
            "field_types": {
                "requests": "array",
                "execute_all": "boolean",
                "context": "object",
                "parallel": "boolean"
            },
            "example_request": {
                "requests": [
                    "Show all employees",
                    "List leave requests",
                    "Export attendance data"
                ],
                "execute_all": True,
                "parallel": False
            }
        }
    ),

    path(
        'api/metadata/',
        views.APIMetadataView.as_view(),
        name='api_metadata',
        kwargs={
            "description": "Get comprehensive API metadata for all available endpoints",
            "filters": ["module", "method", "endpoint_type", "has_filters"],
            "body_fields": [],
            "search_fields": ["url", "description", "module"],
            "sort_fields": ["url", "module", "method"],
            "method": "GET",
            "response_format": "json",
            "field_types": {},
            "example_response": {
                "endpoints": {
                    "/api/employee/": {
                        "methods": ["GET", "POST"],
                        "filters": ["name", "department", "email"],
                        "sort_fields": ["name", "joining_date"],
                        "description": "Employee management endpoint"
                    }
                }
            }
        }
    ),

    path(
        'api/test/',
        views.TestSuiteView.as_view(),
        name='test_suite',
        kwargs={
            "description": "Run comprehensive test suite for NL API converter",
            "filters": ["test_type", "success_rate", "status"],
            "body_fields": ["test_scenarios", "run_all", "test_categories"],
            "search_fields": ["test_name", "test_category"],
            "sort_fields": ["success_rate", "execution_time", "timestamp"],
            "method": "POST",
            "response_format": "json",
            "field_types": {
                "test_scenarios": "array",
                "run_all": "boolean",
                "test_categories": "array"
            },
            "example_request": {
                "run_all": True,
                "test_categories": ["employee", "leave", "attendance"]
            }
        }
    ),
]