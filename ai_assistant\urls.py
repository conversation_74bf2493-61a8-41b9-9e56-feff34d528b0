"""
AI Assistant URL Configuration
"""

from django.urls import path
from . import views

app_name = 'ai_assistant'

urlpatterns = [
    path('command/', views.AICommandView.as_view(), name='ai-command'),
    path('command-func/', views.ai_command, name='ai-command-func'),  # Function-based alternative
    path('history/', views.chat_history, name='chat-history'),
    path('clear-history/', views.clear_chat_history, name='clear-history'),
]
