{% load i18n %}{% load eaglorafilters %}
<div class="oh-sticky-table">
	<div class="oh-sticky-table__table oh-table--sortable">
		<div class="oh-sticky-table__thead">
			<div class="oh-sticky-table__tr">
				<div class="oh-sticky-table__th">{% trans "Title" %}</div>
				<div class="oh-sticky-table__th">{% trans "Highlight" %}</div>
				{% if perms.eaglora_audit.delete_audittag or perms.eaglora_audit.delete_audittag %}
					<div class="oh-sticky-table__th">{% trans "Actions" %}</div>
				{% endif %}
			</div>
		</div>
		<div class="oh-sticky-table__tbody">
			{% for tag in audittags %}
				<div class="oh-sticky-table__tr" draggable="true" id="auditTagTr{{tag.id}}">
					<div class="oh-sticky-table__td">{{tag}}</div>
					<div class="oh-sticky-table__td">{{tag.highlight|yes_no}}</div>
					{% if perms.eaglora_audit.delete_audittag or perms.eaglora_audit.delete_audittag %}
						<div class="oh-sticky-table__td">
							<div class="oh-btn-group">
								{% if perms.eaglora_audit.change_audittag %}
									<a hx-get="{% url 'audit-tag-update' tag.id %}" hx-target="#audittagEditForm"
										data-toggle="oh-modal-toggle" data-target="#audittagEditModal" type="button"
										class="oh-btn oh-btn--light-bkg w-50" title="{% trans 'Edit' %}">
										<ion-icon name="create-outline"></ion-icon></a>
								{% endif %}
								{% if perms.eaglora_audit.delete_audittag %}
									<form hx-confirm="{% trans 'Are you sure you want to delete this history tag ?' %}"
										hx-post="{% url 'audit-tag-delete' tag.id %}" hx-swap="outerHTML"
										hx-on-htmx-after-request="reloadMessage(this);" hx-target="#auditTagTr{{tag.id}}" class="w-50">
										{% csrf_token %}
										<button type="submit" class="oh-btn oh-btn--danger-outline oh-btn--light-bkg w-100"
											title="{% trans 'Remove' %}">
											<ion-icon name="trash-outline"></ion-icon>
										</button>
									</form>
								{% endif %}
							</div>
						</div>
					{% endif %}
				</div>
			{% endfor %}
		</div>
	</div>
</div>
