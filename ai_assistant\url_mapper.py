"""
URL Mapper for AI Assistant

This module provides comprehensive URL mappings for all HRMS modules
based on the actual URL patterns found in the codebase.
"""

from typing import Dict, Any, Optional, List
import logging

logger = logging.getLogger(__name__)


class URLMapper:
    """Maps intents and modules to actual HRMS URLs."""
    
    def __init__(self):
        self.url_mappings = self._build_comprehensive_url_mappings()
        self.dashboard_urls = self._build_dashboard_mappings()
        self.form_urls = self._build_form_mappings()
        self.export_urls = self._build_export_mappings()
        
    def get_url(self, intent: str, module: str, action_type: str = None) -> Optional[str]:
        """
        Get the appropriate URL for given intent and module.
        
        Args:
            intent: The detected intent (search, fill_form, export, etc.)
            module: The target module (employee, leave, attendance, etc.)
            action_type: Optional specific action type
            
        Returns:
            URL string or None if not found
        """
        
        # Primary URL mapping
        module_urls = self.url_mappings.get(module, {})
        url = module_urls.get(intent)
        
        if url:
            return url
            
        # Fallback to dashboard if no specific URL found
        if intent in ['navigate', 'search']:
            return self.dashboard_urls.get(module)
            
        return None
    
    def _build_comprehensive_url_mappings(self) -> Dict[str, Dict[str, str]]:
        """Build comprehensive URL mappings based on actual codebase URLs."""
        
        return {
            'employee': {
                'search': '/employee/employee-view/',
                'fill_form': '/employee/employee-create/',
                'export': '/employee/employee-export/',
                'import': '/employee/employee-import/',
                'navigate': '/employee/employee-view/',
                'profile': '/employee/employee-profile/',
                'dashboard': '/employee/dashboard/',
            },
            'leave': {
                'search': '/leave/user-request-view/',
                'fill_form': '/leave/request-creation/',
                'export': '/leave/leave-export/',
                'import': '/leave/leave-import/',
                'navigate': '/leave/leave-employee-dashboard/',
                'dashboard': '/leave/leave-dashboard/',
                'types': '/leave/type-view/',
                'calendar': '/leave/leave-calendar/',
                'allocation': '/leave/allocation-request-view/',
            },
            'attendance': {
                'search': '/attendance/attendance-view/',
                'fill_form': '/attendance/attendance-create/',
                'export': '/attendance/attendance-info-export/',
                'import': '/attendance/attendance-info-import/',
                'navigate': '/attendance/attendance-dashboard/',
                'dashboard': '/attendance/attendance-dashboard/',
                'my_attendance': '/attendance/view-my-attendance/',
                'clock_in': '/attendance/clock-in/',
                'clock_out': '/attendance/clock-out/',
                'overtime': '/attendance/attendance-overtime-view/',
                'requests': '/attendance/request-attendance-view/',
            },
            'payroll': {
                'search': '/payroll/view-payslip/',
                'fill_form': '/payroll/contract-create/',
                'export': '/payroll/payslip-export/',
                'navigate': '/payroll/view-payroll-dashboard/',
                'dashboard': '/payroll/view-payroll-dashboard/',
                'run_action': '/payroll/payslip-create/',
                'contracts': '/payroll/view-contract/',
                'allowances': '/payroll/view-allowance/',
                'deductions': '/payroll/view-deduction/',
            },
            'recruitment': {
                'search': '/recruitment/candidate-view/',
                'fill_form': '/recruitment/candidate-create/',
                'export': '/recruitment/candidate-export/',
                'navigate': '/recruitment/recruitment-view/',
                'dashboard': '/recruitment/pipeline/',
                'jobs': '/recruitment/job-view/',
                'interviews': '/recruitment/interview-view/',
            },
            'onboarding': {
                'search': '/onboarding/onboarding-view/',
                'fill_form': '/onboarding/onboarding-create/',
                'navigate': '/onboarding/view-onboarding-dashboard/',
                'dashboard': '/onboarding/view-onboarding-dashboard/',
                'candidates': '/onboarding/candidates-view/',
            },
            'asset': {
                'search': '/asset/asset-view/',
                'fill_form': '/asset/asset-create/',
                'export': '/asset/asset-export/',
                'navigate': '/asset/asset-dashboard/',
                'dashboard': '/asset/asset-dashboard/',
                'requests': '/asset/asset-request-view/',
                'categories': '/asset/asset-category-view/',
            },
            'pms': {
                'search': '/pms/employee-objective-view/',
                'fill_form': '/pms/objective-create/',
                'navigate': '/pms/dashboard-view/',
                'dashboard': '/pms/dashboard-view/',
                'feedback': '/pms/feedback-view/',
                'goals': '/pms/goal-view/',
            },
            'project': {
                'search': '/project/project-view/',
                'fill_form': '/project/project-create/',
                'navigate': '/project/project-dashboard-view/',
                'dashboard': '/project/project-dashboard-view/',
                'tasks': '/project/task-view/',
                'timesheet': '/project/timesheet-view/',
            },
            'base': {
                'navigate': '/',
                'dashboard': '/',
                'search': '/',
                'settings': '/settings/',
                'companies': '/settings/company-view/',
                'departments': '/settings/department-view/',
                'job_positions': '/settings/job-position-view/',
                'shifts': '/settings/shift-view/',
                'work_types': '/settings/work-type-view/',
            }
        }
    
    def _build_dashboard_mappings(self) -> Dict[str, str]:
        """Build dashboard URL mappings."""
        
        return {
            'employee': '/employee/dashboard/',
            'leave': '/leave/leave-employee-dashboard/',
            'attendance': '/attendance/attendance-dashboard/',
            'payroll': '/payroll/view-payroll-dashboard/',
            'recruitment': '/recruitment/pipeline/',
            'onboarding': '/onboarding/view-onboarding-dashboard/',
            'asset': '/asset/asset-dashboard/',
            'pms': '/pms/dashboard-view/',
            'project': '/project/project-dashboard-view/',
            'main': '/',
            'home': '/',
            'dashboard': '/',
        }
    
    def _build_form_mappings(self) -> Dict[str, Dict[str, str]]:
        """Build form URL mappings for creation/editing."""
        
        return {
            'employee': {
                'create': '/employee/employee-create/',
                'edit': '/employee/employee-update/',
                'profile': '/employee/edit-profile/',
            },
            'leave': {
                'apply': '/leave/request-creation/',
                'create_type': '/leave/type-creation/',
                'allocation': '/leave/allocation-request-creation/',
            },
            'attendance': {
                'create': '/attendance/attendance-create/',
                'request': '/attendance/attendance-request-create/',
                'overtime': '/attendance/attendance-overtime-create/',
            },
            'payroll': {
                'contract': '/payroll/contract-create/',
                'allowance': '/payroll/allowance-create/',
                'deduction': '/payroll/deduction-create/',
            },
            'recruitment': {
                'candidate': '/recruitment/candidate-create/',
                'job': '/recruitment/job-create/',
                'stage': '/recruitment/stage-create/',
            }
        }
    
    def _build_export_mappings(self) -> Dict[str, str]:
        """Build export URL mappings."""
        
        return {
            'employee': '/employee/employee-export/',
            'leave': '/leave/leave-export/',
            'attendance': '/attendance/attendance-info-export/',
            'payroll': '/payroll/payslip-export/',
            'recruitment': '/recruitment/candidate-export/',
            'asset': '/asset/asset-export/',
        }
    
    def get_form_url(self, module: str, form_type: str = 'create') -> Optional[str]:
        """Get form URL for specific module and form type."""
        
        module_forms = self.form_urls.get(module, {})
        return module_forms.get(form_type)
    
    def get_export_url(self, module: str) -> Optional[str]:
        """Get export URL for specific module."""
        
        return self.export_urls.get(module)
    
    def get_dashboard_url(self, module: str) -> Optional[str]:
        """Get dashboard URL for specific module."""
        
        return self.dashboard_urls.get(module)
    
    def get_search_url_with_filters(self, module: str, filters: Dict[str, Any]) -> str:
        """Build search URL with query parameters for filters."""
        
        base_url = self.get_url('search', module)
        if not base_url:
            return None
            
        # Build query parameters from filters
        query_params = []
        
        if filters.get('employee_name'):
            query_params.append(f"employee_first_name__icontains={filters['employee_name']}")
        
        if filters.get('department'):
            query_params.append(f"employee_work_info__department__department__icontains={filters['department']}")
        
        if filters.get('date_range'):
            # Parse date range and add appropriate filters
            query_params.append(f"date_range={filters['date_range']}")
        
        if query_params:
            return f"{base_url}?{'&'.join(query_params)}"
        
        return base_url
    
    def resolve_ambiguous_request(self, text: str, module: str) -> Dict[str, Any]:
        """Resolve ambiguous requests by providing multiple options."""
        
        options = []
        module_urls = self.url_mappings.get(module, {})
        
        for intent, url in module_urls.items():
            if intent in ['search', 'fill_form', 'dashboard']:
                options.append({
                    'intent': intent,
                    'url': url,
                    'description': self._get_action_description(intent, module)
                })
        
        return {
            'type': 'disambiguation',
            'options': options,
            'message': f"I found multiple options for {module}. Which would you like?"
        }
    
    def _get_action_description(self, intent: str, module: str) -> str:
        """Get human-readable description for an action."""
        
        descriptions = {
            'search': f"View all {module} records",
            'fill_form': f"Create new {module} entry",
            'dashboard': f"Go to {module} dashboard",
            'export': f"Export {module} data",
            'navigate': f"Navigate to {module} page",
        }
        
        return descriptions.get(intent, f"{intent.title()} {module}")
    
    def get_quick_actions(self, module: str) -> List[Dict[str, str]]:
        """Get quick action suggestions for a module."""
        
        module_urls = self.url_mappings.get(module, {})
        actions = []
        
        for intent, url in module_urls.items():
            if intent in ['search', 'fill_form', 'dashboard', 'export']:
                actions.append({
                    'intent': intent,
                    'url': url,
                    'label': self._get_action_description(intent, module),
                    'icon': self._get_action_icon(intent)
                })
        
        return actions
    
    def _get_action_icon(self, intent: str) -> str:
        """Get icon for action type."""
        
        icons = {
            'search': '🔍',
            'fill_form': '📝',
            'dashboard': '📊',
            'export': '📤',
            'navigate': '🧭',
            'run_action': '⚡',
        }
        
        return icons.get(intent, '📋')
