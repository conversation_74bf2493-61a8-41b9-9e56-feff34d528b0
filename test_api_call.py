#!/usr/bin/env python
"""
Test API call to AI Assistant
"""

import requests
import json

def test_ai_api():
    """Test the AI assistant API endpoint."""
    
    url = "http://127.0.0.1:8000/ai/command/"
    
    payload = {
        "message": "Show all employees",
        "context": {
            "current_page": "/",
            "current_module": ""
        }
    }
    
    headers = {
        "Content-Type": "application/json"
    }
    
    print("🧪 Testing AI Assistant API...")
    print(f"URL: {url}")
    print(f"Payload: {json.dumps(payload, indent=2)}")
    print("-" * 50)
    
    try:
        response = requests.post(url, json=payload, headers=headers, timeout=10)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        print(f"Response Content: {response.text}")
        
        if response.status_code == 200:
            try:
                response_data = response.json()
                print("\n✅ SUCCESS! Parsed JSON Response:")
                print(json.dumps(response_data, indent=2))
            except json.JSONDecodeError:
                print("\n⚠️ Response is not valid JSON")
        else:
            print(f"\n❌ Error: HTTP {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Could not connect to the server")
        print("Make sure Django server is running at http://127.0.0.1:8000/")
    except Exception as e:
        print(f"❌ Unexpected Error: {e}")

if __name__ == "__main__":
    test_ai_api()
