{% load i18n %}
<div class="oh-modal__dialog-header pb-0">
  <h2 class="oh-modal__dialog-title" id="">
    {% trans "Rotating Shift Assign Update" %}
  </h2>
  <button
    class="oh-modal__close--custom"
    onclick="$(this).parents().closest('.oh-modal--show').toggleClass('oh-modal--show')"
    aria-label="Close"
  >
    <ion-icon name="close-outline"></ion-icon>
  </button>
</div>

<div class="oh-modal__dialog-body">
  <form
    hx-post="{% url 'rotating-shift-assign-update' update_form.instance.id %}"
    hx-target="#objectUpdateModalTarget"
    id="rotating-shift-assign-update-form"
    class="oh-profile-section pt-3"
  >
    {% csrf_token %} {{update_form.as_p}}
    <div class="oh-modal__dialog-footer p-0 mt-3">
      <button type="submit" class="oh-btn oh-btn--secondary oh-btn--shadow">
        {% trans "Save" %}
      </button>
    </div>
  </form>
</div>

<script>
  $(document).ready(function () {
    function hidBasedOn(basedOn, parent) {
      if (basedOn == "after") {
        parent.find("label[for='id_rotate_after_day']").show();
        parent.find("#id_rotate_after_day").show();

        parent.find("label[for='id_rotate_every_weekend']").hide();
        parent.find("#id_rotate_every_weekend").hide();
        parent.find("label[for='id_rotate_every']").hide();
        parent.find("#id_rotate_every").hide();
      } else if (basedOn == "weekly") {
        parent.find("label[for='id_rotate_every_weekend']").show();
        parent.find("#id_rotate_every_weekend").show();

        parent.find("label[for='id_rotate_after_day']").hide();
        parent.find("#id_rotate_after_day").hide();
        parent.find("label[for='id_rotate_every']").hide();
        parent.find("#id_rotate_every").hide();
      } else if (basedOn == "monthly") {
        parent.find("label[for='id_rotate_every']").show();
        parent.find("#id_rotate_every").show();

        parent.find("label[for='id_rotate_after_day']").hide();
        parent.find("#id_rotate_after_day").hide();
        parent.find("label[for='id_rotate_every_weekend']").hide();
        parent.find("#id_rotate_every_weekend").hide();
      }
    }
    var basedOn = $("#rotating-shift-assign-update-form #id_based_on");
    var parent = $("#rotating-shift-assign-update-form");
    hidBasedOn(basedOn.val(), parent);

    basedOn.on("change", function (e) {
      hidBasedOn(basedOn.val(), parent);
    });
  });
</script>
