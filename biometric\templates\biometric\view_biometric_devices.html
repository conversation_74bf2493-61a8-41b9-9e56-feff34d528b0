{% extends 'index.html' %} {% block content %} {% load static %} {% load i18n %}
<div class="oh-wrapper">
    {% include 'biometric/nav_biometric_devices.html' %}
    <div class="oh-checkpoint-badge mb-2" id="selectedDevices" data-ids="[]" data-clicked="" style="display: none">
    </div>

    <div class="d-flex flex-row-reverse">
        <span class="m-3"
            onclick="$('[name=hired]').val('false'); $('[name=hired]').first().change(); $('.filterButton').click()"
            style="cursor: pointer">
            <span class="oh-dot oh-dot--small me-1" style="background-color: red"></span>
            {% trans "Not-Connected" %}
        </span>
        <span class="m-3"
            onclick="$('[name=is_scheduler]').val('true');$('[name=is_scheduler]').first().change(); $('.filterButton').click()"
            style="cursor: pointer">
            <span class="oh-dot oh-dot--small me-1" style="background-color: rgb(103, 171, 238)"></span>
            {% trans "Scheduled" %}
        </span>
        <span class="m-3"
            onclick="$('[name=is_live]').val('true');$('[name=is_live]').first().change(); $('.filterButton').click()"
            style="cursor: pointer">
            <span class="oh-dot oh-dot--small me-1" style="background-color: orange"></span>
            {% trans "Live Capture" %}
        </span>
    </div>
    <div id="biometricDeviceList">
        {% if devices %}
            {% include 'biometric/card_biometric_devices.html' %}
        {% else %}
            {% include 'biometric/empty_view_biometric.html' %}
        {% endif %}
    </div>
    <div class="oh-modal" id="BiometricDeviceModal" role="dialog" aria-labelledby="BiometricDeviceModal"
        aria-hidden="true">
        <div class="oh-modal__dialog" style="max-width: 550px" id="BiometricDeviceFormTarget"></div>
    </div>
</div>
<script>
    function machineTypeChange(selectElement) {
        var machineType = selectElement.val();
        var allElements = [
            "#zkPassword", "#machinIpInput", "#machinUserName", "#machinPassword",
            "#machinPortInput", "#apiUrlInput", "#apiKeyInput", "#apiSecretInput",
            "#apiRequestIDInput"
        ];

        // Hide all elements initially
        $(allElements.join(', ')).hide();

        // Show relevant elements based on the selected machine type
        switch (machineType) {
            case "anviz":
                $("input[name='api_url']").val("");
                $("#apiUrlInput, #apiKeyInput, #apiSecretInput, #apiRequestIDInput").show();
                break;
            case "zk":
                $("#zkPassword, #machinIpInput, #machinPortInput").show();
                break;
            case "cosec":
            case "dahua":
                $("#machinIpInput, #machinUserName, #machinPassword, #machinPortInput").show();
                break;
            case "etimeoffice":
                $("#apiUrlInput, #machinUserName, #machinPassword").show();
                if (!$("input[name='api_url']").val()) {
                    $("input[name='api_url']").val("https://api.etimeoffice.com/api/");
                }

                break;
            default:
                // No elements need to be shown for unknown machine types
                break;
        }
    }
</script>

{% endblock %}
