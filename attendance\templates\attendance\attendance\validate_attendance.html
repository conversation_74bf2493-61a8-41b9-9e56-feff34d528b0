{% load i18n %}
{% load basefilters %}
{% load attendancefilters %}
{% include 'filter_tags.html' %}
<style>
  .disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  .oh-modal_close--custom {
    border: none;
    background: none;
    font-size: 1.5rem;
    opacity: 0.7;
    position: absolute;
    top: 25px;
    right: 15px;
  }
</style>
<div class="oh-tabs__content oh-tabs__content--active" id ="tab_contents">
<div class="oh-sticky-table">
    <div class="oh-sticky-table__table oh-table--sortable">
        <div class="oh-sticky-table__thead">
            <div class="oh-sticky-table__tr">
                <div
                  class="oh-sticky-table__th"
                  hx-get="{% url 'attendance-search' %}?{{pd}}&sortby=employee_id__employee_first_name"
                  hx-target="#tab_contents"
                  >
                  {% trans "Employee" %}
                </div>
                <div
                  class="oh-sticky-table__th"
                  hx-get="{% url 'attendance-search' %}?{{pd}}&sortby=attendance_date"
                  hx-target="#tab_contents"
                  >
                  {% trans "Date" %}
                </div>
                <div class="oh-sticky-table__th">{% trans "Day" %}</div>
                <div class="oh-sticky-table__th">{% trans "Check-In" %}</div>
                <div
                  class="oh-sticky-table__th"
                  hx-get="{% url 'attendance-search' %}?{{pd}}&sortby=attendance_clock_in_date"
                  hx-target="#tab_contents"
                  >
                  {% trans "In Date" %}
                </div>
                <div class="oh-sticky-table__th">{% trans "Check-Out" %}</div>
                <div
                  class="oh-sticky-table__th"
                  hx-get="{% url 'attendance-search' %}?{{pd}}&sortby=attendance_clock_out_date"
                  hx-target="#tab_contents"
                  >
                  {% trans "Out Date" %}
                </div>
                <div class="oh-sticky-table__th">{% trans "Shift" %}</div>
                <div class="oh-sticky-table__th">{% trans "Work Type" %}</div>
                <div class="oh-sticky-table__th">{% trans "Min Hour" %}</div>
                <div
                  class="oh-sticky-table__th"
                  hx-get="{% url 'attendance-search' %}?{{pd}}&sortby=at_work_second"
                  hx-target="#tab_contents"
                  >
                  {% trans "At Work" %}
                </div>
                <div
                  class="oh-sticky-table__th"
                  hx-get="{% url 'attendance-search' %}?{{pd}}&sortby=at_work_second"
                  hx-target="#tab_contents"
                  >
                  {% trans "Pending Hour" %}
                </div>
                <div
                  class="oh-sticky-table__th"
                  hx-get="{% url 'attendance-search' %}?{{pd}}&sortby=overtime_second"
                  hx-target="#tab_contents"
                  >
                  {% trans "Overtime" %}
                </div>
                <div class="oh-sticky-table__th"></div>
            </div>
        </div>
        <div class="oh-sticky-table__tbody">
            {% for attendance in attendances %}
                <div
                  class="oh-sticky-table__tr"
                  draggable="false"
                  data-toggle="oh-modal-toggle"
                  data-target="#objectDetailsModalW25"
                  hx-target="#objectDetailsModalW25Target"
                  hx-get="{% url 'user-request-one-view' attendance.id %}?instances_ids={{attendances_ids}}"
                  >
                    <div class="oh-sticky-table__sd">
                        <div class="d-flex">
                            <div class="oh-profile oh-profile--md">
                                <div class="oh-profile__avatar mr-1">
                                    <img
                                      src="{{attendance.employee_id.get_avatar}}"
                                      class="oh-profile__image"
                                      alt="Mary Magdalene"
                                    />
                                </div>
                                <span class="oh-profile__name oh-text--dark">{{attendance.employee_id}}</span>
                            </div>
                        </div>
                    </div>
                    <div class="oh-sticky-table__td dateformat_changer">{{attendance.attendance_date}}</div>
                    <div class="oh-sticky-table__td">
                      {{attendance.attendance_day.get_day_display }}
                    </div>
                    <div class="oh-sticky-table__td timeformat_changer">
                      {{attendance.attendance_clock_in}}
                    </div>
                    <div class="oh-sticky-table__td dateformat_changer">
                      {{attendance.attendance_clock_in_date}}
                    </div>
                    <div class="oh-sticky-table__td timeformat_changer">
                      {{attendance.attendance_clock_out}}
                    </div>
                    <div class="oh-sticky-table__td dateformat_changer">
                      {{attendance.attendance_clock_out_date}}
                    </div>
                    <div class="oh-sticky-table__td">{{attendance.shift_id}}</div>
                    <div class="oh-sticky-table__td">{{attendance.work_type_id}}</div>
                    <div class="oh-sticky-table__td">{{attendance.minimum_hour}}</div>
                    <div class="oh-sticky-table__td">
                      {{ attendance.attendance_worked_hour }}
                    </div>
                    <div class="oh-sticky-table__td">{{ attendance.hours_pending }}</div>
                    <div class="oh-sticky-table__td">
                      {{attendance.attendance_overtime}}
                    </div>
                    <div class="oh-sticky-table__td">
                        <div class="oh-btn-group">
                            {% if perms.attendance.change_attendance or request.user|is_reportingmanager %}
                                <a
                                  hx-get="{% url 'attendance-update' attendance.id %}"
                                  onclick="event.stopPropagation()"
                                  hx-target="#updateAttendanceModalBody"
                                  hx-swap="innerHTML"
                                  data-toggle="oh-modal-toggle"
                                  data-target="#updateAttendanceModal"
                                  class="oh-btn oh-btn--light-bkg w-50"
                                  title="{% trans 'Edit' %}"
                                  >
                                  <ion-icon name="create-outline"></ion-icon>
                                </a>
                            {% endif %}
                            {% if perms.attendance.delete_attendance %}
                                <form action="{% url 'attendance-delete' attendance.id %}"
                                      onclick="event.stopPropagation()"
                                      onsubmit="return confirm('{% trans "Are you sure want to delete this attendance?" %}')"
                                      hx-target="#tab_contents" method='post' class='w-50'>
                                      {% csrf_token %}
                                    <button
                                      type="submit"
                                      class="oh-btn oh-btn--danger-outline oh-btn--light-bkg w-100"
                                      title="{% trans 'Remove' %}"
                                          >
                                      <ion-icon name="trash-outline"></ion-icon>
                                    </button>
                                </form>
                            {% endif %}
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>
</div>
<div class="oh-pagination">
    <span class="oh-pagination__page">
      {% trans "Page" %} {{ attendances.number }} {% trans "of" %} {{ attendances.paginator.num_pages }}.
    </span>
    <nav class="oh-pagination__nav">
        <div class="oh-pagination__input-container me-3">
            <span class="oh-pagination__label me-1">{% trans "Page" %}</span>
            <input
              type="number"
              name="page"
              class="oh-pagination__input"
              value="{{attendances.number}}"
              hx-get="{% url 'attendance-search' %}?{{pd}}"
              hx-target="#tab_contents"
              min="1"
            />
            <span class="oh-pagination__label">
                {% trans "of" %} {{attendances.paginator.num_pages}}
            </span>
        </div>
        <ul class="oh-pagination__items">
            {% if attendances.has_previous %}
                <li class="oh-pagination__item oh-pagination__item--wide">
                    <a
                      hx-target="#tab_contents"
                      hx-get="{% url 'attendance-search' %}?{{pd}}&page=1"
                      class="oh-pagination__link"
                      >{% trans "First" %}
                    </a>
                </li>
                <li class="oh-pagination__item oh-pagination__item--wide">
                    <a
                      hx-target="#tab_contents"
                      hx-get="{% url 'attendance-search' %}?{{pd}}&page={{ attendances.previous_page_number }}"
                      class="oh-pagination__link"
                      >{% trans "Previous" %}
                    </a>
                </li>
            {% endif %}
            {% if attendances.has_next %}
                <li class="oh-pagination__item oh-pagination__item--wide">
                    <a
                      hx-target="#tab_contents"
                      hx-get="{% url 'attendance-search' %}?{{pd}}&page={{ attendances.next_page_number }}"
                      class="oh-pagination__link"
                      >{% trans "Next" %}
                    </a>
                </li>
                <li class="oh-pagination__item oh-pagination__item--wide">
                    <a
                      hx-target="#tab_contents"
                      hx-get="{% url 'attendance-search' %}?{{pd}}&page={{ attendances.paginator.num_pages }}"
                      class="oh-pagination__link"
                      >{% trans "Last" %}
                    </a>
                </li>
            {% endif %}
        </ul>
    </nav>
</div>
</div>

{% if perms.attendance.change_attendance or request.user|is_reportingmanager %}
<div
  class="oh-modal"
  id="updateAttendanceModal"
  role="dialog"
  aria-labelledby="updateAttendanceModal"
  aria-hidden="true"
  >
    <div class="oh-modal__dialog">
        <div class="oh-modal__dialog-header">
            <h2 class="oh-modal__dialog-title" id="updateAttendanceModalLabel">
              {% trans "Edit Attendance" %}
            </h2>
            <button
              type="button"
              class="oh-modal_close--custom"
              onclick="$('#updateAttendanceModal').removeClass('oh-modal--show');"
                >
                <ion-icon
                  name="close-outline"
                  role="img"
                  aria-label="close outline"
                ></ion-icon>
            </button>
        </div>
        <div class="oh-modal__dialog-body" id="updateAttendanceModalBody"></div>
    </div>
</div>
{% endif %}
