"""
CLI Interface for Natural Language API Converter

This module provides a comprehensive command-line interface for testing
and interacting with the Natural Language API Converter system.
"""

import json
import sys
import argparse
from typing import Dict, Any, List
from datetime import datetime
from .natural_language_api_converter import NaturalLanguageAPIConverter
from .api_request_executor import APIRequestExecutor
from .test_nl_api_converter import run_comprehensive_tests


class CLIInterface:
    """
    Comprehensive CLI interface for the Natural Language API Converter.
    Provides 200% functionality for testing and interaction.
    """
    
    def __init__(self):
        self.converter = NaturalLanguageAPIConverter()
        self.executor = APIRequestExecutor()
        self.session_history = []
    
    def run_interactive_mode(self):
        """Run the interactive CLI mode."""
        
        print("🤖 Natural Language API Converter - Interactive Mode")
        print("=" * 60)
        print("Type your natural language requests, or 'help' for commands")
        print("Type 'quit' or 'exit' to exit")
        print()
        
        while True:
            try:
                # Get user input
                user_input = input("🗣️  You: ").strip()
                
                if not user_input:
                    continue
                
                # Handle special commands
                if user_input.lower() in ['quit', 'exit', 'q']:
                    print("👋 Goodbye!")
                    break
                elif user_input.lower() == 'help':
                    self._show_help()
                    continue
                elif user_input.lower() == 'history':
                    self._show_history()
                    continue
                elif user_input.lower() == 'stats':
                    self._show_stats()
                    continue
                elif user_input.lower() == 'clear':
                    self._clear_history()
                    continue
                elif user_input.lower().startswith('test'):
                    self._run_tests()
                    continue
                
                # Process natural language request
                self._process_request(user_input)
                
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {str(e)}")
    
    def process_single_request(self, natural_language: str, execute: bool = False) -> Dict[str, Any]:
        """
        Process a single natural language request.
        
        Args:
            natural_language: The natural language input
            execute: Whether to execute the API request
            
        Returns:
            Result dictionary with conversion and execution results
        """
        
        print(f"\n🔄 Processing: '{natural_language}'")
        print("-" * 50)
        
        # Convert to API request
        api_request = self.converter.convert_to_api_request(natural_language)
        
        print("📋 Generated API Request:")
        print(json.dumps(api_request, indent=2))
        
        result = {
            'input': natural_language,
            'api_request': api_request,
            'timestamp': datetime.now().isoformat()
        }
        
        # Execute if requested
        if execute and api_request.get('confidence', 0) >= 0.7:
            print("\n⚡ Executing API Request...")
            execution_result = self.executor.execute_request(api_request)
            result['execution_result'] = execution_result
            
            print("📊 Execution Result:")
            if execution_result.get('success'):
                print(f"✅ Success: {execution_result.get('summary', 'Request completed')}")
                if execution_result.get('data'):
                    self._display_data(execution_result['data'])
            else:
                print(f"❌ Failed: {execution_result.get('error', {}).get('message', 'Unknown error')}")
        
        elif execute:
            print(f"⚠️  Skipping execution due to low confidence: {api_request.get('confidence', 0):.2f}")
        
        # Store in session history
        self.session_history.append(result)
        
        return result
    
    def run_batch_processing(self, requests: List[str], execute: bool = False) -> List[Dict[str, Any]]:
        """
        Process multiple natural language requests in batch.
        
        Args:
            requests: List of natural language requests
            execute: Whether to execute the API requests
            
        Returns:
            List of result dictionaries
        """
        
        print(f"\n🔄 Processing {len(requests)} requests in batch mode...")
        print("=" * 60)
        
        results = []
        
        for i, request in enumerate(requests, 1):
            print(f"\n[{i}/{len(requests)}] Processing: '{request}'")
            result = self.process_single_request(request, execute)
            results.append(result)
        
        # Generate batch summary
        self._generate_batch_summary(results)
        
        return results
    
    def _process_request(self, user_input: str):
        """Process a user request in interactive mode."""
        
        # Ask if user wants to execute the request
        print(f"\n🔄 Converting: '{user_input}'")
        
        # Convert to API request
        api_request = self.converter.convert_to_api_request(user_input)
        
        # Display the generated request
        print("\n📋 Generated API Request:")
        self._display_api_request(api_request)
        
        # Ask if user wants to execute
        if api_request.get('confidence', 0) >= 0.7:
            execute = input("\n🚀 Execute this request? (y/n): ").lower().startswith('y')
            
            if execute:
                print("\n⚡ Executing...")
                execution_result = self.executor.execute_request(api_request)
                self._display_execution_result(execution_result)
        else:
            print(f"\n⚠️  Low confidence ({api_request.get('confidence', 0):.2f}). Skipping execution.")
            print("💡 Try being more specific in your request.")
        
        # Store in history
        self.session_history.append({
            'input': user_input,
            'api_request': api_request,
            'timestamp': datetime.now().isoformat()
        })
    
    def _display_api_request(self, api_request: Dict[str, Any]):
        """Display the API request in a formatted way."""
        
        print(f"  🎯 URL: {api_request.get('url', 'N/A')}")
        print(f"  📡 Method: {api_request.get('method', 'N/A')}")
        print(f"  🎯 Confidence: {api_request.get('confidence', 0):.2f}")
        
        if api_request.get('filters'):
            print(f"  🔍 Filters: {json.dumps(api_request['filters'], indent=4)}")
        
        if api_request.get('search'):
            print(f"  🔎 Search: {api_request['search']}")
        
        if api_request.get('sort_by'):
            print(f"  📊 Sort: {api_request['sort_by']} ({api_request.get('order', 'asc')})")
        
        if api_request.get('body'):
            print(f"  📝 Body: {json.dumps(api_request['body'], indent=4)}")
    
    def _display_execution_result(self, result: Dict[str, Any]):
        """Display the execution result."""
        
        if result.get('success'):
            print(f"\n✅ Success!")
            print(f"📊 Summary: {result.get('summary', 'Request completed')}")
            
            execution_time = result.get('execution_metadata', {}).get('execution_time', 0)
            print(f"⏱️  Execution time: {execution_time:.3f}s")
            
            if result.get('data'):
                self._display_data(result['data'])
        else:
            print(f"\n❌ Failed!")
            error = result.get('error', {})
            print(f"💥 Error: {error.get('message', 'Unknown error')}")
    
    def _display_data(self, data: Any):
        """Display response data in a formatted way."""
        
        if isinstance(data, dict):
            if 'results' in data:
                results = data['results']
                count = len(results)
                total = data.get('count', count)
                
                print(f"\n📋 Results ({count} of {total}):")
                for i, item in enumerate(results[:5], 1):  # Show first 5
                    print(f"  {i}. {self._format_item(item)}")
                
                if count > 5:
                    print(f"  ... and {count - 5} more")
            
            elif 'count' in data:
                print(f"\n📊 Count: {data['count']}")
            
            else:
                print(f"\n📄 Data: {json.dumps(data, indent=2)[:200]}...")
        
        elif isinstance(data, list):
            print(f"\n📋 List ({len(data)} items):")
            for i, item in enumerate(data[:5], 1):
                print(f"  {i}. {self._format_item(item)}")
            
            if len(data) > 5:
                print(f"  ... and {len(data) - 5} more")
        
        else:
            print(f"\n📄 Data: {str(data)[:200]}...")
    
    def _format_item(self, item: Any) -> str:
        """Format a single item for display."""
        
        if isinstance(item, dict):
            # Try to find a meaningful representation
            if 'name' in item:
                return f"{item['name']} (ID: {item.get('id', 'N/A')})"
            elif 'title' in item:
                return f"{item['title']} (ID: {item.get('id', 'N/A')})"
            elif 'email' in item:
                return f"{item['email']} (ID: {item.get('id', 'N/A')})"
            else:
                return f"ID: {item.get('id', 'N/A')}"
        else:
            return str(item)
    
    def _show_help(self):
        """Show help information."""
        
        print("\n📚 Available Commands:")
        print("  help     - Show this help message")
        print("  history  - Show session history")
        print("  stats    - Show performance statistics")
        print("  clear    - Clear session history")
        print("  test     - Run comprehensive tests")
        print("  quit/exit - Exit the program")
        print("\n💡 Natural Language Examples:")
        print("  'Show all employees'")
        print("  'List employees from HR department'")
        print("  'Create employee John Doe <NAME_EMAIL>'")
        print("  'Find employees sorted by salary descending'")
        print("  'Export attendance data'")
    
    def _show_history(self):
        """Show session history."""
        
        if not self.session_history:
            print("\n📝 No requests in session history")
            return
        
        print(f"\n📝 Session History ({len(self.session_history)} requests):")
        for i, entry in enumerate(self.session_history[-10:], 1):  # Show last 10
            timestamp = entry['timestamp'][:19]  # Remove microseconds
            confidence = entry['api_request'].get('confidence', 0)
            print(f"  {i}. [{timestamp}] '{entry['input']}' (confidence: {confidence:.2f})")
    
    def _show_stats(self):
        """Show performance statistics."""
        
        stats = self.executor.get_performance_stats()
        
        print("\n📊 Performance Statistics:")
        for key, value in stats.items():
            if isinstance(value, float):
                print(f"  {key}: {value:.3f}")
            else:
                print(f"  {key}: {value}")
    
    def _clear_history(self):
        """Clear session history."""
        
        self.session_history.clear()
        self.executor.clear_request_history()
        print("\n🗑️  Session history cleared")
    
    def _run_tests(self):
        """Run comprehensive tests."""
        
        print("\n🧪 Running comprehensive tests...")
        success = run_comprehensive_tests()
        
        if success:
            print("✅ All tests passed!")
        else:
            print("❌ Some tests failed. Check the test report for details.")
    
    def _generate_batch_summary(self, results: List[Dict[str, Any]]):
        """Generate a summary for batch processing."""
        
        total = len(results)
        successful_conversions = sum(1 for r in results if r['api_request'].get('confidence', 0) >= 0.7)
        
        print(f"\n📊 Batch Processing Summary:")
        print(f"  Total requests: {total}")
        print(f"  Successful conversions: {successful_conversions}")
        print(f"  Success rate: {(successful_conversions / total) * 100:.1f}%")


def main():
    """Main CLI entry point."""
    
    parser = argparse.ArgumentParser(description='Natural Language API Converter CLI')
    parser.add_argument('--interactive', '-i', action='store_true', help='Run in interactive mode')
    parser.add_argument('--request', '-r', type=str, help='Process a single request')
    parser.add_argument('--execute', '-e', action='store_true', help='Execute the API request')
    parser.add_argument('--batch', '-b', type=str, help='Process requests from file (one per line)')
    parser.add_argument('--test', '-t', action='store_true', help='Run comprehensive tests')
    
    args = parser.parse_args()
    
    cli = CLIInterface()
    
    if args.test:
        run_comprehensive_tests()
    elif args.interactive:
        cli.run_interactive_mode()
    elif args.request:
        cli.process_single_request(args.request, args.execute)
    elif args.batch:
        try:
            with open(args.batch, 'r') as f:
                requests = [line.strip() for line in f if line.strip()]
            cli.run_batch_processing(requests, args.execute)
        except FileNotFoundError:
            print(f"❌ File not found: {args.batch}")
    else:
        # Default to interactive mode
        cli.run_interactive_mode()


if __name__ == '__main__':
    main()
