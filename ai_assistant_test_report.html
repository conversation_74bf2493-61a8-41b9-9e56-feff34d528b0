
    <!DOCTYPE html>
    <html>
    <head>
        <title>AI Assistant Test Report</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { background: #2c3e50; color: white; padding: 20px; border-radius: 5px; }
            .summary { background: #ecf0f1; padding: 15px; margin: 20px 0; border-radius: 5px; }
            .passed { color: #27ae60; font-weight: bold; }
            .failed { color: #e74c3c; font-weight: bold; }
            .test-result { margin: 10px 0; padding: 10px; border-left: 4px solid #bdc3c7; }
            .test-passed { border-left-color: #27ae60; background: #d5f4e6; }
            .test-failed { border-left-color: #e74c3c; background: #fadbd8; }
            .confusion-matrix { margin: 20px 0; }
            .matrix-table { border-collapse: collapse; width: 100%; }
            .matrix-table th, .matrix-table td { border: 1px solid #ddd; padding: 8px; text-align: center; }
            .matrix-table th { background: #f2f2f2; }
            .recommendations { background: #fff3cd; padding: 15px; border-radius: 5px; }
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🤖 AI Assistant Test Report</h1>
            <p>Generated on 2025-07-11 08:21:18</p>
        </div>
        
        <div class="summary">
            <h2>📊 Test Summary</h2>
            <p><strong>Total Tests:</strong> 40</p>
            <p><strong>Passed:</strong> <span class="passed">39</span></p>
            <p><strong>Failed:</strong> <span class="failed">1</span></p>
            <p><strong>Accuracy:</strong> <span class="passed">97.5%</span></p>
        </div>
        
        <div class="confusion-matrix">
            <h2>🎯 Confusion Matrix</h2>
            <table class="matrix-table">
                <tr>
                    <th>Expected \ Actual</th>
    <th>export</th><th>fill_form</th><th>navigate</th><th>run_action</th><th>search</th><th>sort</th></tr><tr><th>export</th><td>6</td><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td></tr><tr><th>fill_form</th><td>0</td><td>7</td><td>0</td><td>0</td><td>0</td><td>0</td></tr><tr><th>navigate</th><td>0</td><td>0</td><td>5</td><td>0</td><td>0</td><td>0</td></tr><tr><th>run_action</th><td>0</td><td>0</td><td>0</td><td>5</td><td>0</td><td>0</td></tr><tr><th>search</th><td>0</td><td>0</td><td>0</td><td>0</td><td>16</td><td>0</td></tr><tr><th>sort</th><td>0</td><td>0</td><td>0</td><td>0</td><td>0</td><td>1</td></tr>
            </table>
        </div>
        
        <div class="recommendations">
            <h2>💡 Recommendations</h2>
            <ul>
    
            </ul>
        </div>
        
        <div>
            <h2>❌ Failed Test Cases</h2>
    
        <div class="test-result test-failed">
            <strong>Input:</strong> "Show employees sorted by highest salary"<br>
            <strong>Category:</strong> employee_sort<br>
            <strong>Expected:</strong> {
  "intent": "sort",
  "module": "employee",
  "action": "ajax_update",
  "url": "/employee/employee-view/"
}<br>
            <strong>Actual:</strong> {
  "intent": "sort",
  "module": "employee",
  "action": "redirect",
  "url": "/employee/dashboard/",
  "redirect_url": "/employee/dashboard/",
  "filters": {},
  "sort_by": null,
  "order": "asc",
  "prefill_data": {},
  "message": "Opening employee page.",
  "confidence": 1.0,
  "entities": {
    "sort_criteria": "by highest salary"
  }
}<br>
            <strong>Failure Reason:</strong> URL mismatch: expected '/employee/employee-view/', got '/employee/dashboard/'; Action mismatch: expected 'ajax_update', got 'redirect'
        </div>
        
        </div>
    </body>
    </html>
    