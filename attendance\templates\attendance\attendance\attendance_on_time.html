{% load i18n %}
{% load attendancefilters %}
{% load basefilters %}
{% include 'filter_tags.html' %}
<style>
  .disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
</style>
 <div class="oh-sticky-table">
      <div class="oh-sticky-table__table oh-table--sortable">
        <div class="oh-sticky-table__thead">
          <div class="oh-sticky-table__tr">
            <div class="oh-sticky-table__th" >
              <div class="d-flex">
                <div class="">
                  <input type="checkbox" title='{% trans "Select All" %}' class="oh-input oh-input__checkbox mt-1 mr-2 all-attendances" />
                </div>
                <div hx-get="{% url 'attendance-search' %}?{{pd}}&sortby=employee_id__employee_first_name" hx-target="#tab_contents">
                  {% trans "Employee" %}
                </div>
              </div>
            </div>
            <div class="oh-sticky-table__th" hx-get="{% url 'attendance-search' %}?{{pd}}&sortby=attendance_date" hx-target="#tab_contents">{% trans "Date" %}</div>
            <div class="oh-sticky-table__th">{% trans "Day" %}</div>
            <div class="oh-sticky-table__th" >{% trans "Check-In" %}</div>
            <div class="oh-sticky-table__th" hx-get="{% url 'attendance-search' %}?{{pd}}&sortby=attendance_clock_in_date" hx-target="#tab_contents">{% trans "In Date" %}</div>
            <div class="oh-sticky-table__th">{% trans "Check-Out" %}</div>
            <div class="oh-sticky-table__th" hx-get="{% url 'attendance-search' %}?{{pd}}&sortby=attendance_clock_out_date" hx-target="#tab_contents">{% trans "Out Date" %}</div>
            <div class="oh-sticky-table__th">{% trans "Shift" %}</div>
            <div class="oh-sticky-table__th">{% trans "Work Type" %}</div>
            <div class="oh-sticky-table__th" >{% trans "Min Hour" %}</div>
            <div class="oh-sticky-table__th" hx-get="{% url 'attendance-search' %}?{{pd}}&sortby=at_work_second" hx-target="#tab_contents">{% trans "At Work" %}</div>
            <div class="oh-sticky-table__th" hx-get="{% url 'attendance-search' %}?{{pd}}&sortby=overtime_second" hx-target="#tab_contents">{% trans "Overtime" %}</div>
            <div class="oh-sticky-table__th"></div>
          </div>
        </div>
        <div class="oh-sticky-table__tbody">
          {% for attendance in attendances %}
          <div class="oh-sticky-table__tr"
          draggable="false">
            <div class="oh-sticky-table__sd">
              <div class="d-flex">

                <div class="">
                  <input type="checkbox" id="{{attendance.id}}" class="oh-input attendance-checkbox oh-input__checkbox mt-2 mr-2 all-attendance-row" />
                </div>
                <div class="oh-profile oh-profile--md">
                  <div class="oh-profile__avatar mr-1">
                    <img
                    src="{{attendance.employee_id.get_avatar}}"
                    class="oh-profile__image"
                    alt="Mary Magdalene"
                    />
                  </div>
                  <span class="oh-profile__name oh-text--dark"
                  >{{attendance.employee_id}}</span
                  >
                </div>
              </div>
            </div>
            <div class="oh-sticky-table__td dateformat_changer">
              {{attendance.attendance_date}}
            </div>
            <div class="oh-sticky-table__td">
              {{attendance.attendance_day.get_day_display }}
            </div>
            <div class="oh-sticky-table__td timeformat_changer">
              {{attendance.attendance_clock_in}}
            </div>
            <div class="oh-sticky-table__td dateformat_changer">
              {{attendance.attendance_clock_in_date}}
            </div>
            <div class="oh-sticky-table__td timeformat_changer">
              {{attendance.attendance_clock_out}}
            </div>
            <div class="oh-sticky-table__td dateformat_changer">
              {{attendance.attendance_clock_out_date}}
            </div>
            <div class="oh-sticky-table__td">{{attendance.shift_id}}</div>
            <div class="oh-sticky-table__td">
              {{attendance.work_type_id}}
            </div>
            <div class="oh-sticky-table__td">
              {{attendance.minimum_hour}}
            </div>
            <div class="oh-sticky-table__td">
              {{attendance.attendance_worked_hour}}
            </div>
            <div class="oh-sticky-table__td">
              {{attendance.attendance_overtime}}
            </div>

            <div class="oh-sticky-table__td">
                <div class="oh-btn-group">
                {% if perms.attendance.change_attendance or request.user|is_reportingmanager %}
                <a hx-get="{% url 'attendance-update' attendance.id %}" hx-target='#updateAttendanceModalBody' hx-swap='innerHTML' data-toggle='oh-modal-toggle' data-target='#updateAttendanceModal'  class="oh-btn oh-btn--light-bkg w-50" title="{% trans 'Edit' %}"><ion-icon name="create-outline"></ion-icon></a>
                {% endif %}
                {% if perms.attendance.delete_attendance %}
                <form action="{% url 'attendance-delete' attendance.id  %}" onsubmit="return confirm('{% trans "Are you sure want to delete this attendance?" %}')" hx-target="#tab_contents" method='post' class='w-50'>
                  {% csrf_token %}
                  <button type='submit' class="oh-btn oh-btn--danger-outline oh-btn--light-bkg w-100" title="{% trans 'Remove' %}"><ion-icon name="trash-outline"></ion-icon></button>
                </form>
                {% endif %}
              </div>
          </div>
          </div>
          {% endfor %}

        </div>
      </div>
    </div>

<script>
    $(document).ready(function () {
      var activeTab = localStorage.getItem('activeTabAttendance')
      if (activeTab != null) {
        var tab  = $(`[data-target="${activeTab}"]`)
        var tabContent = $(activeTab)
        $(tab).attr('class', 'oh-tabs__tab oh-tabs__tab--active');
        $(tabContent).attr('class', 'oh-tabs__content oh-tabs__content--active');
      }
      else{
        $('[data-target="#tab_1"]').attr('class', 'oh-tabs__tab oh-tabs__tab--active');
        $('#tab_1').attr('class', 'oh-tabs__content oh-tabs__content--active');
      }
      $('.oh-tabs__tab').click(function (e) {
        var activeTab = $(this).attr('data-target');
        localStorage.setItem('activeTabAttendance',activeTab)

      });
    });
  </script>
