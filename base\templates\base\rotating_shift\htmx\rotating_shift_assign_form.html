{% load i18n %}
<div class="oh-modal__dialog-header pb-0">
  <h2 class="oh-modal__dialog-title" id="rotatingShiftAssignModalLabel">
    {% trans "Rotating Shift Assign" %}
  </h2>
  <button class="oh-modal__close" aria-label="Close">
    <ion-icon name="close-outline"></ion-icon>
  </button>
</div>
<div class="oh-modal__dialog-body">
  <form
    hx-post="{% url 'rotating-shift-assign-add' %}"
    hx-target="#objectCreateModalTarget"
    class="oh-profile-section pt-3"
  >
    {% csrf_token %} {{form.as_p}}
    <div class="oh-modal__dialog-footer p-0 mt-3">
      <button type="submit" class="oh-btn oh-btn--secondary oh-btn--shadow">
        {% trans "Save" %}
      </button>
    </div>
  </form>
</div>
