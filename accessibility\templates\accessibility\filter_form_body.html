{% load widget_tweaks %} {% load i18n %}
{% load generic_template_filters %}
<div class="row" style="padding-right: 20px; padding-right: 10px;">
  <div class="col-12" style="padding-right: 20px !important;">{{ form.non_field_errors }}</div>
  {% for field in form.visible_fields %}
    <div class="col-12 col-md-{{ field|col }}" id="id_{{ field.name }}_parent_div" style="padding-right: 0;">
      <div class="oh-label__info" for="id_{{ field.name }}">
        <label class="oh-label {% if field.field.required %} required-star{% endif %}" for="id_{{ field.name }}"><b>{% trans field.label %}</b></label>
        {% if field.help_text != '' %}
          <span class="oh-info mr-2" title="{{ field.help_text|safe }}"></span>
        {% endif %}
      </div>

      {% if field.field.widget.input_type == 'checkbox' %}
        <div class="oh-switch" style="width: 30px">{{ field|add_class:'oh-switch__checkbox' }}</div>
      {% else %}
        <div id="dynamic_field_{{ field.name }}">{{ field|add_class:'form-control' }}
          {{ field.errors }}</div>
      {% endif %}
    </div>
  {% endfor %}
</div>
