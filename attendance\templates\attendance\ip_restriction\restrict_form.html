{% load i18n %} {% load basefilters %}
<div class="oh-modal__dialog-header pb-0">
    <h2 class="oh-modal__dialog-title" id="restrictIpLabel">
        {% if id != None %}
            {% trans "Edit Allowed IPs" %}
        {% else %}
            {% trans "Add Allowed IPs" %}
        {% endif %}
    </h2>
    <button class="oh-modal__close" aria-label="Close">
      <ion-icon name="close-outline" role="img" class="md hydrated" aria-label="close outline"></ion-icon>
    </button>
</div>
<div class="oh-modal__dialog-body">
    <form class="oh-profile-section"
    {% if id != None %}
    hx-post="{% url 'edit-allowed-ip' %}?id={{id}}"
    {% else %}
    hx-post="{% url 'create-allowed-ip' %}"
    {% endif %}
    hx-target="#objectDetailsModalW25Target">
        {% for field in form.visible_fields %}
            {{ form.non_field_errors }}
            <div
                class="oh-input__group"
                id="moreIPContainer_{{ forloop.counter0 }}"
            >
                <label
                    class="oh-label {% if field.field.required %}required-star{% endif %}"
                    for="id_{{ field.name }}"
                    {% if id == None %}
                    title="{{ field.help_text|safe }}"
                    {% endif %}
                >
                    {% trans field.label %}
                </label>
                {{ field }}
                {{field.errors}}
            </div>
        {% endfor %}
        <div class="oh-modal__dialog-footer p-0 mt-3">
            <button type="submit" class="oh-btn oh-btn--secondary oh-btn--shadow" >
                {% trans "Save" %}
            </button>
        </div>
    </form>
</div>
