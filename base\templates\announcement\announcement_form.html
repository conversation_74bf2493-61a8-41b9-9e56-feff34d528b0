{% load i18n %}
{% if messages %}
    <script>reloadMessage();</script>
    <span hx-get="{% url 'announcement-list' %}" hx-trigger="load" hx-target="#announcementListCard"
        hx-on-htmx-after-request="setTimeout(() => {$('.oh-modal__close').click();}, 500);"></span>
{% endif %}
{% if form.errors %}
    <div class="oh-wrapper">
        <div class="oh-alert-container">
            {% for error in form.non_field_errors %}
            <div class="oh-alert oh-alert--animated oh-alert--danger">
                {{ error }}
            </div>
            {% endfor %}
        </div>
    </div>
{% endif %}
<div class="oh-modal__dialog-header pb-0">
    <h2 class="oh-modal__dialog-title" id="createModalTitle">
        {% trans "Create " %} {{form.verbose_name}}
    </h2>
    <button class="oh-modal__close" aria-label="Close"
        onclick="setTimeout(function () { $('#objectCreateModalTarget').empty(); }, 200);">
        <ion-icon name="close-outline"></ion-icon>
    </button>
</div>
<div class="oh-modal__dialog-body">
    <form hx-post="{% url 'create-announcement' %}" hx-target="#objectCreateModalTarget"
        hx-encoding="multipart/form-data" class="oh-profile-section">
        {% csrf_token %}
        {{form.as_p}}
        <div class="d-flex flex-row-reverse">
            <button type="submit" class="oh-btn oh-btn--secondary mt-2 mr-0 oh-btn--w-100-resp">
                {% trans "Save" %}
            </button>
        </div>
    </form>
</div>
