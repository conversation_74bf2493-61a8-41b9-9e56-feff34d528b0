"""
AI Assistant Admin Configuration
"""

from django.contrib import admin
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _

from .models import ChatHistory, AIAssistantSettings, ConversationMemory


@admin.register(ChatHistory)
class ChatHistoryAdmin(admin.ModelAdmin):
    """Admin interface for ChatHistory model."""
    
    list_display = [
        'user', 'intent', 'confidence', 'processing_time', 
        'created_at', 'message_preview'
    ]
    list_filter = ['intent', 'created_at', 'confidence']
    search_fields = ['user__username', 'message', 'intent']
    readonly_fields = ['created_at', 'processing_time']
    ordering = ['-created_at']
    
    fieldsets = (
        (_('Basic Information'), {
            'fields': ('user', 'intent', 'confidence', 'processing_time')
        }),
        (_('Content'), {
            'fields': ('message', 'response')
        }),
        (_('Metadata'), {
            'fields': ('created_at',),
            'classes': ('collapse',)
        }),
    )
    
    def message_preview(self, obj):
        """Show a preview of the message."""
        if len(obj.message) > 50:
            return obj.message[:50] + "..."
        return obj.message
    message_preview.short_description = _("Message Preview")
    
    def has_add_permission(self, request):
        """Disable adding chat history through admin."""
        return False
    
    def has_change_permission(self, request, obj=None):
        """Disable changing chat history through admin."""
        return False


@admin.register(AIAssistantSettings)
class AIAssistantSettingsAdmin(admin.ModelAdmin):
    """Admin interface for AIAssistantSettings model."""
    
    list_display = [
        'ollama_model', 'ollama_base_url', 'temperature', 
        'max_tokens', 'is_active'
    ]
    list_filter = ['is_active', 'ollama_model', 'enable_langchain']
    
    fieldsets = (
        (_('LLM Configuration'), {
            'fields': ('ollama_model', 'ollama_base_url', 'max_tokens', 'temperature')
        }),
        (_('Security Settings'), {
            'fields': ('enable_security_validation', 'max_chat_history')
        }),
        (_('Feature Flags'), {
            'fields': ('enable_langchain', 'enable_memory', 'is_active')
        }),
    )
    
    def has_delete_permission(self, request, obj=None):
        """Prevent deletion of settings."""
        return False


@admin.register(ConversationMemory)
class ConversationMemoryAdmin(admin.ModelAdmin):
    """Admin interface for ConversationMemory model."""
    
    list_display = [
        'user', 'session_id', 'last_accessed', 'expires_at', 
        'is_expired_status'
    ]
    list_filter = ['last_accessed', 'expires_at']
    search_fields = ['user__username', 'session_id']
    readonly_fields = ['last_accessed', 'is_expired_status']
    
    fieldsets = (
        (_('Session Information'), {
            'fields': ('user', 'session_id', 'expires_at')
        }),
        (_('Context Data'), {
            'fields': ('context_data',)
        }),
        (_('Metadata'), {
            'fields': ('last_accessed', 'is_expired_status'),
            'classes': ('collapse',)
        }),
    )
    
    def is_expired_status(self, obj):
        """Show if the memory is expired."""
        if obj.is_expired():
            return format_html(
                '<span style="color: red;">Expired</span>'
            )
        return format_html(
            '<span style="color: green;">Active</span>'
        )
    is_expired_status.short_description = _("Status")
    
    actions = ['cleanup_expired_memories']
    
    def cleanup_expired_memories(self, request, queryset):
        """Clean up expired conversation memories."""
        expired_count = 0
        for memory in queryset:
            if memory.is_expired():
                memory.delete()
                expired_count += 1
        
        self.message_user(
            request,
            f"Cleaned up {expired_count} expired conversation memories."
        )
    cleanup_expired_memories.short_description = _("Clean up expired memories")
