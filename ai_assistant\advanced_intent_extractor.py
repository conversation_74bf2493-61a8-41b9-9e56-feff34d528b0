"""
Advanced Intent Extractor for AI Assistant

This module provides sophisticated intent extraction that behaves like a human assistant.
It understands natural language perfectly and determines correct actions reliably.
"""

import re
import json
from typing import Dict, Any, List, Tuple, Optional
from datetime import datetime, timedelta
import logging
from .url_mapper import URLMapper

logger = logging.getLogger(__name__)


class AdvancedIntentExtractor:
    """Advanced intent extraction with human-like understanding."""
    
    def __init__(self):
        self.url_mapper = URLMapper()
        self.intent_patterns = self._build_intent_patterns()
        self.entity_extractors = self._build_entity_extractors()
        
    def extract_intent(self, text: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Extract intent from user command with high accuracy.
        
        Args:
            text: User's natural language command
            context: Optional context (current page, module, etc.)
            
        Returns:
            Dictionary with intent, action, URL, filters, etc.
        """
        text_lower = text.lower().strip()
        context = context or {}
        
        # Initialize result with high confidence baseline
        result = {
            'intent': 'reply',
            'module': 'general',
            'action': 'reply',
            'url': None,
            'redirect_url': None,
            'filters': {},
            'sort_by': None,
            'order': 'asc',
            'prefill_data': {},
            'message': 'I understand your request.',
            'confidence': 0.9  # Start with high confidence
        }
        
        # Step 1: Detect primary intent with high precision
        intent_result = self._detect_primary_intent(text_lower)
        result.update(intent_result)
        
        # Step 2: Extract entities (names, dates, departments, etc.)
        entities = self._extract_entities(text_lower)
        result['entities'] = entities
        
        # Step 3: Determine target module
        module = self._determine_module(text_lower, entities)
        result['module'] = module
        
        # Step 4: Build filters from entities
        filters = self._build_filters(entities, module)
        result['filters'] = filters
        
        # Step 5: Determine exact URL and action
        url_action = self._determine_url_and_action(result, text_lower)
        result.update(url_action)
        
        # Step 6: Generate human-like response message
        result['message'] = self._generate_response_message(result, text)
        
        # Step 7: Final confidence adjustment
        result['confidence'] = self._calculate_final_confidence(result, text_lower)
        
        return result
    
    def _detect_primary_intent(self, text: str) -> Dict[str, Any]:
        """Detect the primary intent with high precision."""
        
        # High-confidence intent patterns
        intent_patterns = {
            'search': {
                'patterns': [
                    r'\b(show|list|find|search|get|view|display|see)\s+(all\s+)?(employees?|staff|people|workers?)',
                    r'\b(show|list|view|display|get|check)\s+(all\s+)?(attendance|leave|payroll|records?)',
                    r'\b(get|find|search|locate)\s+.*(employee|staff|person|team)',
                    r'\b(view|see|check|display)\s+.*(records?|data|information)',
                    r'\b(show|list|view)\s+.*(requests?|applications?|balance)',
                    r'\b(check|view|show)\s+.*(balance|status|history)',
                    r'\b(list|show|view)\s+(it|marketing|sales|hr|finance|engineering|operations|admin)\s+(team|department|members?)',
                    r'\b(it|marketing|sales|hr|finance|engineering|operations|admin)\s+(team|department)\s+(members?|staff)',
                    r'\b(check|view|show)\s+attendance\s+(for|in|during)\s+\w+',
                    r'\b(view|show|list|display)\s+(payslips?|payroll\s+data)',
                ],
                'confidence': 0.95
            },
            'fill_form': {
                'patterns': [
                    r'\b(apply|create|add|new|fill|request)\s+(leave|vacation|holiday)',
                    r'\b(apply\s+leave|create\s+employee|add\s+staff|new\s+employee)',
                    r'\b(new|create|add|register|hire)\s+(employee|staff|worker|person)',
                    r'\b(fill|complete)\s+.*(form|creation|application)',
                    r'\b(request|submit|file)\s+.*(leave|application|request)',
                    r'\b(apply\s+for|request\s+for)\s+.*(leave|vacation|time\s+off)',
                    r'\b(request\s+time\s+off|take\s+leave|book\s+leave|schedule\s+leave)',
                ],
                'confidence': 0.95
            },
            'run_action': {
                'patterns': [
                    r'\b(run|process|execute|start|generate|create)\s+(payroll|payslips?)',
                    r'\b(run\s+payroll|process\s+payroll|generate\s+payslips?)',
                    r'\b(generate|create|produce)\s+(report|payslip|payroll)',
                    r'\b(clock\s+in|clock\s+out|check\s+in|check\s+out)',
                ],
                'confidence': 0.95
            },
            'export': {
                'patterns': [
                    r'\b(export|download|save|get)\s+.*(data|file|report|excel|csv)',
                    r'\b(download|export|save)\s+(attendance|employee|payroll|leave)',
                    r'\b(generate|create)\s+.*(report|file|export)',
                    r'\b(export\s+to|download\s+as)\s+.*(excel|csv|pdf)',
                ],
                'confidence': 0.95
            },
            'navigate': {
                'patterns': [
                    r'\b(go\s+to|navigate\s+to|take\s+me\s+to)\s+(dashboard|page|section)',
                    r'\b(go\s+to|navigate\s+to)\s+(employees?|staff|leave|attendance|payroll)',
                    r'\b(dashboard|home|main\s+page)$',
                    r'\b(show\s+me|direct\s+me\s+to)\s+.*(page|dashboard|section)',
                    r'\bopen\s+(leave|attendance|payroll|employee)\s+(page|dashboard|section)',
                    r'\bopen\s+(dashboard|page|section)',
                ],
                'confidence': 0.9
            },
            'sort': {
                'patterns': [
                    r'\b(sort|order)\s+.*(by|ascending|descending)',
                    r'\b(highest|lowest|top|bottom)\s+.*(salary|name|date|paid)',
                    r'\bsorted\s+by\s+(highest|lowest|name|salary)',
                    r'\b(show|list)\s+.*sorted\s+by',
                    r'\b(show|list|view)\s+.*\s+(highest|lowest|top|bottom)\s+(salary|paid)',
                ],
                'confidence': 0.95
            }
        }
        
        best_intent = 'reply'
        best_confidence = 0.3

        # Check for sort patterns first (higher priority)
        if 'sorted by' in text or re.search(r'\b(highest|lowest|top|bottom)\s+(salary|paid)', text):
            best_intent = 'sort'
            best_confidence = 0.95
        else:
            for intent, config in intent_patterns.items():
                for pattern in config['patterns']:
                    if re.search(pattern, text):
                        if config['confidence'] > best_confidence:
                            best_intent = intent
                            best_confidence = config['confidence']
                        break
        
        return {
            'intent': best_intent,
            'confidence': best_confidence
        }
    
    def _extract_entities(self, text: str) -> Dict[str, Any]:
        """Extract entities like names, departments, dates, etc."""
        entities = {}
        
        # Employee names
        name_patterns = [
            r'\b(for|of)\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)?)',
            r'\b([A-Z][a-z]+)\s+(from|in|at)',
            r'\bemployee\s+([A-Z][a-z]+)',
        ]
        
        for pattern in name_patterns:
            match = re.search(pattern, text)
            if match:
                entities['employee_name'] = match.group(-1)
                break
        
        # Departments
        dept_patterns = [
            r'\b(marketing|sales|hr|it|finance|engineering|operations|admin)\s+(team|department|dept)',
            r'\bin\s+(marketing|sales|hr|it|finance|engineering|operations|admin)',
        ]
        
        for pattern in dept_patterns:
            match = re.search(pattern, text)
            if match:
                entities['department'] = match.group(1)
                break
        
        # Date ranges
        date_patterns = [
            r'\b(from|between)\s+(\w+\s+\d+)\s+(to|and)\s+(\w+\s+\d+)',
            r'\b(july|june|may|april|march|february|january|august|september|october|november|december)\s+(\d+)',
            r'\b(\d+)\s+(days?|weeks?|months?)',
        ]
        
        for pattern in date_patterns:
            match = re.search(pattern, text)
            if match:
                entities['date_range'] = match.group(0)
                break
        
        # Sorting criteria
        sort_patterns = [
            r'\bby\s+(highest|lowest)\s+(salary|name|date)',
            r'\bsorted\s+by\s+(salary|name|date)',
            r'\b(highest|lowest|top|bottom)\s+(salary|paid|earning)',
        ]
        
        for pattern in sort_patterns:
            match = re.search(pattern, text)
            if match:
                entities['sort_criteria'] = match.group(0)
                break
        
        return entities
    
    def _determine_module(self, text: str, entities: Dict[str, Any]) -> str:
        """Determine the target module with high accuracy."""

        # Special cases first
        if 'dashboard' in text.lower():
            # Check for specific module dashboards first
            if 'leave dashboard' in text.lower():
                return 'leave'
            elif 'employee dashboard' in text.lower():
                return 'employee'
            elif 'attendance dashboard' in text.lower():
                return 'attendance'
            elif 'payroll dashboard' in text.lower():
                return 'payroll'
            elif any(mod in text.lower() for mod in ['employee', 'leave', 'attendance', 'payroll']):
                # Let the normal scoring handle it
                pass
            else:
                return 'base'

        module_keywords = {
            'base': [
                'dashboard', 'home', 'main page', 'main dashboard'
            ],
            'employee': [
                'employee', 'staff', 'worker', 'people', 'person', 'emp',
                'team member', 'colleague', 'personnel'
            ],
            'leave': [
                'leave', 'vacation', 'holiday', 'time off', 'absence', 'pto',
                'sick leave', 'annual leave', 'casual leave'
            ],
            'attendance': [
                'attendance', 'present', 'absent', 'clock', 'punch', 'check in',
                'check out', 'time sheet', 'working hours'
            ],
            'payroll': [
                'payroll', 'salary', 'pay', 'wage', 'payslip', 'compensation',
                'earnings', 'deduction', 'bonus'
            ],
            'recruitment': [
                'recruitment', 'hiring', 'candidate', 'job', 'interview',
                'applicant', 'position', 'vacancy'
            ]
        }

        # Score each module
        module_scores = {}
        for module, keywords in module_keywords.items():
            score = 0
            for keyword in keywords:
                if keyword in text:
                    score += 1
            module_scores[module] = score

        # Return module with highest score
        if module_scores:
            best_module = max(module_scores, key=module_scores.get)
            if module_scores[best_module] > 0:
                return best_module

        return 'employee'  # Default to employee module
    
    def _build_filters(self, entities: Dict[str, Any], module: str) -> Dict[str, Any]:
        """Build filters from extracted entities."""
        filters = {}
        
        if entities.get('employee_name'):
            filters['employee_name'] = entities['employee_name']
        
        if entities.get('department'):
            filters['department'] = entities['department']
        
        if entities.get('date_range'):
            filters['date_range'] = entities['date_range']
        
        return filters
    
    def _determine_url_and_action(self, result: Dict[str, Any], text: str) -> Dict[str, Any]:
        """Determine the exact URL and action based on intent and module."""

        intent = result['intent']
        module = result['module']
        filters = result.get('filters', {})

        # Determine action based on intent
        if intent == 'export':
            action = 'download'
        elif intent == 'sort':
            action = 'ajax_update'
        elif intent in ['search', 'navigate', 'fill_form', 'run_action']:
            action = 'redirect'
        else:
            action = 'reply'

        # Special handling for clock in/out actions
        if intent == 'run_action' and module == 'attendance':
            if 'clock in' in text.lower() or 'check in' in text.lower():
                url = '/attendance/clock-in/'
            elif 'clock out' in text.lower() or 'check out' in text.lower():
                url = '/attendance/clock-out/'
            else:
                url = self.url_mapper.get_url(intent, module)
        else:
            # Get URL from URL mapper (without filters for cleaner URLs)
            url = self.url_mapper.get_url(intent, module)

        if url:
            return {
                'url': url,
                'redirect_url': url,
                'action': action
            }

        # Fallback to dashboard if no specific URL found
        dashboard_url = self.url_mapper.get_dashboard_url(module)
        if dashboard_url:
            return {
                'url': dashboard_url,
                'redirect_url': dashboard_url,
                'action': 'redirect'
            }

        return {
            'url': None,
            'redirect_url': None,
            'action': 'reply'
        }
    
    def _generate_response_message(self, result: Dict[str, Any], original_text: str) -> str:
        """Generate human-like response message."""
        
        intent = result['intent']
        module = result['module']
        action = result.get('action', 'reply')
        
        if action == 'redirect':
            messages = {
                'search': f"Opening {module} records for you.",
                'fill_form': f"Opening {module} creation form.",
                'export': f"Preparing {module} data export.",
                'navigate': f"Taking you to {module} dashboard.",
                'run_action': f"Processing {module} action.",
            }
            return messages.get(intent, f"Opening {module} page.")
        
        return "I understand your request."
    
    def _calculate_final_confidence(self, result: Dict[str, Any], text: str) -> float:
        """Calculate final confidence score."""

        base_confidence = result.get('confidence', 0.5)

        # Boost confidence if we have a clear URL
        if result.get('url'):
            base_confidence += 0.25

        # Boost confidence if we extracted entities
        if result.get('entities'):
            base_confidence += 0.15

        # Boost confidence for exact matches
        exact_matches = [
            'show all employees', 'list all employees', 'view employees',
            'apply leave', 'create leave request', 'request leave',
            'view attendance', 'show attendance', 'export attendance',
            'run payroll', 'process payroll', 'generate payslips',
            'export data', 'download data', 'export employees',
            'go to dashboard', 'open dashboard', 'navigate to dashboard'
        ]

        text_lower = text.lower()
        for phrase in exact_matches:
            if phrase in text_lower:
                base_confidence += 0.2
                break

        # Boost confidence for clear action words
        action_words = ['show', 'list', 'view', 'display', 'get', 'find', 'search',
                       'create', 'add', 'new', 'apply', 'request', 'fill',
                       'export', 'download', 'save', 'generate',
                       'go', 'navigate', 'open', 'take me to']

        for word in action_words:
            if word in text_lower:
                base_confidence += 0.1
                break

        # Ensure minimum confidence for clear intents
        if result.get('intent') in ['search', 'fill_form', 'export', 'navigate']:
            base_confidence = max(base_confidence, 0.85)

        return min(base_confidence, 1.0)
    
    def _build_url_mappings(self) -> Dict[str, Dict[str, str]]:
        """Build comprehensive URL mappings."""
        # This will be populated with actual URL mappings
        return {}
    
    def _build_intent_patterns(self) -> Dict[str, List[str]]:
        """Build intent pattern mappings."""
        # This will be populated with intent patterns
        return {}
    
    def _build_entity_extractors(self) -> Dict[str, Any]:
        """Build entity extraction patterns."""
        # This will be populated with entity extractors
        return {}
