# AI Assistant Dependencies
# These are optional dependencies for enhanced AI functionality

# <PERSON><PERSON>hain for AI agent framework
langchain>=0.0.350
langchain-community>=0.0.10

# Ollama for local LLM integration
ollama>=0.1.7

# spaCy for enhanced NLP (optional)
spacy>=3.7.0

# Additional utilities
python-dateutil>=2.8.2
requests>=2.31.0

# For enhanced text processing
nltk>=3.8.1

# For better JSON handling
jsonschema>=4.19.0

# Installation instructions:
# 1. Install Ollama: https://ollama.ai/
# 2. Pull a model: ollama pull mistral
# 3. Install Python dependencies: pip install -r ai_assistant/requirements.txt
# 4. Download spaCy model: python -m spacy download en_core_web_sm
