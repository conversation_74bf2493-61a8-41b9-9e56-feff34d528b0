{% extends 'settings.html' %}
{% load i18n %}
{% block settings %}{% load static %}
<div class="oh-inner-sidebar-content">
	<div class="oh-inner-sidebar-content__header d-flex justify-content-between align-items-center">
		<h2 class="oh-inner-sidebar-content__title">{% trans "Action Type" %}</h2>
		{% if perms.employee.add_actiontype %}
		<button class="oh-btn oh-btn--secondary oh-btn--shadow"
			data-toggle="oh-modal-toggle"
			data-target="#objectCreateModal"
			hx-get="{% url 'action-type-create' %}"
			hx-target="#objectCreateModalTarget"
			>
			<ion-icon name="add-outline" class="me-1"></ion-icon>
			{% trans "Create" %}
		</button>
		{% endif %}
	</div>
	{% if action_types %}
		<div id="actionTypes">
			{% include 'base/action_type/action_type_view.html' %}
		</div>
	{% else %}
		<div style="display: flex; flex-direction: column; justify-content: center; align-items: center; height: 100%;">
			<img style="display: block; width: 15%; margin: 20px auto; filter: opacity(0.5);" src="{% static 'images/ui/legal.png' %}" class="" alt="Page not found. 404." />
			<h5 class="oh-404__subtitle">{% trans "There is no disciplinary action type at this moment." %}</h5>
		</div>
	{% endif %}
</div>

<script>
	function actionChange(selectElement) {
		var selectedActiontype = selectElement.val();
		var parentForm = selectElement.parents().closest("form");
		$.ajax({
			type: "post",
			url: "{% url 'action-type-name' %}",
			data: {
				csrfmiddlewaretoken: getCookie("csrftoken"),
				"action_type": selectedActiontype,
			},
			success: function (response) {

				// Check if the response.action_type is "warning"
				if (response.action_type === "warning") {
					// Hide the 'block_option' field
					parentForm.find('[id=id_block_option]').parent().hide();
				} else {
					// Show the 'block_option' field
					parentForm.find('[id=id_block_option]').parent().show();
				}

			},
		});
	  }
</script>
{% endblock settings %}
