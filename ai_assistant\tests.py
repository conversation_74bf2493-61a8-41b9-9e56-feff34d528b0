"""
AI Assistant Tests
"""

import json
from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse

from .models import <PERSON>t<PERSON>istory, AIAssistantSettings, ConversationMemory
from .security import SecurityValidator, InputSanitizer
from .command_processor import CommandProcessor


class ChatHistoryModelTest(TestCase):
    """Test ChatHistory model."""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
    
    def test_chat_history_creation(self):
        """Test creating a chat history entry."""
        response_data = {
            'action': 'reply',
            'message': 'Test response',
            'intent': 'search'
        }
        
        chat = ChatHistory.objects.create(
            user=self.user,
            message='Test message',
            response=response_data,
            intent='search',
            confidence=0.8
        )
        
        self.assertEqual(chat.user, self.user)
        self.assertEqual(chat.message, 'Test message')
        self.assertEqual(chat.intent, 'search')
        self.assertEqual(chat.confidence, 0.8)
        self.assertEqual(chat.get_response_message(), 'Test response')


class SecurityValidatorTest(TestCase):
    """Test SecurityValidator class."""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        self.validator = SecurityValidator(self.user)
    
    def test_dangerous_pattern_detection(self):
        """Test detection of dangerous patterns."""
        dangerous_commands = [
            'DROP TABLE employees',
            'DELETE FROM users',
            '<script>alert("xss")</script>',
            'javascript:alert(1)'
        ]
        
        for command in dangerous_commands:
            result = {'intent': 'search', 'module': 'employee'}
            validated = self.validator.validate_command(command, result)
            self.assertEqual(validated['intent'], 'error')
    
    def test_safe_command_validation(self):
        """Test validation of safe commands."""
        safe_command = 'show all employees in marketing department'
        result = {'intent': 'search', 'module': 'employee'}
        
        validated = self.validator.validate_command(safe_command, result)
        self.assertNotEqual(validated['intent'], 'error')


class InputSanitizerTest(TestCase):
    """Test InputSanitizer class."""
    
    def test_command_sanitization(self):
        """Test command sanitization."""
        dirty_command = '<script>alert("xss")</script>show employees'
        clean_command = InputSanitizer.sanitize_command(dirty_command)
        
        self.assertNotIn('<script>', clean_command)
        self.assertIn('show employees', clean_command)
    
    def test_filter_sanitization(self):
        """Test filter sanitization."""
        dirty_filters = {
            'department<script>': 'HR',
            'name': '<script>alert(1)</script>John',
            'valid_field': 'valid_value'
        }
        
        clean_filters = InputSanitizer.sanitize_filters(dirty_filters)
        
        self.assertNotIn('department<script>', clean_filters)
        self.assertIn('valid_field', clean_filters)
        self.assertEqual(clean_filters['valid_field'], 'valid_value')


class CommandProcessorTest(TestCase):
    """Test CommandProcessor class."""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        self.processor = CommandProcessor(self.user)
    
    def test_search_command_processing(self):
        """Test processing of search commands."""
        command_result = {
            'intent': 'search',
            'module': 'employee',
            'filters': {'department': 'HR'},
            'sort_by': 'name',
            'order': 'asc'
        }
        
        processed = self.processor.process_command(command_result)
        
        self.assertIn('redirect_url', processed)
        self.assertIn('/employee/employee-view/', processed['redirect_url'])


class AIAssistantViewTest(TestCase):
    """Test AI Assistant views."""
    
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        self.client.login(username='testuser', password='testpass123')
    
    def test_ai_command_view(self):
        """Test AI command view."""
        url = reverse('ai_assistant:ai-command')
        data = {'message': 'show all employees'}
        
        response = self.client.post(
            url,
            data=json.dumps(data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 200)
        response_data = json.loads(response.content)
        self.assertIn('action', response_data)
        self.assertIn('message', response_data)
    
    def test_ai_command_view_empty_message(self):
        """Test AI command view with empty message."""
        url = reverse('ai_assistant:ai-command')
        data = {'message': ''}
        
        response = self.client.post(
            url,
            data=json.dumps(data),
            content_type='application/json'
        )
        
        self.assertEqual(response.status_code, 400)


class AIAssistantSettingsTest(TestCase):
    """Test AIAssistantSettings model."""
    
    def test_get_settings(self):
        """Test getting AI assistant settings."""
        settings = AIAssistantSettings.get_settings()
        
        self.assertIsNotNone(settings)
        self.assertEqual(settings.ollama_model, 'mistral')
        self.assertTrue(settings.is_active)
    
    def test_settings_creation(self):
        """Test creating AI assistant settings."""
        settings = AIAssistantSettings.objects.create(
            ollama_model='llama3',
            temperature=0.5,
            max_tokens=500
        )
        
        self.assertEqual(settings.ollama_model, 'llama3')
        self.assertEqual(settings.temperature, 0.5)
        self.assertEqual(settings.max_tokens, 500)
