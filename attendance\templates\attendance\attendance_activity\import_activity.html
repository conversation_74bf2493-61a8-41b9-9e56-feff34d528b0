{% load i18n %}
<div class="oh-modal__dialog-header pb-0">
    <h2 class="oh-modal__dialog-title" id="activityImportLabel">
        {% trans "Import Attendance Activities" %}
    </h2>

    <button class="oh-modal__close" aria-label="Close">
        <ion-icon name="close-outline"></ion-icon>
    </button>
</div>

<div class="oh-modal__dialog-body" id="activityImportModalBody">
    <form hx-post="{% url 'attendance-activity-import' %}" hx-encoding="multipart/form-data"
        hx-target="#objectCreateModalTarget" hx-swap="outerHTML" class="oh-profile-section" id="activityImportForm">
        {% csrf_token %}
        <div id="uploading" class="oh-modal__dialog-body mr-5" style="display: none;">
            <div class="loader-container" style="margin: auto;">
                <div class="loader"></div>
                <div class="loader-text">{% trans "Uploading..." %}</div>
                <div class="text-center" style="-webkit-text-stroke: medium;">
                    {% trans "Don't refresh the page" %}
                </div>
            </div>
        </div>

        <div id="uploadContainer">
            <label for="uploadFile" class="oh-dropdown__import-label">
                <ion-icon name="cloud-upload" class="oh-dropdown__import-form-icon"></ion-icon>
                <span class="oh-dropdown__import-form-title">{% trans "Upload a File" %}</span>
                <span class="oh-dropdown__import-form-text">{% trans "Drag and drop files here" %}</span>
            </label>
            <input type="file" name="activity_import" required />
            <div class="d-flex flex-row-reverse">
                <button type="submit" class="oh-btn oh-btn--small oh-btn--secondary mt-3">
                    {% trans "Upload" %}
                </button>
            </div>
        </div>
    </form>
</div>

<script>
    $(document).ready(function () {
        Swal.fire({
            text: "{% trans 'Do you want to download the template?' %}",
            icon: "question",
            showCancelButton: true,
            confirmButtonColor: "#008000",
            cancelButtonColor: "#d33",
            confirmButtonText: "{% trans 'Confirm' %}"
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    type: "GET",
                    url: "{% url 'attendance-activity-import-excel' %}",
                    xhrFields: { responseType: "blob" },
                    success: function (response) {
                        const url = URL.createObjectURL(new Blob([response], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" }));
                        const link = document.createElement("a");
                        link.href = url;
                        link.download = "activity_excel.xlsx";
                        document.body.appendChild(link);
                        link.click();
                        URL.revokeObjectURL(url);
                    },
                    error: function () {
                        console.error("{% trans 'Error downloading file' %}");
                    }
                });
            }
        });

        $('#activityImportForm').on('submit', function () {
            $('#uploadContainer').hide();
            $('#uploading').show();
        });
    });
</script>
