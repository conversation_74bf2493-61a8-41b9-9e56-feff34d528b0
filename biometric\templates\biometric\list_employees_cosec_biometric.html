{% load i18n %} {% load static %}
<div class="oh-sticky-table">
  <div class="oh-sticky-table__table oh-table--sortable">
    <div class="oh-sticky-table__thead">
      <div class="oh-sticky-table__tr">
        <div class="oh-sticky-table__th" style="width: 10px">
          <div class="centered-div">
            <input
              type="checkbox"
              class="oh-input oh-input__checkbox all-bio-employee"
              title="Select All"
              data-device="{{device_id}}"
              id="allBioEmployee"
            />
          </div>
        </div>
        <div class="oh-sticky-table__th">{% trans "Employee" %}</div>
        <div class="oh-sticky-table__th">{% trans "User ID" %}</div>
        <div class="oh-sticky-table__th">{% trans "Reference ID" %}</div>
        <div class="oh-sticky-table__th">{% trans "Active" %}</div>
        <div class="oh-sticky-table__th">
          {% if employees.users %}
            {% if employees.users.0.finger_count %}
              {% trans "Finger Count" %}
            {% else %}
              {% trans "Face Count" %}
            {% endif %}
          {% else %}
            {% trans "Biometric Template" %}
          {% endif %}
        </div>
        <div class="oh-sticky-table__th">{% trans "Card Count" %}</div>
        <div class="oh-sticky-table__th">{% trans "Valid Active" %}</div>
        <div class="oh-sticky-table__th">{% trans "Valid End Date" %}</div>
        <div class="oh-sticky-table__th">{% trans "Work Email" %}</div>
        <div class="oh-sticky-table__th">{% trans "Phone" %}</div>
        <div class="oh-sticky-table__th">{% trans "Job Position" %}</div>
        <div class="oh-sticky-table__th oh-sticky-table__right" style="width: 210px;">
          {% trans "Actions" %}
        </div>
      </div>
    </div>
    {% for employee in employees.users %}
    <div class="oh-sticky-table__tbody ui-sortable">
      <div class="oh-sticky-table__tr ui-sortable-handle">
        <div class="oh-sticky-table__sd">
          <div class="centered-div">
            <input
              type="checkbox"
              id="{{employee.user_id}}"
              class="form-check-input all-bio-employee-row"
            />
          </div>
        </div>
        <div class="oh-sticky-table__td">
          {{employee.employee_id.get_full_name}}
        </div>
        <div class="oh-sticky-table__td">{{employee.user_id}}</div>
        <div class="oh-sticky-table__td">{{employee.ref_user_id}}</div>
        <div class="oh-sticky-table__td">
          {% if employee.user_active == "1" %}
            {% trans "Yes" %}
          {% else %}
            {% trans "No" %}
          {% endif %}
        </div>
        <div class="oh-sticky-table__td">
          {% if employee.finger_count or employee.face_count %}
            {% if employee.finger_count %}
              {{employee.finger_count}}
            {% else %}
              {{employee.face_count}}
            {% endif %}
          {% else %}
            {% trans "Not Enrolled" %}
          {% endif %}
        </div>
        <div class="oh-sticky-table__td">{{employee.card_count}}</div>
        <div class="oh-sticky-table__td">
          {% if employee.validity_enable == "1" %} {% trans "Yes" %} {% else %}
          {% trans "No" %} {% endif %}
        </div>
        <div class="oh-sticky-table__td">
          {{employee.validity_date_dd}}/{{employee.validity_date_mm}}/{{employee.validity_date_yyyy}}
        </div>
        <div class="oh-sticky-table__td">
          {{employee.employee_id.employee_work_info.email}}
        </div>
        <div class="oh-sticky-table__td">{{employee.employee_id.phone}}</div>
        <div class="oh-sticky-table__td">
          {{employee.employee_id.get_job_position}}
        </div>
        <div class="oh-sticky-table__td oh-sticky-table__right">
          <div class="oh-btn-group">
            {% if employee.face_count %}
              <a
                class="oh-btn oh-btn--light-bkg w-100"
                href="{% url 'enable-cosec-face-recognition' user_id=employee.user_id device_id=device_id %}"
                onclick="event.preventDefault();event.stopPropagation(); confirm(`{% trans 'Do you want to delete this user from the biometric device?' %}`)"
                title="{% trans 'Enable Face' %}"
                >
                <ion-icon name="person-circle-outline"></ion-icon>
              </a>
            {% endif %}
            <a
              onclick="event.stopPropagation();"
              class="oh-btn oh-btn--light-bkg w-100"
              data-toggle="oh-modal-toggle"
              data-target="#objectUpdateModal"
              hx-target="#objectUpdateModalTarget"
              hx-get="{% url 'edit-cosec-user' user_id=employee.user_id device_id=device_id %}"
              title="{% trans 'Edit' %}"
              >
              <ion-icon
                name="create-outline"
                role="img"
                class="md hydrated"
                style="color: blue"
                aria-label="create outline"
              ></ion-icon>
            </a>
            <a
              class="oh-btn oh-btn--danger-outline oh-btn--light-bkg w-100"
              href="{% url 'delete-cosec-user' user_id=employee.user_id device_id=device_id %}"
              onclick="event.preventDefault();event.stopPropagation(); confirm(`{% trans 'Do you want to delete this user from the biometric device?' %}`)"
              title="{% trans 'Delete' %}"
              >
              <ion-icon
                name="trash-outline"
                role="img"
                class="md hydrated"
                aria-label="trash outline"
              ></ion-icon>
            </a>
          </div>
        </div>
      </div>
    </div>
    {% endfor %}
  </div>
</div>

<div class="oh-pagination">
  <span class="oh-pagination__page">
    {% trans "Page" %} {{ employees.paginator.number }}
    {% trans "of" %} {{ employees.paginator.num_pages }}.
  </span>
  <nav class="oh-pagination__nav">
    <div class="oh-pagination__input-container me-3">
      <span class="oh-pagination__label me-1">{% trans "Page" %}</span>
      <input
        type="number"
        name="page"
        class="oh-pagination__input"
        value="{{employees.paginator.number}}"
        hx-get="{% url 'search-employee-in-device' %}?{{pd}}&view=list&device={{device_id}}"
        hx-target="#section"
        min="1"
      />
      <span class="oh-pagination__label"
        >{% trans "of" %} {{employees.paginator.num_pages}}</span
      >
    </div>
    <ul class="oh-pagination__items">
      {% if employees.paginator.has_previous %}
      <li class="oh-pagination__item oh-pagination__item--wide">
        <a
          hx-target="#section"
          hx-get="{% url 'search-employee-in-device' %}?{{pd}}&view=list&page=1&device={{device_id}}"
          class="oh-pagination__link"
          >{% trans "First" %}</a
        >
      </li>
      <li class="oh-pagination__item oh-pagination__item--wide">
        <a
          hx-target="#section"
          hx-get="{% url 'search-employee-in-device' %}?{{pd}}&view=list&page={{ employees.paginator.previous_page_number }}&device={{device_id}}"
          class="oh-pagination__link"
          >{% trans "Previous" %}</a
        >
      </li>
      {% endif %} {% if employees.paginator.has_next %}
      <li class="oh-pagination__item oh-pagination__item--wide">
        <a
          hx-target="#section"
          hx-get="{% url 'search-employee-in-device' %}?{{pd}}&view=list&page={{ employees.paginator.next_page_number }}&device={{device_id}}"
          class="oh-pagination__link"
          >{% trans "Next" %}</a
        >
      </li>
      <li class="oh-pagination__item oh-pagination__item--wide">
        <a
          hx-target="#section"
          hx-get="{% url 'search-employee-in-device' %}?{{pd}}&view=list&page={{ employees.paginator.num_pages }}&device={{device_id}}"
          class="oh-pagination__link"
          >{% trans "Last" %}</a
        >
      </li>
      {% endif %}
    </ul>
  </nav>
</div>
