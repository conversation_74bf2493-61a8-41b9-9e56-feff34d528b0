{% load i18n %}{% load static %}
{% include 'filter_tags.html' %}
{% load basefilters %}
{% if messages %}
<div class="oh-wrapper">
  {% for message in messages %}
  <div class="oh-alert-container">
    <div class="oh-alert oh-alert--animated {{message.tags}}">
      {{ message }}
    </div>
  </div>
  {% endfor %}
</div>
{% endif %}
{% if data %}
  <div class="oh-table_sticky--wrapper">
    <div class="oh-sticky-dropdown--header">
      <div class="oh-dropdown" x-data="{open: false}">
        <button class="oh-sticky-dropdown_btn " @click="open = !open"><ion-icon name="ellipsis-vertical-sharp"
          role="img" class="md hydrated" aria-label="ellipsis vertical sharp"></ion-icon></button>
        <div class="oh-dropdown__menu oh-sticky-table_dropdown" x-show="open" @click.outside="open = false">
        <ul class="oh-dropdown__items" id="LateComeCells">
        </ul>
        </div>
      </div>
    </div>
    <div id="late-early-table" data-table-name="late_early_table">
      <div div class="oh-sticky-table" >
        <div class="oh-sticky-table__table oh-table--sortable">
          <div class="oh-sticky-table__thead">
            <div class="oh-sticky-table__tr">
              <div class="oh-sticky-table__th" style="width: 20px;">
                <div class="centered-div">
                  <input type="checkbox" name="selectAllInstance" title="{% trans 'Select All' %}" class="oh-input oh-input__checkbox all-latecome"/>
                </div>
              </div>
              <div class="oh-sticky-table__th {% if request.sort_option.order == '-employee_id__employee_first_name' %}arrow-up {% elif request.sort_option.order == 'employee_id__employee_first_name' %}arrow-down {% else %} arrow-up-down {% endif %}" scope="col" hx-target='#report-container' hx-get="{% url 'late-come-early-out-search' %}?{{pd}}&sortby=employee_id__employee_first_name">{% trans "Employee" %}</div>
              <div class="oh-sticky-table__th {% if request.sort_option.order == '-type' %}arrow-up {% elif request.sort_option.order == 'type' %} arrow-down {% else %} arrow-up-down {% endif %}" data-cell-index="1" data-cell-title='{% trans "Type" %}' scope="col" hx-target='#report-container' hx-get="{% url 'late-come-early-out-search' %}?{{pd}}&sortby=type">{% trans "Type" %}</div>
              <div style="width:170px" class="oh-sticky-table__th {% if request.sort_option.order == '-attendance_id__attendance_date' %}arrow-up {% elif request.sort_option.order == 'attendance_id__attendance_date' %}arrow-down {% else %} arrow-up-down {% endif %}" data-cell-index="2" data-cell-title='{% trans "Attendance Date" %}' scope="col" hx-target='#report-container' hx-get="{% url 'late-come-early-out-search' %}?{{pd}}&sortby=attendance_id__attendance_date">{% trans "Attendance Date"%}</div>
              <div class="oh-sticky-table__th" data-cell-index="3" data-cell-title='{% trans "Check-In" %}' scope="col">{% trans "Check-In" %}</div>
              <div class="oh-sticky-table__th {% if request.sort_option.order == '-attendance_id__attendance_clock_in_date' %}arrow-up {% elif request.sort_option.order == 'attendance_id__attendance_clock_in_date' %}arrow-down {% else %} arrow-up-down {% endif %}" data-cell-index="4" data-cell-title='{% trans "In-Date" %}' scope="col" hx-target='#report-container' hx-get="{% url 'late-come-early-out-search' %}?{{pd}}&sortby=attendance_id__attendance_clock_in_date">{% trans "In Date" %}</div>
              <div class="oh-sticky-table__th" data-cell-index="5" data-cell-title='{% trans "Check-Out" %}' scope="col">{% trans "Check-Out" %}</div>
              <div class="oh-sticky-table__th {% if request.sort_option.order == '-attendance_id__attendance_clock_out_date' %}arrow-up {% elif request.sort_option.order == 'attendance_id__attendance_clock_out_date' %}arrow-down {% else %} arrow-up-down {% endif %}" data-cell-index="6" data-cell-title='{% trans "Out-Date" %}' scope="col" hx-target='#report-container' hx-get="{% url 'late-come-early-out-search' %}?{{pd}}&sortby=attendance_id__attendance_clock_out_date">{% trans "Out Date" %}</div>
              <div class="oh-sticky-table__th " data-cell-index="7" data-cell-title='{% trans "Min Hour" %}' scope="col" hx-target='#report-container' hx-get="{% url 'late-come-early-out-search' %}?{{pd}}&sortby=">{% trans "Min Hour" %}</div>
              <div class="oh-sticky-table__th {% if request.sort_option.order == '-attendance_id__at_work_second' %}arrow-up {% elif request.sort_option.order == 'attendance_id__at_work_second' %}arrow-down {% else %} arrow-up-down {% endif %}" data-cell-index="8" data-cell-title='{% trans "At Work" %}' scope="col" hx-target='#report-container' hx-get="{% url 'late-come-early-out-search' %}?{{pd}}&sortby=attendance_id__at_work_second">{% trans "At Work" %}</div>
              <div class="oh-sticky-table__th" data-cell-index="9" data-cell-title='{% trans "Penalties" %}' scope="col">{% trans "Penalties" %}</div>
              {% if request.user|is_reportingmanager or perms.attendance.change_penaltyaccounts or perms.attendance.delete_attendancelatecomeearlyout %}
                <div class="oh-sticky-table__th oh-sticky-table__right"  scope="col" style="width: 150px;">{% trans "Actions" %}</div>
              {% endif %}
              {% comment %} <div class='oh-sticky-table__th' scope="col" style="width: 80px;"></div> {% endcomment %}
            </div>
          </div>
          <div class="oh-sticky-table__tbody">
            {% for late_in_early_out in data %}
            <div class="oh-sticky-table__tr" draggable="false"
              data-toggle="oh-modal-toggle" data-target="#objectDetailsModal"
              hx-target="#objectDetailsModalTarget"
              hx-get="{% url 'late-in-early-out-single-view' late_in_early_out.id %}?{{pd}}&instances_ids={{late_in_early_out_ids}}">
              <div class="oh-sticky-table__sd" onclick="event.stopPropagation();">
                <div class="centered-div">
                  <input type="checkbox" onchange="highlightRow($(this))" id="{{late_in_early_out.id}}" class="oh-input attendance-checkbox oh-input__checkbox all-latecome-row" />
                </div>
              </div>
              <div class="oh-sticky-table__td">
                <div class="oh-profile oh-profile--md">
                  <div class="oh-profile__avatar mr-1">
                    <img
                      src="{{late_in_early_out.employee_id.get_avatar}}"
                      class="oh-profile__image"
                      alt=""
                    />
                  </div>
                  <span class="oh-profile__name oh-text--dark"
                    >{{late_in_early_out.employee_id}}</span
                  >
                </div>
              </div>
              <div data-cell-index="1" class='oh-sticky-table__td'>
                {{late_in_early_out.get_type_display}}
              </div>
              <div data-cell-index="2" class='oh-sticky-table__td dateformat_changer'>{{late_in_early_out.attendance_id.attendance_date}}</div>
              <div data-cell-index="3" class='oh-sticky-table__td timeformat_changer'>{{late_in_early_out.attendance_id.attendance_clock_in}}</div>
              <div data-cell-index="4" class='oh-sticky-table__td dateformat_changer'>{{late_in_early_out.attendance_id.attendance_clock_in_date}}</div>
              <div data-cell-index="5" class='oh-sticky-table__td timeformat_changer'>{{late_in_early_out.attendance_id.attendance_clock_out}}</div>
              <div data-cell-index="6" class='oh-sticky-table__td dateformat_changer'>{{late_in_early_out.attendance_id.attendance_clock_out_date}}</div>
              <div data-cell-index="7" class='oh-sticky-table__td'>{{late_in_early_out.attendance_id.minimum_hour}}</div>
              <div data-cell-index="8" class='oh-sticky-table__td'>{{late_in_early_out.attendance_id.attendance_worked_hour}}</div>
              <div data-cell-index="9" class='oh-sticky-table__td' onclick="event.stopPropagation();">
                {% if late_in_early_out.get_penalties_count %}
                <div class="" data-target="#penaltyViewModal" data-toggle="oh-modal-toggle" hx-get="{% url "view-penalties" %}?late_early_id={{late_in_early_out.id}}" hx-target="#penaltyViewModalBody" align="center" style="background-color: rgba(229, 79, 56, 0.036); border: 2px solid rgb(229, 79, 56); border-radius: 18px; padding: 10px; font-weight: bold; width: 85%;">Penalties :{{late_in_early_out.get_penalties_count}}</div>
                {% endif %}
              </div>
              {% if request.user|is_reportingmanager or perms.attendance.change_penaltyaccounts or perms.attendance.delete_attendancelatecomeearlyout %}
                <div class="oh-sticky-table__td oh-sticky-table__right" onclick="event.stopPropagation();">
                  <div class="oh-btn-group">
                    {% if request.user|is_reportingmanager or perms.attendance.change_penaltyaccounts %}
                    <button
                      data-toggle="oh-modal-toggle"
                      data-target="#penaltyModal"
                      hx-target="#penaltyModalBody"
                      hx-get="{% url "cut-penalty" late_in_early_out.id %}?{{pd}}"
                      type='submit'
                      class="oh-btn oh-btn--danger-outline oh-btn--light-bkg w-50"
                      title="{% trans 'Penalty' %}">
                      <ion-icon name="information-circle-outline"></ion-icon>
                    </button>
                    {% endif %}
                    {% if perms.attendance.delete_attendancelatecomeearlyout %}
                      <form hx-confirm="{% trans 'Are you sure want to delete this attendance?' %}"
                            hx-post="{% url 'late-come-early-out-delete' late_in_early_out.id  %}?{{pd}}"
                            hx-target="#report-container"
                            onclick="event.stopPropagation()"
                            class="w-50"
                        >
                        {% csrf_token %}
                        <button type='submit' class="oh-btn oh-btn--danger-outline oh-btn--light-bkg w-100" title="{% trans 'Remove' %}">
                          <ion-icon name="trash-outline"></ion-icon>
                        </button>
                      </form>
                    {% endif %}
                  </div>
                </div>
              {% endif %}
            </div>
            {% endfor %}
          </div>
        </div>
      </div>
  </div>
  </div>

  <div class="oh-pagination">
    <span
      class="oh-pagination__page"
      >
      {% trans "Page" %} {{ data.number }} {% trans "of" %} {{ data.paginator.num_pages }}.
      </span
    >
    <nav class="oh-pagination__nav">
      <div class="oh-pagination__input-container me-3">
        <span class="oh-pagination__label me-1">{% trans "Page" %}</span>
        <input
          type="number"
          name="page"
          class="oh-pagination__input"
          value="{{data.number}}"
          hx-get="{% url 'late-come-early-out-search' %}?{{pd}}"
          hx-target="#report-container"
          min="1"
        />
        <span class="oh-pagination__label">{% trans "of" %} {{data.paginator.num_pages}}</span>
      </div>
      <ul class="oh-pagination__items">
        {% if data.has_previous %}
        <li class="oh-pagination__item oh-pagination__item--wide">
          <a hx-target='#report-container' hx-get="{% url 'late-come-early-out-search' %}?{{pd}}&page=1" class="oh-pagination__link" onclick="ticklatecomeCheckboxes()">{% trans "First" %}</a>
        </li>
        <li class="oh-pagination__item oh-pagination__item--wide">
          <a hx-target='#report-container' hx-get="{% url 'late-come-early-out-search' %}?{{pd}}&page={{ data.previous_page_number }}" class="oh-pagination__link" onclick="ticklatecomeCheckboxes()">{% trans "Previous" %}</a>
        </li>
        {% endif %}
        {% if data.has_next %}
        <li class="oh-pagination__item oh-pagination__item--wide">
          <a hx-target='#report-container' hx-get="{% url 'late-come-early-out-search' %}?{{pd}}&page={{ data.next_page_number }}" class="oh-pagination__link" onclick="ticklatecomeCheckboxes()">{% trans "Next" %}</a>
        </li>
        <li class="oh-pagination__item oh-pagination__item--wide">
          <a hx-target='#report-container' hx-get="{% url 'late-come-early-out-search' %}?{{pd}}&page={{ data.paginator.num_pages }}" class="oh-pagination__link" onclick="ticklatecomeCheckboxes()">{% trans "Last" %}</a>
        </li>
        {% endif %}
      </ul>
    </nav>
  </div>
{% else %}
  <!-- start of empty page -->
  <div class="oh-404">
    <img
      style="width: 150px; height: 150px"
      src="{% static 'images/ui/no-results.png' %}"
      class="oh-404__image mb-4"
    />
    <h5 class="oh-404__subtitle">
      {% trans "No search result found!" %}
    </h5>
  </div>
  <!-- end of empty page -->
{% endif %}
<script>

  $(document).ready(function () {
    $('#selectAllLatecome').show();
    ticklatecomeCheckboxes();
    $(".all-latecome-row").change(function () {
      var parentTable = $(this).closest(".oh-sticky-table");
      var body = parentTable.find(".oh-sticky-table__tbody");
      var parentCheckbox = parentTable.find(".all-latecome");
      parentCheckbox.prop(
        "checked",
        body.find(".all-latecome-row:checked").length ===
          body.find(".all-latecome-row").length
      );
      addinglatecomeIds();
    });

    $(".all-latecome").change(function () {
      addinglatecomeIds();
    });

    $("#selectAllLatecome").click(function () {
      selectAllLatecome();
    });

    $("#unselectAllLatecome").click(function () {
      unselectAllLatecome();
    });
  });


	toggleColumns("late-early-table","LateComeCells")
  localStorageLateComeCells = localStorage.getItem("late_early_table")
  if (!localStorageLateComeCells) {
    $("#LateComeCells").find("[type=checkbox]").prop("checked",true)
  }
  $("[type=checkbox]").change()
</script>
