{% extends "index.html" %}{% block content %}{% load static %}{% load i18n %}
<section class="oh-wrapper oh-main__topbar" x-data="{searchShow: false}">
  <div class="oh-main__titlebar oh-main__titlebar--left">
    <h1 class="oh-main__titlebar-title fw-bold">
      {% trans "Multiple Approval Condition" %}
    </h1>
    <a
      class="oh-main__titlebar-search-toggle"
      role="button"
      aria-label="Toggle Search"
      @click="searchShow = !searchShow"
    >
      <ion-icon
        name="search-outline"
        class="oh-main__titlebar-serach-icon"
      ></ion-icon>
    </a>
  </div>
  <div
    class="oh-main__titlebar oh-main__titlebar--right oh-d-flex-column--resp oh-mb-3--small"
  >
    <div class="oh-main__titlebar-button-container">
      <a
        hx-get="{% url 'multiple-level-approval-create' %}"
        hx-target="#objectCreateModalTarget"
        data-toggle="oh-modal-toggle"
        data-target="#objectCreateModal"
        class="oh-btn oh-btn--secondary"
      >
        <ion-icon name="add-outline"></ion-icon>
        {% trans 'Create' %}
      </a>
    </div>
  </div>
</section>
<div class="oh-wrapper" id="multipleApproveCondition">
  {% include "multi_approval_condition/condition_table.html" %}
</div>
{% endblock content %}
