{% load i18n %}
<div class="row">
	<div class="col-12 col-sm-12 col-md-12 col-lg-12">
		<div class="oh-card p-4">
			<div class="oh-card__body">
				<div class="oh-sticky-table oh-sticky-table--no-highlight">
					<div class="oh-sticky-table__table">
						<div class="oh-sticky-table__thead">
							<div class="oh-sticky-table__tr">
								<div class="oh-sticky-table__th">{% trans "Group" %}</div>
								<div class="oh-sticky-table__th">{% trans "Permissions" %}</div>
								<div class="oh-sticky-table__th"></div>
							</div>
						</div>
						<div class="oh-sticky-table__tbody">
							{% for gp in groups %}
							<div
								class="oh-sticky-table__tr oh-permission-table__tr oh-permission-table--collapsed"
							>
								<div class="oh-sticky-table__sd oh-permission-table--toggle">
									<div class="d-flex align-items-center">
										<button class="oh-permission-table__collapse me-2">
											<ion-icon
												class="oh-permission-table__collapse-down"
												title="{% trans 'Reveal' %}"
												name="chevron-down-outline"
											></ion-icon>
											<ion-icon
												class="oh-permission-table__collapse-up"
												title="{% trans 'Collapse' %}"
												name="chevron-up-outline"
											></ion-icon>
										</button>
										<span class="oh-permission-table__user">{{gp}}.</span>
									</div>
								</div>
								<div class="oh-sticky-table__td">
									<span class="perm-count"
										>{{gp.permissions.all|length}} {% trans "Permissions"%}</span
									>
									{% for permission in gp.permissions.all %}
									<span
										class="oh-permission-panel oh-collapse-panel"
										data-label="Permissions"
										data-count="{{gp.permissions.all|length}}"
									>
										{{permission}} {% if perms.change_group %}
										<form
											action="{% url 'group-permission-remove' permission.id gp.id %}"
											method="post"
										>
											{% csrf_token %}
											<button
												type="submit"
												class="oh-permission-panel__remove"
												title='{% trans "Remove" %}'
											>
												<ion-icon name="close-outline"></ion-icon>
											</button>
										</form>
										{% endif %}
									</span>
									{% endfor %}
								</div>
								<div class="oh-sticky-table__sd">
									<div class="d-flex">
										{% if perms.change_group %}
										<a
											href="{% url 'user-group-update' gp.id %}"
											type="button"
											class="oh-btn oh-btn--light-bkg w-50"
											title="{% trans 'Edit' %}"
										>
											<ion-icon name="create-outline"></ion-icon
										></a>
										{% endif %}
										{% if perms.delete_group %}
										<form action="{% url 'user-group-delete' gp.id %}"
                          onsubmit="return confirm('{% trans "Are you sure you want to delete this group?" %}');"
											    class="w-50" method='post'>
											{% csrf_token %}
											<button
												type="submit"
												class="oh-btn oh-btn--danger-outline oh-btn--light-bkg w-100"
												title="{% trans 'Remove' %}"
											  >
												<ion-icon name="trash-outline"></ion-icon>
											</button>
										</form>
										{% endif %}
									</div>
								</div>
							</div>
							{% endfor %}
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
