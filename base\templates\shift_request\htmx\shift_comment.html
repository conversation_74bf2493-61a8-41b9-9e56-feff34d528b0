{% load i18n static %}
{% load basefilters %}
<style>
	#enlargeImageContainer {
		position: absolute;
		left: -300px;
		top: 100px;
		height: 200px;
		width: 200px;
	}
</style>

{% if messages %}
<script>
	reloadMessage();
</script>
{% endif %}

<div class="oh-activity-sidebar__header">
	<a style="cursor: pointer;" onclick="$('.oh-activity-sidebar--show').removeClass('oh-activity-sidebar--show');">
		<ion-icon name="chevron-forward-outline"
			class="oh-activity-sidebar__header-icon me-2 oh-activity-sidebar__close"
			data-target="#activitySidebar"></ion-icon>
	</a>
	<span class="oh-activity-sidebar__title"> {% trans "Comments" %} </span>
</div>
{% if request.user.employee_get == shift_request.employee_id or perms.base.add_shiftrequestcomment or request.user|is_reportingmanager %}
	<form hx-target="#commentContainer" hx-post="{% url 'shift-request-add-comment' request_id %}?{{pd}}" id="commentForm">
		{% csrf_token %}
		<div>
			<input type="text" name="comment" id="commentInput" hx-on:keyup="toggleCommentButton(this);"
				class="oh-input w-100" placeholder="Comment here">
			<button type="submit" id="commentButton" class="oh-btn oh-btn--secondary mt-2 mr-0 oh-btn--w-100-resp"
				style="display: none;">
				{% trans "Comment" %}
			</button>
		</div>
		<div class="oh-inner-sidebar-content__footer"></div>
	</form>
{% endif %}

{% if comments %}
	<ol class="oh-activity-sidebar__qa-list" role="list">
		{% for comment in comments %}
			<li class="oh-activity-sidebar__qa-item" id="commentCard{{comment.id}}">
				<span class="oh-activity-sidebar__q">{{ comment.comment }}
					{% if request.user.employee_get == comment.employee_id or perms.base.delete_shiftrequestcomment or request.user|is_reportingmanager %}
						<span class="float-end" hx-on:click="setTimeout(() => {reloadMessage(this);},100);"
							hx-get='{% url "shift-request-delete-comment" comment.id %}' hx-swap="outerHTML"
							hx-target="#commentCard{{comment.id}}">
							<ion-icon name="close-outline" style="font-size: 24px" role="img" class="md hydrated"
								aria-label="close outline"></ion-icon></span>
					{% endif %}
				</span>

				<div class="d-flex mt-2 mb-2">
					{% for file in comment.files.all %}

						<a href="{{ file.file.url }}" rel="noopener noreferrer" target="_blank" id="shiftReqCommentFile{{file.id}}">
							<span class="oh-file-icon oh-file-icon--pdf" onmouseover="enlargeImage('{{ file.file.url }}',$(this))" style="width: 40px; height: 40px">
								{% if request.user.employee_get == comment.employee_id or perms.base.delete_baserequestfile or request.user|is_reportingmanager %}
									<img src="{% static 'images/ui/minus-icon.png' %}" style="display: block; width: 50%; height: 50%" onclick="event.stopPropagation();event.preventDefault()"
										hx-get="{% url 'delete-shift-comment-file' %}?ids={{ file.id }}&shift_id={{ request_id }}&comment_id={{comment.id}}" hx-target="#shiftReqCommentFile{{file.id}}"
										hx-swap="outerHTML" hx-on:click="setTimeout(() => {reloadMessage(this);},100);"
										/>
								{% endif %}
							</span>
						</a>
					{% endfor %}
					{% if request.user.employee_get == comment.employee_id or perms.base.add_baserequestfile or request.user|is_reportingmanager %}
						<form hx-post="{% url 'view-shift-comment' comment.request_id.id %}?comment_id={{ comment.id }}"
							class="add-files-form" hx-encoding="multipart/form-data" data-target="#activitySidebar"
							hx-swap="innerHTML" hx-target="#commentContainer">
							{% csrf_token %}
							<label for="addFile_{{comment.id}}" \><ion-icon name="add-outline" style="font-size: 24px" role="img"
									class="md hydrated" aria-label="add outline"></ion-icon></label>
							<input type="file" name="files" class="d-none" multiple="true" accept=".jpg, .jpeg, .png, .pdf"
								id="addFile_{{comment.id}}" onchange="submitForm(this)" />
							<input type="submit" class="d-none add_more_submit" value="save" />
						</form>
					{% endif %}
				</div>
				<span class="oh-activity-sidebar__a">
					{% trans 'by' %}
					<img src="{{ note.note_by.get_avatar }}" style="width: 1.5em; border-radius: 100%" />
					{{ comment.employee_id.get_full_name }} @ {{comment.request_id.employee_id.get_full_name }}
					{% trans "'s shift request" %}
				</span>
				<div style="width: 50%">
					<div id="enlargeImageContainer" class="enlargeImageContainer"></div>
				</div>
			</li>
		{% endfor %}
	</ol>
{% else %}
	<div class="oh-timeoff-modal__profile-content">
		<div class="oh-404">
			<div class="">
				<span class="oh-timeoff-title fw-bold" style="display: block">{% trans "There are no comments to show." %}</span>
				<img style="display: block; width: 100px; margin: 20px auto" src="{% static "/images/ui/comment.png" %}" class="" />
			</div>
		</div>
	</div>
{% endif %}
