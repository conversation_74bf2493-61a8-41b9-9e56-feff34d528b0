{% load i18n %}
{% load static %}
{% load attendancefilters %}
{% load basefilters %}
{% include 'filter_tags.html' %}
{% if messages %}
<div class="oh-wrapper">
  {% for message in messages %}
  <div class="oh-alert-container">
    <div class="oh-alert oh-alert--animated {{message.tags}}">
      {{ message }}
    </div>
  </div>
  {% endfor %}
</div>
{% endif %}
{% if data %}
<div class="oh-card">
{% for attendance_list in data %}
<div class="oh-accordion-meta">
  <div class="oh-accordion-meta__item">
    <div class="oh-accordion-meta__header" onclick='$(this).toggleClass("oh-accordion-meta__header--show");'>
      <span class="oh-accordion-meta__title pt-3 pb-3">
        <div class="oh-tabs__input-badge-container">
          <span
            class="oh-badge oh-badge--secondary oh-badge--small oh-badge--round mr-1"
          >
            {{attendance_list.list.paginator.count}}
          </span>
          {{attendance_list.grouper}}
        </div>
      </span>
    </div>
    <div class="oh-accordion-meta__body d-none">
      <div class="oh-sticky-table oh-sticky-table--no-overflow mb-5">
        <div div class="oh-sticky-table" >
          <div class="oh-sticky-table__table oh-table--sortable">
            <div class="oh-sticky-table__thead">
              <div class="oh-sticky-table__tr">
                <div class="oh-sticky-table__th" style="width: 20px;">
                  <div class="centered-div">
                    <input type="checkbox" title="{% trans 'Select All' %}" class="oh-input oh-input__checkbox all-latecome"/>
                  </div>
                </div>
                <div class='oh-sticky-table__th' scope="col" hx-target='#report-container' hx-get="{% url 'late-come-early-out-search' %}?{{pd}}&sortby=employee_id__employee_first_name">{% trans "Employee" %}</div>
                <div class='oh-sticky-table__th' scope="col" hx-target='#report-container' hx-get="{% url 'late-come-early-out-search' %}?{{pd}}&sortby=type">{% trans "Type" %}</div>
                <div class='oh-sticky-table__th' scope="col" hx-target='#report-container' hx-get="{% url 'late-come-early-out-search' %}?{{pd}}&sortby=attendance_id__attendance_date">{% trans "Attendance Date" %}</div>
                <div class='oh-sticky-table__th' scope="col" hx-target='#report-container' hx-get="{% url 'late-come-early-out-search' %}?{{pd}}&sortby=">{% trans "Check-In" %}</div>
                <div class='oh-sticky-table__th' scope="col" hx-target='#report-container' hx-get="{% url 'late-come-early-out-search' %}?{{pd}}&sortby=attendance_id__attendance_clock_in_date">{% trans "In Date" %}</div>
                <div class='oh-sticky-table__th' scope="col" hx-target='#report-container' hx-get="{% url 'late-come-early-out-search' %}?{{pd}}&sortby=">{% trans "Check-Out" %}</div>
                <div class='oh-sticky-table__th' scope="col" hx-target='#report-container' hx-get="{% url 'late-come-early-out-search' %}?{{pd}}&sortby=attendance_id__attendance_clock_out_date">{% trans "Out Date" %}</div>
                <div class='oh-sticky-table__th' scope="col" hx-target='#report-container' hx-get="{% url 'late-come-early-out-search' %}?{{pd}}&sortby=">{% trans "Min Hour" %}</div>
                <div class='oh-sticky-table__th' scope="col" hx-target='#report-container' hx-get="{% url 'late-come-early-out-search' %}?{{pd}}&sortby=attendance_id__at_work_second">{% trans "At Work" %}</div>
                <div class='oh-sticky-table__th' scope="col">{% trans "Penalties" %}</div>
                {% if request.user|is_reportingmanager or perms.attendance.change_penaltyaccounts or perms.attendance.delete_attendancelatecomeearlyout %}
                  <div class='oh-sticky-table__th oh-sticky-table__right' scope="col" style="width: 150px;">{% trans "Actions" %}</div>
                {% endif %}
              </div>
            </div>
            <div class="oh-sticky-table__tbody">
              {% for late_in_early_out in attendance_list.list %}
              <div class="oh-sticky-table__tr" draggable="false"
                data-toggle="oh-modal-toggle" data-target="#objectDetailsModal"
                hx-target="#objectDetailsModalTarget"
                hx-get="{% url 'late-in-early-out-single-view' late_in_early_out.id %}?{{pd}}&instances_ids={{late_in_early_out_ids}}">
                <div class="oh-sticky-table__sd" onclick="event.stopPropagation();">
                  <div class="centered-div">
                    <input type="checkbox" id="{{late_in_early_out.id}}" class="oh-input attendance-checkbox oh-input__checkbox all-latecome-row" />
                  </div>
                </div>
                <div class="oh-sticky-table__td">
                  <div class="oh-profile oh-profile--md">
                    <div class="oh-profile__avatar mr-1">
                      <img
                        src="{{late_in_early_out.employee_id.get_avatar}}"
                        class="oh-profile__image"
                        alt=""
                      />
                    </div>
                    <span class="oh-profile__name oh-text--dark"
                      >{{late_in_early_out.employee_id}}</span
                    >
                  </div>
                </div>
                <div class='oh-sticky-table__td'>
                  {% if late_in_early_out.type == 'late_come' %}
                  {% trans "Late Come" %}
                  {% else %}
                  {% trans "Early Out" %}
                  {% endif %}
                </div>
                <div class='oh-sticky-table__td dateformat_changer'>{{late_in_early_out.attendance_id.attendance_date}}</div>
                <div class='oh-sticky-table__td timeformat_changer'>{{late_in_early_out.attendance_id.attendance_clock_in}}</div>
                <div class='oh-sticky-table__td dateformat_changer'>{{late_in_early_out.attendance_id.attendance_clock_in_date}}</div>
                <div class='oh-sticky-table__td timeformat_changer'>{{late_in_early_out.attendance_id.attendance_clock_out}}</div>
                <div class='oh-sticky-table__td dateformat_changer'>{{late_in_early_out.attendance_id.attendance_clock_out_date}}</div>
                <div class='oh-sticky-table__td'>{{late_in_early_out.attendance_id.minimum_hour}}</div>
                <div class='oh-sticky-table__td'>{{late_in_early_out.attendance_id.attendance_worked_hour}}</div>
                <div class='oh-sticky-table__td' onclick="event.stopPropagation();">
                  {% if late_in_early_out.get_penalties_count %}
                  <div class="" data-target="#penaltyViewModal" data-toggle="oh-modal-toggle" hx-get="{% url "view-penalties" %}?late_early_id={{late_in_early_out.id}}" hx-target="#penaltyViewModalBody" align="center" style="background-color: rgba(229, 79, 56, 0.036); border: 2px solid rgb(229, 79, 56); border-radius: 18px; padding: 10px; font-weight: bold; width: 85%;">Penalties :{{late_in_early_out.get_penalties_count}}</div>
                  {% endif %}
                </div>
                {% if request.user|is_reportingmanager or perms.attendance.change_penaltyaccounts or perms.attendance.delete_attendancelatecomeearlyout %}
                  <div class="oh-sticky-table__td oh-sticky-table__right" onclick="event.stopPropagation();">
                    <div class="oh-btn-group">
                      {% if request.user|is_reportingmanager or perms.attendance.change_penaltyaccounts %}
                        <button
                          data-toggle="oh-modal-toggle"
                          data-target="#penaltyModal"
                          hx-target="#penaltyModalBody"
                          hx-get="{% url "cut-penalty" late_in_early_out.id %}?{{pd}}"
                          type='submit'
                          class="oh-btn oh-btn--danger-outline oh-btn--light-bkg w-50"
                          title="{% trans 'Penalty' %}">
                          <ion-icon name="information-circle-outline"></ion-icon>
                        </button>
                      {% endif %}
                      {% if perms.attendance.delete_attendancelatecomeearlyout %}
                        <form hx-confirm="{% trans 'Are you sure want to delete this attendance?' %}"
                              hx-post="{% url 'late-come-early-out-delete' late_in_early_out.id  %}?{{pd}}"
                              hx-target="#report-container"
                              onclick="event.stopPropagation()"
                              class="w-50"
                            >
                          {% csrf_token %}
                          <button type='submit' class="oh-btn oh-btn--danger-outline oh-btn--light-bkg w-100" title="{% trans 'Remove' %}">
                            <ion-icon name="trash-outline"></ion-icon>
                          </button>
                        </form>
                      {% endif %}
                    </div>
                  </div>
                {% endif %}

              </div>
              {% endfor %}
            </div>
          </div>
        </div>
        <div class="oh-pagination">
          <span class="oh-pagination__page">
            {% trans "Page" %} {{ attendance_list.list.number }} {% trans "of" %} {{ attendance_list.list.paginator.num_pages }}.
          </span>
          <nav class="oh-pagination__nav">
            <div class="oh-pagination__input-container me-3">
            <span class="oh-pagination__label me-1">{% trans "Page" %}</span>
            <input
              type="number"
              name="{{attendance_list.dynamic_name}}"
              class="oh-pagination__input"
              value="{{attendance_list.list.number}}"
              hx-get="{% url 'late-come-early-out-search' %}?{{pd}}"
              hx-target="#report-container"
              min="1"
            />
            <span class="oh-pagination__label"
              >{% trans "of" %} {{attendance_list.list.paginator.num_pages}}</span
            >
            </div>
            <ul class="oh-pagination__items">
            {% if attendance_list.list.has_previous %}
            <li class="oh-pagination__item oh-pagination__item--wide">
              <a
              hx-target="#report-container"
              hx-get="{% url 'late-come-early-out-search' %}?{{pd}}&{{attendance_list.dynamic_name}}=1"
              class="oh-pagination__link"
              >{% trans "First" %}</a
              >
            </li>
            <li class="oh-pagination__item oh-pagination__item--wide">
              <a
              hx-target="#report-container"
              hx-get="{% url 'late-come-early-out-search' %}?{{pd}}&{{attendance_list.dynamic_name}}={{ attendance_list.list.previous_page_number }}"
              class="oh-pagination__link"
              >{% trans "Previous" %}</a
              >
            </li>
            {% endif %} {% if attendance_list.list.has_next %}
            <li class="oh-pagination__item oh-pagination__item--wide">
              <a
              hx-target="#report-container"
              hx-get="{% url 'late-come-early-out-search' %}?{{pd}}&{{attendance_list.dynamic_name}}={{ attendance_list.list.next_page_number }}"
              class="oh-pagination__link"
              >{% trans "Next" %}</a
              >
            </li>
            <li class="oh-pagination__item oh-pagination__item--wide">
              <a
              hx-target="#report-container"
              hx-get="{% url 'late-come-early-out-search' %}?{{pd}}&{{attendance_list.dynamic_name}}={{ attendance_list.list.paginator.num_pages }}"
              class="oh-pagination__link"
              >{% trans "Last" %}</a
              >
            </li>
            {% endif %}
            </ul>
          </nav>
          </div>
    </div>
  </div>
</div>
{% endfor %}

<div class="oh-pagination">
  <span
    class="oh-pagination__page"
    >
    {% trans "Page" %} {{ data.number }} {% trans "of" %} {{ data.paginator.num_pages }}.
    </span
  >
  <nav class="oh-pagination__nav">
    <div class="oh-pagination__input-container me-3">
      <span class="oh-pagination__label me-1">{% trans "Page" %}</span>
      <input
        type="number"
        name="page"
        class="oh-pagination__input"
        value="{{data.number}}"
        hx-get="{% url 'late-come-early-out-search' %}?{{pd}}"
        hx-target="#report-container"
        min="1"
      />
      <span class="oh-pagination__label">{% trans "of" %} {{data.paginator.num_pages}}</span>
    </div>
    <ul class="oh-pagination__items">
      {% if data.has_previous %}
      <li class="oh-pagination__item oh-pagination__item--wide">
        <a hx-target='#report-container' hx-get="{% url 'late-come-early-out-search' %}?{{pd}}&page=1" class="oh-pagination__link">{% trans "First" %}</a>
      </li>
      <li class="oh-pagination__item oh-pagination__item--wide">
        <a hx-target='#report-container' hx-get="{% url 'late-come-early-out-search' %}?{{pd}}&page={{ data.previous_page_number }}" class="oh-pagination__link">{% trans "Previous" %}</a>
      </li>
      {% endif %}
      {% if data.has_next %}
      <li class="oh-pagination__item oh-pagination__item--wide">
        <a hx-target='#report-container' hx-get="{% url 'late-come-early-out-search' %}?{{pd}}&page={{ data.next_page_number }}" class="oh-pagination__link">{% trans "Next" %}</a>
      </li>
      <li class="oh-pagination__item oh-pagination__item--wide">
        <a hx-target='#report-container' hx-get="{% url 'late-come-early-out-search' %}?{{pd}}&page={{ data.paginator.num_pages }}" class="oh-pagination__link">{% trans "Last" %}</a>
      </li>
      {% endif %}
    </ul>
  </nav>
</div>
</div>

{% else %}
  <div class="oh-wrapper">
    <div class="oh-empty">
        <img src="{% static 'images/ui/search.svg' %}" class="oh-404__image" alt="Page not found. 404." />
        <h1 class="oh-empty__title">{% trans "No Records found." %}</h1>
        <p class="oh-empty__subtitle">{% trans "No group result found." %}</p>
    </div>
  </div>
{% endif %}
<script>
  $(".oh-table__sticky-collaspable-sort").click(function (e) {
    e.preventDefault();
    let clickedEl = $(e.target).closest(".oh-table__toggle-parent");
    let targetSelector = clickedEl.data("target");
    let toggleBtn = clickedEl.find(".oh-table__toggle-button");
    $(`[data-group='${targetSelector}']`).toggleClass(
      "oh-table__toggle-child--show"
    );
    if (toggleBtn) {
      toggleBtn.toggleClass("oh-table__toggle-button--show");
    }
  });
  $(document).ready(function() {
    {% if data %}
      $('#selectAllLatecome').show();
    {% else %}
      $('#selectAllLatecome').hide();
    {% endif %}
  })
</script>
