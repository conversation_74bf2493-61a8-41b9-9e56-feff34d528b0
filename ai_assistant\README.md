# AI Assistant for HRMS

A human-like AI assistant that integrates with your HRMS system to provide intelligent, conversational interaction with HR data and processes.

## 🌟 Features

- **Human-like Reasoning**: Uses local LLMs (via Ollama) with LangChain for intelligent responses
- **Natural Language Processing**: Understands vague and complex human inputs
- **Context-Aware**: Knows user role, current page, and system state
- **Action-Oriented**: Can perform real actions like searching, filling forms, running reports
- **Security-First**: Role-based permissions and input sanitization
- **Free & Open Source**: Uses local models only - no paid APIs required

## 🧠 What the AI Assistant Can Do

| User Says | AI Thinks & Does |
|-----------|------------------|
| "Get payroll of Sethu" | Find employee → open/export their payroll |
| "Show all employees in Marketing" | Find people in that department → display in table |
| "Apply leave for <PERSON>r<PERSON> from July 10 to 12" | Go to form → prefill employee and date → open page |
| "Export attendance for June" | Trigger export → prepare download |
| "Sort employees by highest salary" | Apply sorting on frontend table |
| "Open onboarding form" | Navigate to form page and allow data entry |
| "Run payroll for sales team" | Open payroll page with department filter + month |

## 🚀 Quick Start

### 1. Installation

```bash
# Install AI Assistant dependencies
pip install -r ai_assistant/requirements.txt

# Install and start Ollama
curl -fsSL https://ollama.ai/install.sh | sh
ollama serve

# Pull a language model
ollama pull mistral
```

### 2. Django Setup

Add to your `INSTALLED_APPS`:

```python
INSTALLED_APPS = [
    # ... your other apps
    'ai_assistant',
]
```

Add to your main `urls.py`:

```python
urlpatterns = [
    # ... your other URLs
    path('ai/', include('ai_assistant.urls')),
]
```

### 3. Run Setup Command

```bash
python manage.py setup_ai_assistant
```

### 4. Add to Templates

Add the chatbox to your base template:

```html
{% load ai_assistant_tags %}

<!-- Add this before closing </body> tag -->
{% ai_chatbox %}
```

### 5. Run Migrations

```bash
python manage.py makemigrations ai_assistant
python manage.py migrate
```

## 🛠️ Configuration

### AI Assistant Settings

Configure via Django admin or programmatically:

```python
from ai_assistant.models import AIAssistantSettings

settings = AIAssistantSettings.objects.create(
    ollama_model='mistral',  # or 'llama3', 'phi3', 'gemma'
    ollama_base_url='http://localhost:11434',
    temperature=0.7,
    max_tokens=1000,
    enable_langchain=True,
    enable_memory=True,
    is_active=True
)
```

### Available Models

- **mistral**: Fast and efficient, good for general tasks
- **llama3**: More capable, better reasoning
- **phi3**: Lightweight, good for basic tasks
- **gemma**: Google's model, good balance

## 🔧 Architecture

### Core Components

1. **LangChain Agent** (`langchain_agent.py`)
   - Orchestrates LLM reasoning
   - Manages conversation memory
   - Coordinates tool execution

2. **Django Tools Registry** (`django_tools.py`)
   - Provides HRMS-specific functions
   - Handles database operations
   - Manages permissions

3. **Intent Extractor** (`llm_intent_extractor.py`)
   - Enhanced with LLM reasoning
   - Fallback to rule-based patterns
   - Confidence scoring

4. **Security Layer** (`security.py`)
   - Input sanitization
   - Permission validation
   - Dangerous pattern detection

5. **URL Context System** (`url_context.py`)
   - Navigation assistance
   - Page context awareness
   - Breadcrumb generation

### Data Flow

```
User Input → Security Validation → LLM Processing → Tool Execution → Response
     ↓              ↓                    ↓              ↓            ↓
  Sanitize → Check Permissions → Extract Intent → Call Django → Format Output
```

## 🎯 Usage Examples

### Employee Management

```
User: "Show me all employees in the Chennai office"
AI: Searches employees with location filter → Redirects to filtered employee list

User: "Add a new employee named John Smith"
AI: Opens employee creation form → Prefills name field

User: "Export employee data for HR audit"
AI: Triggers employee data export → Provides download link
```

### Leave Management

```
User: "Apply leave for Sarah from Dec 25 to Jan 2"
AI: Opens leave form → Prefills employee, dates → Ready for submission

User: "Show pending leave requests"
AI: Filters leave requests by status → Displays pending requests

User: "What's my leave balance?"
AI: Looks up user's leave allocation → Shows remaining days by type
```

### Payroll Operations

```
User: "Run payroll for November"
AI: Opens payroll processing → Sets month filter → Ready to process

User: "Show Raj's payslip for last month"
AI: Searches employee → Filters payslips → Displays result

User: "Export payroll data for accounting"
AI: Triggers payroll export → Prepares download
```

## 🔒 Security Features

### Input Sanitization
- HTML tag removal
- Script injection prevention
- SQL injection protection
- XSS prevention

### Permission System
- Role-based access control
- Module-level permissions
- Action-specific validation
- User context awareness

### Dangerous Pattern Detection
- Database manipulation attempts
- System command injection
- File system access attempts
- Network request blocking

## 🎨 Frontend Integration

### Floating Chatbox
- Responsive design
- Mobile-friendly
- Customizable styling
- Real-time messaging

### Quick Actions
- Pre-defined commands
- Context-sensitive suggestions
- One-click operations
- Smart recommendations

### Chat History
- Conversation persistence
- Search functionality
- Export capabilities
- Privacy controls

## 📊 Monitoring & Analytics

### Chat History Tracking
- User interactions
- Intent detection accuracy
- Response times
- Error rates

### Performance Metrics
- LLM response times
- Tool execution success
- User satisfaction
- System load

## 🔧 Customization

### Adding New Tools

```python
# In django_tools.py
def my_custom_tool(self, parameter: str) -> str:
    """Custom tool description."""
    # Your implementation
    return json.dumps(result)

# Register the tool
self._register_tool(
    "my_custom_tool",
    self.my_custom_tool,
    "Description of what this tool does"
)
```

### Custom Intent Patterns

```python
# In intent_extractor.py
self.intent_patterns['my_intent'] = [
    r'\b(custom|pattern)\b.*\b(keywords)\b',
    r'\b(another|pattern)\b',
]
```

### Styling Customization

Override CSS classes in your template:

```css
.ai-chatbox {
    /* Your custom styles */
}

.ai-floating-btn {
    /* Your custom button styles */
}
```

## 🐛 Troubleshooting

### Common Issues

1. **Ollama not responding**
   ```bash
   # Check if Ollama is running
   ollama list
   
   # Restart Ollama
   ollama serve
   ```

2. **Model not found**
   ```bash
   # Pull the required model
   ollama pull mistral
   ```

3. **Permission denied errors**
   - Check user permissions in Django admin
   - Verify role-based access settings

4. **Slow responses**
   - Use smaller models (phi3 instead of llama3)
   - Reduce max_tokens setting
   - Check system resources

### Debug Mode

Enable debug logging:

```python
LOGGING = {
    'loggers': {
        'ai_assistant': {
            'level': 'DEBUG',
            'handlers': ['console'],
        },
    },
}
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure security compliance
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- **LangChain** for the agent framework
- **Ollama** for local LLM hosting
- **Django** for the web framework
- **Bootstrap** for UI components
