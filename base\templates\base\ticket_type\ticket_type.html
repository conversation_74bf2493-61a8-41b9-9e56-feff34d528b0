{% extends 'settings.html' %}
{% load i18n %}
{% block settings %}{% load static %}
<div class="oh-inner-sidebar-content">
    {% if perms.helpdesk.view_tickettype %}
        <div class="oh-inner-sidebar-content__header d-flex justify-content-between align-items-center">
            <h2 class="oh-inner-sidebar-content__title">{% trans "Ticket Type" %}</h2>
			{% if perms.helpdesk.add_tickettype %}
            <button
                class="oh-btn oh-btn--secondary oh-btn--shadow"
                data-toggle="oh-modal-toggle"
                data-target="#ticketModal"
                hx-get="{% url 'ticket-type-create' %}"
                hx-target="#ticketForm"
            	>
                <ion-icon name="add-outline" class="me-1"></ion-icon>
                {% trans "Create" %}
            </button>
			{% endif %}
        </div>
		{% if ticket_types %}
        	{% include 'base/ticket_type/ticket_type_view.html' %}
		{% else %}
			<div style="display: flex; flex-direction: column; justify-content: center; align-items: center; height: 100%;">
				<img style="display: block; width: 15%; margin: 20px auto; filter: opacity(0.5);" src="{% static 'images/ui/ticket.png' %}" class="" alt="Page not found. 404." />
				<h5 class="oh-404__subtitle">{% trans "There is no ticket types at this moment." %}</h5>
			</div>
		{% endif %}
    {% endif %}

</div>

<!-- start of modals -->

<!-- start of create modal -->
<div
	class="oh-modal"
	id="ticketModal"
	role="dialog"
	aria-labelledby="ticketModal"
	aria-hidden="true"
>
	<div class="oh-modal__dialog">
		<div class="oh-modal__dialog-header">
			<h2 class="oh-modal__dialog-title" id="createModaltitle">
				{% trans "Create Ticket Type" %}
			</h2>
			<button class="oh-modal__close" aria-label="Close">
				<ion-icon name="close-outline"></ion-icon>
			</button>
		</div>
		<div>
			<div class="oh-modal__dialog-body" id="ticketForm"></div>
		</div>
	</div>
</div>
<!-- end of create modal -->
<!-- start of edit modal -->
<div
	class="oh-modal"
	id="ticketEditModal"
	role="dialog"
	aria-labelledby="ticketEditModal"
	aria-hidden="true"
>
	<div class="oh-modal__dialog">
		<div class="oh-modal__dialog-header">
			<h2 class="oh-modal__dialog-title" id="editModaltitle">
				{% trans "Update Ticket Type" %}
			</h2>
			<button class="oh-modal__close" aria-label="Close">
				<ion-icon name="close-outline"></ion-icon>
			</button>
		</div>
		<div class="oh-modal__dialog-body" id="ticketEditForm"></div>
	</div>
</div>
<!-- end of edit modal -->
<!-- end of modals -->
{% endblock settings %}
