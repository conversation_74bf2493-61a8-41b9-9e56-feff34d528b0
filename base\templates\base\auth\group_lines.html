{% load static i18n basefilters %}
{% if groups %}
{% for group in groups %}
  <div class="oh-user_permission-list_item" onclick="$(this).next().toggle();$(this).toggleClass('perm-accordion-active');">
    <div class="oh-user_permission-list_profile">
        <div class="oh-navbar__user-photo oh-user_permission--profile">
            <img src="https://ui-avatars.com/api/?name={{group}}&background=random"
                class="oh-navbar__user-image" loading="lazy">
        </div>
        <div class="oh-feedback-card__name-container ms-1">
          <form hx-post='{% url "update-group-permission" %}' hx-vals='{"name_update": "true"}' hx-swap="none">
            <input type="hidden" name="id" value="{{group.id}}">
            <span class="oh-card__title oh-card__title--sm fw-bold me-1">
              <input class="oh-tabs__movable-title oh-table__editable-input" value="{{group}}" onkeyup="event.stopPropagation();$(this).closest('form').find('#hxSubmit').click()" hx-target="#hxSubmit"  name="name"style="width: 250px;" readonly="">
            </span>
            <button style="display: none;" type="submit" onclick="event.stopPropagation()" id="hxSubmit"></button>
          </form>
          <span class="oh-user_permission_list-text oh-text--light">{% trans "Total" %} {{group.user_set.all|length}} {% trans "users in this group" %}</span>
        </div>
    </div>
    {% if perms.auth.change_group or perms.auth.delete_group %}
      <div class="btn-group">
        {% if perms.auth.change_group %}
          <button class=" oh-btn oh-btn--light" hx-get="{% url "user-group-assign" %}" hx-vals='{"group":"{{group.id}}"}' hx-target="#groupAssignBody" data-toggle="oh-modal-toggle" data-target="#groupAssign" onclick="event.stopPropagation()">
            <ion-icon name="person-add-outline" class="text-dark"></ion-icon>
          </button>
        {% endif %}
        {% if perms.auth.delete_group %}
        <form action="{% url "user-group-delete" group.id %}" onsubmit="return confirm('Do you want to delete this group permission?')" method="post" style="display: inline;">
          {% csrf_token %}
          <button type="submit" class="oh-btn oh-btn--light" onclick="event.stopPropagation();"  >
            <ion-icon name="trash-outline" class="text-danger"></ion-icon>
          </button>
        </form>
        {% endif %}
      </div>
    {% endif %}
  </div>
  <div class="panel view-groups" id="panel{{group.id}}" data-group-id="{{group.id}}" data-group-name="{{group.name}}">
    <div class="oh-general__tab-target oh-profile-section">
      <script>
        $(document).ready(function () {
          checkSelected(`{{group.permissions.all|user_perms|safe}}`,"#panel{{group.id}}",true)
        });
      </script>
        {% include "base/auth/permission_table.html" %}
    </div>
  </div>
{% endfor %}
{% else %}
  <div style="display: flex; flex-direction: column; align-items: center; height: 100%;">
      <img style="display: block; width: 15%; margin: 20px auto; filter: opacity(0.5);" src="{% static 'images/ui/employee_group.png' %}" class="" alt="Page not found. 404." />
      <h5 class="oh-404__subtitle">{% trans "There is no employee group at this moment." %}</h5>
  </div>
{% endif %}



<div class="oh-wrapper w-100">
    <div class="oh-pagination">
      <span class="oh-pagination__page" data-toggle="modal" data-target="#addEmployeeModal">{% trans 'Page' %} {{ groups.number }} {% trans 'of' %} {{ groups.paginator.num_pages }}.</span>

      <nav class="oh-pagination__nav">
        <div class="oh-pagination__input-container me-3">
          <span class="oh-pagination__label me-1">{% trans 'Page' %}</span>

          <input type="number" name="page" class="oh-pagination__input" value="{{ groups.number }}" hx-get="{% url 'user-group-search' %}?{{ pd }}" hx-target="#permissionContainer" min="1" />
          <span class="oh-pagination__label">{% trans 'of' %} {{ groups.paginator.num_pages }}</span>
        </div>

        <ul class="oh-pagination__items">
          {% if groups.has_previous %}
            <li class="oh-pagination__item oh-pagination__item--wide">
              <a hx-target="#permissionContainer" hx-get="{% url 'user-group-search' %}?{{ pd }}&page=1" class="oh-pagination__link">{% trans 'First' %}</a>
            </li>
            <li class="oh-pagination__item oh-pagination__item--wide">
              <a hx-target="#permissionContainer" hx-get="{% url 'user-group-search' %}?{{ pd }}&page={{ groups.previous_page_number }}" class="oh-pagination__link">{% trans 'Previous' %}</a>
            </li>
          {% endif %}
          {% if groups.has_next %}
            <li class="oh-pagination__item oh-pagination__item--wide">
              <a hx-target="#permissionContainer" hx-get="{% url 'user-group-search' %}?{{ pd }}&page={{ groups.next_page_number }}" class="oh-pagination__link">{% trans 'Next' %}</a>
            </li>
            <li class="oh-pagination__item oh-pagination__item--wide">
              <a hx-target="#permissionContainer" hx-get="{% url 'user-group-search' %}?{{ pd }}&page={{ groups.paginator.num_pages }}" class="oh-pagination__link">{% trans 'Last' %}</a>
            </li>
          {% endif %}
        </ul>
      </nav>
    </div>
  </div>
