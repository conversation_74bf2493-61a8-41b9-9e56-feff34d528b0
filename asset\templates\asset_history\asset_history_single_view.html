{% load static %} {% load i18n %}
<div class="oh-modal__dialog-header">
    <h2 class="oh-modal__dialog-title mb-0" id="historySingleModalLabel">
          {% trans "Asset Details" %}
    </h2>
    <button class="oh-modal__close" aria-label="Close" >
          <ion-icon name="close-outline"></ion-icon>
    </button>

<div class="oh-modal__dialog-body oh-modal__dialog-relative p-0 pt-2">
    {% if request.GET.requests_ids %}
    <div class="oh-modal__dialog oh-modal__dialog--navigation m-0 p-0">
        <button hx-get="{% url "asset-history-single-view" previous %}?requests_ids={{requests_ids}}" hx-target = "#objectDetailsModalTarget" style="left:-90px" class="oh-modal__diaglog-nav oh-modal__nav-prev" data-action="previous">
            <ion-icon name="chevron-back-outline" class="md hydrated" role="img"
            aria-label="chevron back outline"></ion-icon>
        </button>

        <button hx-get="{% url "asset-history-single-view" next %}?requests_ids={{requests_ids}}" hx-target = "#objectDetailsModalTarget" style="right:-60px" class="oh-modal__diaglog-nav oh-modal__nav-next" data-action="next">
            <ion-icon name="chevron-forward-outline" class="md hydrated" role="img"
            aria-label="chevron forward outline"></ion-icon>
        </button>
    </div>
    {% endif %}

    <div class="oh-timeoff-modal__profile-content pt-2 m-0">
        <div class="oh-profile mb-2">
            <div class="oh-profile__avatar">
                <img
                src="{{asset_assignment.assigned_to_employee_id.get_avatar}}"
                class="oh-profile__image me-2"
                alt="Mary Magdalene"
                />
            </div>
            <div class="oh-timeoff-modal__profile-info">
                <span class="oh-timeoff-modal__user fw-bold m-0"
                >{{asset_assignment.asset_id}}</span
                >
                <span
                class="oh-timeoff-modal__user m-0"
                style="font-size: 18px; color: #4d4a4a"
                >
                {{asset_assignment.asset_id.asset_category_id}} </span
                >
            </div>
        </div>
    </div>

    <div class="oh-modal__dialog-header pt-0">
    <div class="oh-timeoff-modal__stats-container">
    	<div class="oh-timeoff-modal__stat">
    		<span class="oh-timeoff-modal__stat-title"
    			>{% trans "Allocated User" %}</span
    		>
    		<span class="oh-timeoff-modal__stat-count"
    			>{{asset_assignment.assigned_to_employee_id}}</span
    		>
    	</div>
    	<div class="oh-timeoff-modal__stat">
    		<span class="oh-timeoff-modal__stat-title"
    			>{% trans "Returned Status" %}
    		</span>
    		<span class="oh-timeoff-modal__stat-count"
    			>{{asset_assignment.return_status}}</span
    		>
    	</div>
    </div>

    <div class="oh-timeoff-modal__stats-container mt-3">
    	<div class="oh-timeoff-modal__stat">
    		<span class="oh-timeoff-modal__stat-title"
    			>{% trans "Allocated Date" %}
    		</span>
    		<span class="oh-timeoff-modal__stat-count dateformat_changer"
    			>{{asset_assignment.assigned_date}}</span
    		>
    	</div>
    	<div class="oh-timeoff-modal__stat">
    		<span class="oh-timeoff-modal__stat-title"
    			>{% trans "Returned Date" %}
    		</span>
    		<span class="oh-timeoff-modal__stat-count dateformat_changer"
    			>{{asset_assignment.return_date}}</span
    		>
    	</div>
    </div>
    <div class="oh-timeoff-modal__stats-container mt-3">
    	<div class="oh-timeoff-modal__stat">
    		<span class="oh-timeoff-modal__stat-title">{% trans "Asset" %}</span>
    		<span class="oh-timeoff-modal__stat-count"
    			>{{asset_assignment.asset_id}}</span
    		>
    	</div>
        <div class="oh-timeoff-modal__stat">
    		<span class="oh-timeoff-modal__stat-title"
    			>{% trans "Return Description" %}</span
    		>
    		<div class="oh-timeoff-modal__stat-description">
    			{{asset_assignment.return_condition}}
    		</div>
    	</div>
    </div>

    <div class="oh-timeoff-modal__stats-container mt-3 ">
    	<div class="oh-timeoff-modal__stat">
    		<span class="oh-timeoff-modal__stat-title">{% trans "Assign Condition Images" %}</span>
    		<span class="oh-timeoff-modal__stat-count"
    			>
                <div class="d-flex mt-2 mb-2">
                    {% for doc in asset_assignment.assign_images.all %}
                        <a
                            href="{{doc.image.url}}"
                            rel="noopener noreferrer"
                            target="_blank"
                            ><span
                                class="oh-file-icon oh-file-icon--pdf"
                                onmouseover="enlargeImage('{{doc.image.url}}')"
                                style="width:40px;height:40px"
                            ></span
                        ></a>
                    {% endfor %}
                </div>
            </span>
    	</div>
        <div class="oh-timeoff-modal__stat">
    		<span class="oh-timeoff-modal__stat-title"
    			>{% trans "Return Condition Images" %}</span
    		>
    		<div class="oh-timeoff-modal__stat-description">
    			<div class="d-flex ">
                    {% for doc in asset_assignment.return_images.all %}
                        <a
                            href="{{doc.image.url}}"
                            rel="noopener noreferrer"
                            target="_blank"
                            ><span
                                class="oh-file-icon oh-file-icon--pdf"
                                onmouseover="enlargeImage('{{doc.image.url}}')"
                                style="width:40px;height:40px"
                            ></span
                        ></a>
                    {% endfor %}
                </div>

    		</div>
    	</div>
    </div>
    </div>
    <div class="m-3 " id="enlargeImageContainer"></div>
</div>
</div>
