{% extends 'settings.html' %}
{% load i18n %}
{% block settings %}{% load static %}

<!-- ******************* HELPDESK TAGS ******************* -->

<div class="oh-inner-sidebar-content">
    {% if perms.base.view_tags %}
        <div class="oh-inner-sidebar-content__header d-flex justify-content-between align-items-center">
            <h2 class="oh-inner-sidebar-content__title">{% trans "Helpdesk Tags" %}</h2>
			{% if perms.base.add_tags %}
				<button
					class="oh-btn oh-btn--secondary oh-btn--shadow"
					data-toggle="oh-modal-toggle"
					data-target="#tagModal"
					hx-get="{% url 'tag-create' %}"
					hx-target="#tagForm"
				>
					<ion-icon name="add-outline" class="me-1"></ion-icon>
					{% trans "Create" %}
				</button>
			{% endif %}
        </div>
		{% if tags %}
        	{% include 'base/tags/tags_view.html' %}
		{% else %}
			<div style="display: flex; flex-direction: column; justify-content: center; align-items: center; height: 100%;">
				<img style="display: block; width: 15%; margin: 20px auto; filter: opacity(0.5);" src="{% static 'images/ui/price-tag.png' %}" class="" alt="Page not found. 404." />
				<h5 class="oh-404__subtitle">{% trans "There is no helpdesk tags at this moment." %}</h5>
			</div>
		{% endif %}
    {% endif %}

</div>


<!-- start of create modal -->
<div
	class="oh-modal"
	id="tagModal"
	role="dialog"
	aria-labelledby="tagModal"
	aria-hidden="true"
>
	<div class="oh-modal__dialog">
		<div class="oh-modal__dialog-header">
			<h2 class="oh-modal__dialog-title" id="createModalTitle">
				{% trans "Create Helpdesk Tag" %}
			</h2>
			<button class="oh-modal__close" aria-label="Close">
				<ion-icon name="close-outline"></ion-icon>
			</button>
		</div>
		<div class="oh-modal__dialog-body" id="tagForm"></div>
	</div>
</div>
<!-- end of create modal -->
<!-- start of edit modal -->
<div
	class="oh-modal"
	id="tagEditModal"
	role="dialog"
	aria-labelledby="tagEditModal"
	aria-hidden="true"
>
	<div class="oh-modal__dialog">
		<div class="oh-modal__dialog-header">
			<h2 class="oh-modal__dialog-title" id="editModaltitle">
				{% trans "Update Helpdesk Tag" %}
			</h2>
			<button class="oh-modal__close" aria-label="Close">
				<ion-icon name="close-outline"></ion-icon>
			</button>
		</div>
		<div class="oh-modal__dialog-body" id="tagEditForm"></div>
	</div>
</div>
<!-- end of edit modal -->



{% endblock settings %}
