#!/usr/bin/env python
"""
AI Assistant Test Runner

This script runs comprehensive tests on the AI assistant and generates
detailed reports with accuracy metrics and recommendations.
"""

import os
import sys
import django
import json
from datetime import datetime

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'eaglora.settings')
django.setup()

from ai_assistant.test_scenarios import AIAssistantTestSuite


def generate_html_report(results: dict) -> str:
    """Generate HTML report from test results."""
    
    html = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>AI Assistant Test Report</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            .header {{ background: #2c3e50; color: white; padding: 20px; border-radius: 5px; }}
            .summary {{ background: #ecf0f1; padding: 15px; margin: 20px 0; border-radius: 5px; }}
            .passed {{ color: #27ae60; font-weight: bold; }}
            .failed {{ color: #e74c3c; font-weight: bold; }}
            .test-result {{ margin: 10px 0; padding: 10px; border-left: 4px solid #bdc3c7; }}
            .test-passed {{ border-left-color: #27ae60; background: #d5f4e6; }}
            .test-failed {{ border-left-color: #e74c3c; background: #fadbd8; }}
            .confusion-matrix {{ margin: 20px 0; }}
            .matrix-table {{ border-collapse: collapse; width: 100%; }}
            .matrix-table th, .matrix-table td {{ border: 1px solid #ddd; padding: 8px; text-align: center; }}
            .matrix-table th {{ background: #f2f2f2; }}
            .recommendations {{ background: #fff3cd; padding: 15px; border-radius: 5px; }}
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🤖 AI Assistant Test Report</h1>
            <p>Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>
        
        <div class="summary">
            <h2>📊 Test Summary</h2>
            <p><strong>Total Tests:</strong> {results['total_tests']}</p>
            <p><strong>Passed:</strong> <span class="passed">{results['passed']}</span></p>
            <p><strong>Failed:</strong> <span class="failed">{results['failed']}</span></p>
            <p><strong>Accuracy:</strong> <span class="{'passed' if results['accuracy'] >= 95 else 'failed'}">{results['accuracy']:.1f}%</span></p>
        </div>
        
        <div class="confusion-matrix">
            <h2>🎯 Confusion Matrix</h2>
            <table class="matrix-table">
                <tr>
                    <th>Expected \\ Actual</th>
    """
    
    # Add confusion matrix
    confusion_matrix = results['confusion_matrix']
    all_intents = set()
    for expected, actuals in confusion_matrix.items():
        all_intents.add(expected)
        all_intents.update(actuals.keys())
    
    all_intents = sorted(all_intents)
    
    # Header row
    for intent in all_intents:
        html += f"<th>{intent}</th>"
    html += "</tr>"
    
    # Data rows
    for expected in all_intents:
        html += f"<tr><th>{expected}</th>"
        for actual in all_intents:
            count = confusion_matrix.get(expected, {}).get(actual, 0)
            html += f"<td>{count}</td>"
        html += "</tr>"
    
    html += """
            </table>
        </div>
        
        <div class="recommendations">
            <h2>💡 Recommendations</h2>
            <ul>
    """
    
    for rec in results['recommendations']:
        html += f"<li>{rec}</li>"
    
    html += """
            </ul>
        </div>
        
        <div>
            <h2>❌ Failed Test Cases</h2>
    """
    
    for failed in results['failed_scenarios'][:20]:  # Show first 20 failed cases
        html += f"""
        <div class="test-result test-failed">
            <strong>Input:</strong> "{failed['input']}"<br>
            <strong>Category:</strong> {failed['category']}<br>
            <strong>Expected:</strong> {json.dumps(failed['expected'], indent=2)}<br>
            <strong>Actual:</strong> {json.dumps(failed['actual'], indent=2)}<br>
            <strong>Failure Reason:</strong> {failed['failure_reason']}
        </div>
        """
    
    html += """
        </div>
    </body>
    </html>
    """
    
    return html


def generate_json_report(results: dict) -> str:
    """Generate JSON report from test results."""
    
    # Create a clean JSON report
    json_report = {
        'test_summary': {
            'total_tests': results['total_tests'],
            'passed': results['passed'],
            'failed': results['failed'],
            'accuracy': results['accuracy'],
            'timestamp': datetime.now().isoformat()
        },
        'confusion_matrix': results['confusion_matrix'],
        'recommendations': results['recommendations'],
        'failed_scenarios': results['failed_scenarios'][:50],  # First 50 failed cases
        'accuracy_by_category': {},
        'accuracy_by_intent': {}
    }
    
    # Calculate accuracy by category
    category_stats = {}
    for test_result in results['test_results']:
        category = test_result['category']
        if category not in category_stats:
            category_stats[category] = {'total': 0, 'passed': 0}
        
        category_stats[category]['total'] += 1
        if test_result['passed']:
            category_stats[category]['passed'] += 1
    
    for category, stats in category_stats.items():
        accuracy = (stats['passed'] / stats['total']) * 100 if stats['total'] > 0 else 0
        json_report['accuracy_by_category'][category] = {
            'accuracy': accuracy,
            'passed': stats['passed'],
            'total': stats['total']
        }
    
    # Calculate accuracy by intent
    intent_stats = {}
    for test_result in results['test_results']:
        expected_intent = test_result['expected'].get('intent', 'unknown')
        if expected_intent not in intent_stats:
            intent_stats[expected_intent] = {'total': 0, 'passed': 0}
        
        intent_stats[expected_intent]['total'] += 1
        if test_result['passed']:
            intent_stats[expected_intent]['passed'] += 1
    
    for intent, stats in intent_stats.items():
        accuracy = (stats['passed'] / stats['total']) * 100 if stats['total'] > 0 else 0
        json_report['accuracy_by_intent'][intent] = {
            'accuracy': accuracy,
            'passed': stats['passed'],
            'total': stats['total']
        }
    
    return json.dumps(json_report, indent=2)


def main():
    """Run the AI assistant test suite."""
    
    print("🚀 Starting AI Assistant Comprehensive Test Suite")
    print("=" * 60)
    
    # Initialize test suite
    test_suite = AIAssistantTestSuite()
    
    # Run all tests
    results = test_suite.run_all_tests()
    
    # Print summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    print(f"Total Tests: {results['total_tests']}")
    print(f"Passed: {results['passed']} ✅")
    print(f"Failed: {results['failed']} ❌")
    print(f"Accuracy: {results['accuracy']:.1f}%")
    
    if results['accuracy'] >= 95:
        print("🎉 EXCELLENT! Target accuracy achieved!")
    elif results['accuracy'] >= 90:
        print("👍 GOOD! Close to target accuracy.")
    else:
        print("⚠️  NEEDS IMPROVEMENT! Below target accuracy.")
    
    # Generate reports
    print("\n📄 Generating reports...")
    
    # HTML Report
    html_report = generate_html_report(results)
    with open('ai_assistant_test_report.html', 'w', encoding='utf-8') as f:
        f.write(html_report)
    print("   ✅ HTML report saved: ai_assistant_test_report.html")
    
    # JSON Report
    json_report = generate_json_report(results)
    with open('ai_assistant_test_report.json', 'w', encoding='utf-8') as f:
        f.write(json_report)
    print("   ✅ JSON report saved: ai_assistant_test_report.json")
    
    # Print top recommendations
    print("\n💡 TOP RECOMMENDATIONS:")
    for i, rec in enumerate(results['recommendations'][:5], 1):
        print(f"   {i}. {rec}")
    
    # Print some failed examples
    if results['failed_scenarios']:
        print(f"\n❌ SAMPLE FAILED CASES ({len(results['failed_scenarios'])} total):")
        for i, failed in enumerate(results['failed_scenarios'][:3], 1):
            print(f"   {i}. Input: '{failed['input']}'")
            print(f"      Reason: {failed['failure_reason']}")
    
    print("\n🎯 NEXT STEPS:")
    if results['accuracy'] < 95:
        print("   1. Review failed test cases in the HTML report")
        print("   2. Improve intent patterns based on recommendations")
        print("   3. Enhance URL mappings for missing routes")
        print("   4. Re-run tests until 95%+ accuracy is achieved")
    else:
        print("   1. AI Assistant is ready for production!")
        print("   2. Consider adding more edge case tests")
        print("   3. Monitor real-world usage for further improvements")
    
    print(f"\n📊 Open ai_assistant_test_report.html to view detailed results")


if __name__ == "__main__":
    main()
