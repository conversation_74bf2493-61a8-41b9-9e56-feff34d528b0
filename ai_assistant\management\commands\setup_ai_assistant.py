"""
Management command to set up the AI Assistant
"""

from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
import subprocess
import sys
import os

from ai_assistant.models import AIAssistantSettings


class Command(BaseCommand):
    help = 'Set up the AI Assistant with required dependencies and configuration'

    def add_arguments(self, parser):
        parser.add_argument(
            '--skip-ollama',
            action='store_true',
            help='Skip Ollama installation check',
        )
        parser.add_argument(
            '--model',
            type=str,
            default='mistral',
            help='Ollama model to use (default: mistral)',
        )
        parser.add_argument(
            '--ollama-url',
            type=str,
            default='http://localhost:11434',
            help='Ollama base URL (default: http://localhost:11434)',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('Setting up AI Assistant...')
        )

        # Check if AI assistant is already configured
        if AIAssistantSettings.objects.exists():
            self.stdout.write(
                self.style.WARNING('AI Assistant is already configured.')
            )
            if not self.confirm('Do you want to reconfigure?'):
                return

        # Step 1: Check Python dependencies
        self.check_python_dependencies()

        # Step 2: Check Ollama installation
        if not options['skip_ollama']:
            self.check_ollama_installation(options['model'])

        # Step 3: Create AI Assistant settings
        self.create_ai_settings(options)

        # Step 4: Run migrations
        self.run_migrations()

        self.stdout.write(
            self.style.SUCCESS('AI Assistant setup completed successfully!')
        )
        self.print_next_steps()

    def check_python_dependencies(self):
        """Check if required Python packages are installed."""
        self.stdout.write('Checking Python dependencies...')
        
        required_packages = [
            'langchain',
            'ollama',
        ]
        
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package)
                self.stdout.write(f'  ✓ {package} is installed')
            except ImportError:
                missing_packages.append(package)
                self.stdout.write(
                    self.style.WARNING(f'  ✗ {package} is not installed')
                )
        
        if missing_packages:
            self.stdout.write(
                self.style.ERROR(
                    f'Missing packages: {", ".join(missing_packages)}'
                )
            )
            self.stdout.write(
                'Please install them using: pip install -r ai_assistant/requirements.txt'
            )
            if not self.confirm('Continue without these packages?'):
                raise CommandError('Required packages not installed')

    def check_ollama_installation(self, model):
        """Check if Ollama is installed and the model is available."""
        self.stdout.write('Checking Ollama installation...')
        
        # Check if ollama command is available
        try:
            result = subprocess.run(
                ['ollama', '--version'],
                capture_output=True,
                text=True,
                check=True
            )
            self.stdout.write(f'  ✓ Ollama is installed: {result.stdout.strip()}')
        except (subprocess.CalledProcessError, FileNotFoundError):
            self.stdout.write(
                self.style.ERROR('  ✗ Ollama is not installed or not in PATH')
            )
            self.stdout.write(
                'Please install Ollama from: https://ollama.ai/'
            )
            if not self.confirm('Continue without Ollama?'):
                raise CommandError('Ollama is required for AI functionality')
            return

        # Check if the model is available
        try:
            result = subprocess.run(
                ['ollama', 'list'],
                capture_output=True,
                text=True,
                check=True
            )
            
            if model in result.stdout:
                self.stdout.write(f'  ✓ Model {model} is available')
            else:
                self.stdout.write(
                    self.style.WARNING(f'  ✗ Model {model} is not available')
                )
                if self.confirm(f'Do you want to pull the {model} model?'):
                    self.pull_ollama_model(model)
                else:
                    self.stdout.write(
                        self.style.WARNING(
                            f'You can pull the model later with: ollama pull {model}'
                        )
                    )
        except subprocess.CalledProcessError:
            self.stdout.write(
                self.style.WARNING('Could not check available models')
            )

    def pull_ollama_model(self, model):
        """Pull the specified Ollama model."""
        self.stdout.write(f'Pulling Ollama model: {model}...')
        
        try:
            subprocess.run(
                ['ollama', 'pull', model],
                check=True
            )
            self.stdout.write(
                self.style.SUCCESS(f'  ✓ Model {model} pulled successfully')
            )
        except subprocess.CalledProcessError:
            self.stdout.write(
                self.style.ERROR(f'  ✗ Failed to pull model {model}')
            )

    def create_ai_settings(self, options):
        """Create AI Assistant settings."""
        self.stdout.write('Creating AI Assistant settings...')
        
        # Delete existing settings
        AIAssistantSettings.objects.all().delete()
        
        # Create new settings
        settings = AIAssistantSettings.objects.create(
            ollama_model=options['model'],
            ollama_base_url=options['ollama_url'],
            temperature=0.7,
            max_tokens=1000,
            enable_security_validation=True,
            max_chat_history=100,
            enable_langchain=True,
            enable_memory=True,
            is_active=True
        )
        
        self.stdout.write(
            self.style.SUCCESS(f'  ✓ AI Assistant settings created with model: {settings.ollama_model}')
        )

    def run_migrations(self):
        """Run database migrations for AI Assistant."""
        self.stdout.write('Running database migrations...')
        
        try:
            from django.core.management import call_command
            call_command('makemigrations', 'ai_assistant', verbosity=0)
            call_command('migrate', 'ai_assistant', verbosity=0)
            self.stdout.write('  ✓ Database migrations completed')
        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f'  ⚠ Migration warning: {e}')
            )

    def confirm(self, message):
        """Ask for user confirmation."""
        response = input(f'{message} (y/N): ').lower().strip()
        return response in ['y', 'yes']

    def print_next_steps(self):
        """Print next steps for the user."""
        self.stdout.write('\n' + '='*50)
        self.stdout.write(self.style.SUCCESS('NEXT STEPS:'))
        self.stdout.write('='*50)
        
        self.stdout.write('1. Add the AI Assistant to your templates:')
        self.stdout.write('   {% load ai_assistant_tags %}')
        self.stdout.write('   {% ai_chatbox %}')
        
        self.stdout.write('\n2. Include AI Assistant URLs in your main urls.py:')
        self.stdout.write('   path("ai/", include("ai_assistant.urls")),')
        
        self.stdout.write('\n3. Test the AI Assistant:')
        self.stdout.write('   - Visit your website')
        self.stdout.write('   - Click the floating AI button')
        self.stdout.write('   - Try commands like "Show all employees"')
        
        self.stdout.write('\n4. Configure Ollama models (if needed):')
        self.stdout.write('   ollama pull mistral')
        self.stdout.write('   ollama pull llama3')
        
        self.stdout.write('\n5. Check the admin panel for AI Assistant settings:')
        self.stdout.write('   /admin/ai_assistant/aiassistantsettings/')
        
        self.stdout.write('\n' + '='*50)
