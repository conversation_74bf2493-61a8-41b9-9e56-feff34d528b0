
{% load i18n %}
{% load basefilters %}
<section class="oh-wrapper oh-main__topbar" x-data="{searchShow: false}">
  <div class="oh-main__titlebar oh-main__titlebar--left">
    <h1 class="oh-main__titlebar-title fw-bold">
        <a href="{% url 'attendance-overtime-view' %}" class='text-dark'>
            {% trans "Hour Account" %}
        </a>
      </h1>
    <a
      class="oh-main__titlebar-search-toggle"
      role="button"
      aria-label="Toggle Search"
      @click="searchShow = !searchShow"
    >
      <ion-icon
        name="search-outline"
        class="oh-main__titlebar-serach-icon"
      ></ion-icon>
    </a>
  </div>
  <form
    hx-get='{% url "attendance-ot-search" %}'
    hx-swap="innerHTML"
    hx-target="#ot-table"
    id="filterForm"
    class="d-flex"
    onsubmit = event.preventDefault()
  >
    <div class="oh-main__titlebar oh-main__titlebar--right">
      <div
        class="oh-input-group oh-input__search-group"
        :class="searchShow ? 'oh-input__search-group--show' : ''"
      >
        <ion-icon
          name="search-outline"
          class="oh-input-group__icon oh-input-group__icon--left"
        ></ion-icon>
        <input
          type="text"
          class="oh-input oh-input__icon"
          aria-label="Search Input"
          id="attendance-search"
          name='search'
          placeholder="{% trans 'Search' %}"
          onkeyup="$('.filterButton')[0].click()"
        />
      </div>
      <div class="oh-main__titlebar-button-container">
        <div class="oh-dropdown" x-data="{open: false}">
          <button class="oh-btn ml-2" @click="open = !open" onclick="event.preventDefault()">
            <ion-icon name="filter" class="mr-1"></ion-icon>{% trans "Filter" %}<div id="filterCount"></div>
          </button>
          <div
            class="oh-dropdown__menu oh-dropdown__menu--right oh-dropdown__filter p-4"
            x-show="open"
            @click.outside="open = false"
            style="display: none;
                  width: 550px;"
          >
            {% include 'attendance/attendance_account/attendance_account_filter.html' %}
          </div>
        </div>
        <div class="oh-dropdown" x-data="{open: false}">
          <button class="oh-btn ml-2" @click="open = !open" onclick="event.preventDefault()">
            <ion-icon name="library-outline" class="mr-1"></ion-icon>{% trans "Group By" %}
            <div id="filterCount"></div>
          </button>
          <div
          class="oh-dropdown__menu oh-dropdown__menu--right oh-dropdown__filter p-4"
          x-show="open"
          @click.outside="open = false"
          style="display: none"
          >
          <div class="oh-accordion">
            <label for="id_field">{% trans "Group By" %}</label>
              <div class="row">
                <div class="col-sm-12 col-md-12 col-lg-6">
                  <div class="oh-input-group">
                    <label class="oh-label" for="id_field">{% trans "Field" %}</label>
                  </div>
                </div>
                <div class="col-sm-12 col-md-12 col-lg-6">
                  <div class="oh-input-group">
                    <select
                      class="oh-select mt-1 w-100"
                      id="id_field"
                      name="field"
                      class="select2-selection select2-selection--single"
                    >
                      {% for field in gp_fields %}
                      <option value="{{ field.0 }}">{% trans field.1 %}</option>
                      {% endfor %}
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        </form>
        {% if perms.attendance.add_attendanceovertime or request.user|is_reportingmanager or perms.attendance.delete_attendanceovertime %}
          <div class="oh-dropdown ml-2" x-data="{open: false}">
            <button
              class="oh-btn oh-btn--dropdown"
              @click="open = !open"
              @click.outside="open = false"
              onclick="event.preventDefault()"
            >
              {% trans "Actions" %}
            </button>
            <div
                class="oh-dropdown__menu oh-dropdown__menu--right"
                x-show="open"
                style="display: none"
              >
                <ul class="oh-dropdown__items">
                  {% if perms.attendance.add_attendanceovertime or request.user|is_reportingmanager %}
                    <li class="oh-dropdown__item">
                      <a
                        href="#"
                        class="oh-dropdown__link"
                        id="hour-account-export"
                        data-toggle="oh-modal-toggle"
                        data-target="#objectCreateModal"
                        hx-get="{% url 'attendance-account-info-export' %}"
                        hx-target="#objectCreateModalTarget"
                        >{% trans "Export" %}</a
                      >
                    </li>
                  {% endif %}
                  {% if perms.attendance.delete_attendanceovertime %}
                    <li class="oh-dropdown__item">
                      <a
                        href="#"
                        id="hourAccountbulkDelete"
                        data-action="delete"
                        class="oh-dropdown__link oh-dropdown__link--danger"
                        >{% trans "Delete" %}</a
                      >
                    </li>
                  {% endif %}
                </ul>
              </div>
            </div>
          </div>
        {% endif %}
        {% if perms.attendance.add_attendanceovertime or request.user|is_reportingmanager %}
          <div class="oh-btn-group ml-2">
            <div class="oh-dropdown" >
              <button class="oh-btn oh-btn--secondary"
                      data-toggle="oh-modal-toggle"
                      data-target="#objectCreateModal"
                      hx-get="{% url 'attendance-overtime-create' %}"
                      hx-target="#objectCreateModalTarget"
                >
                <ion-icon name="add-sharp" class="mr-2"></ion-icon>
                {% trans "Create" %}
              </button>
            </div>
          </div>
        {% endif %}
      </div>
    </div>
  </form>
  <script>
    $('#attendance-search').keydown(function (e) {
      var val = $(this).val();
      $('.pg').attr('hx-vals', `{"search":${val}}`);
    });
    $(document).ready(function(){
      $('#id_field').on('change',function(){
        $('.filterButton')[0].click();
      })
    });
  </script>
</section>
