{% load i18n %}{% load widget_tweaks %} {% load eaglorafilters %}
{% for field in form %}
    {% if field.field.widget.is_hidden %}
        {{ field }}
    {% endif %}
{% endfor %}
<div class="oh-general__tab-target oh-profile-section" id="personal">
    <div class="oh-profile-section__card">
        <div class="row">
            <div class="col-12">{{form.non_field_errors}}</div>
            {% for field in form.visible_fields %}
                {% if field.field.widget|is_select_multiple or field.field.widget|is_text_area %}
                    <div class="oh-label__info" for="id_{{ field.name }}">
                        <label class="oh-label {% if field.field.required %} required-star{% endif %}"
                            for="id_{{ field.name }}">{% trans field.label %}</label>
                        {% if field.help_text != '' %}
                        <span class="oh-info mr-2" title="{{ field.help_text|safe }}"></span>
                        {% endif %}
                    </div>
                    <div style="width: 100%; padding: 12px;" id="id_{{ field.name }}_parent_div">
                        {{ field|add_class:"form-control" }}
                    </div>
                    {{field.errors}}
                {% else %}
                    <div class="col-12 col-md-6" id="id_{{ field.name }}_parent_div">
                        <div class="oh-label__info" for="id_{{ field.name }}">
                            <label class="oh-label {% if field.field.required %} required-star{% endif %}"
                                for="id_{{ field.name }}">{% trans field.label %}</label>
                            {% if field.help_text != '' %}
                                <span class="oh-info mr-2" title="{{ field.help_text|safe }}"></span>
                            {% endif %}
                        </div>
                        {% if field.field.widget.input_type == "checkbox" %}
                            <div class="oh-switch" style="width: 30px">
                                {{ field|add_class:"oh-switch__checkbox" }}
                            </div>
                        {% else %}
                            {{ field|add_class:"form-control" }}
                        {% endif %}
                        {{field.errors}}
                    </div>
                {% endif %}
            {% endfor %}
        </div>

        <div class="oh-modal__dialog-footer p-0 mt-3">
            <button type="submit" class="oh-btn oh-btn--secondary oh-btn--shadow">
                {% trans "Save" %}
            </button>
        </div>
    </div>
</div>
