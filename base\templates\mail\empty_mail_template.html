{% extends 'index.html' %}
{% load static i18n %}
{% block content %}

<section class="oh-wrapper oh-main__topbar">
    <div
        class="oh-main__titlebar oh-main__titlebar--left oh-d-flex-column--resp oh-mb-3--small"
    >
        <h1 class="oh-main__titlebar-title fw-bold">
            {% trans "Mail Templates" %}
        </h1>
    </div>
    {% if perms.base.add_eagloramailtemplate %}
    <div
        class="oh-main__titlebar oh-main__titlebar--right oh-d-flex-column--resp oh-mb-3--small"
    >
        <a
            hx-get="{% url 'create-mail-template' %}"
            data-toggle="oh-modal-toggle"
            data-target="#objectCreateModal"
            hx-target="#objectCreateModalTarget"
            class="oh-btn oh-btn--secondary ml-2"
            ><ion-icon
                name="add"
                class="mr-1 md hydrated"
                role="img"
                aria-label="add"
            ></ion-icon
            >{% trans "Create" %}</a
        >
    </div>
    {% endif %}
</section>
<main :class="sidebarOpen ? 'oh-main__sidebar-visible' : ''">
    <div class="oh-wrapper">
        <div class="oh-404">
            <img
                style="width: 150px; height: 150px"
                src="{% static 'images/ui/email.png' %}"
                class="oh-404__image mb-4"
                alt="Page not found. 404."
            />
            <h5 class="oh-404__subtitle">
                {% trans "There are currently no email templates." %}
            </h5>
        </div>
    </div>
</main>

{% endblock content %}
