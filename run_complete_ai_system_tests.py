#!/usr/bin/env python
"""
Complete AI System Test Runner

This script runs comprehensive tests for the entire AI assistant system
including natural language processing, API conversion, and execution.
"""

import os
import sys
import django
import json
import time
from datetime import datetime

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'eaglora.settings')
django.setup()

from ai_assistant.natural_language_api_converter import NaturalLanguageAPIConverter
from ai_assistant.api_request_executor import APIRequestExecutor
from ai_assistant.api_metadata_extractor import APIMetadataExtractor
from ai_assistant.test_nl_api_converter import run_comprehensive_tests


def test_natural_language_converter():
    """Test the natural language converter with various inputs."""
    
    print("🧪 Testing Natural Language API Converter...")
    print("-" * 50)
    
    converter = NaturalLanguageAPIConverter()
    
    test_cases = [
        "Show all employees",
        "List employees from HR department",
        "Create employee <PERSON> <NAME_EMAIL>",
        "Apply leave for <PERSON> from July 1 to July 5",
        "Export attendance data",
        "Find employees with highest salary",
        "Show leave requests sorted by date",
        "Mark attendance for <PERSON> today",
        "Generate payroll report for June",
        "List pending leave applications"
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n[{i}/{len(test_cases)}] Testing: '{test_case}'")
        
        try:
            start_time = time.time()
            api_request = converter.convert_to_api_request(test_case)
            execution_time = time.time() - start_time
            
            print(f"  ✅ Success!")
            print(f"  🎯 URL: {api_request.get('url', 'N/A')}")
            print(f"  📡 Method: {api_request.get('method', 'N/A')}")
            print(f"  🎯 Confidence: {api_request.get('confidence', 0):.2f}")
            print(f"  ⏱️  Time: {execution_time:.3f}s")
            
            results.append({
                'input': test_case,
                'success': True,
                'api_request': api_request,
                'execution_time': execution_time
            })
            
        except Exception as e:
            print(f"  ❌ Failed: {str(e)}")
            results.append({
                'input': test_case,
                'success': False,
                'error': str(e),
                'execution_time': 0
            })
    
    # Generate summary
    successful = sum(1 for r in results if r['success'])
    high_confidence = sum(1 for r in results if r.get('api_request', {}).get('confidence', 0) >= 0.8)
    avg_time = sum(r['execution_time'] for r in results) / len(results)
    
    print(f"\n📊 Natural Language Converter Summary:")
    print(f"  Total tests: {len(test_cases)}")
    print(f"  Successful: {successful}")
    print(f"  High confidence (≥0.8): {high_confidence}")
    print(f"  Success rate: {(successful / len(test_cases)) * 100:.1f}%")
    print(f"  Average time: {avg_time:.3f}s")
    
    return results


def test_api_metadata_extractor():
    """Test the API metadata extractor."""
    
    print("\n🧪 Testing API Metadata Extractor...")
    print("-" * 50)
    
    try:
        extractor = APIMetadataExtractor()
        metadata = extractor.extract_all_metadata()
        
        print(f"  ✅ Successfully extracted metadata!")
        print(f"  📊 Total endpoints: {len(metadata['endpoints'])}")
        print(f"  📊 Total models: {len(metadata['models'])}")
        print(f"  📊 Field mappings: {len(metadata['field_mappings'])}")
        print(f"  📊 Intent mappings: {len(metadata['intent_mappings'])}")
        
        # Show some example endpoints
        print(f"\n  📋 Sample endpoints:")
        for i, (url, info) in enumerate(list(metadata['endpoints'].items())[:5], 1):
            print(f"    {i}. {url} ({info.get('view_type', 'unknown')})")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Failed: {str(e)}")
        return False


def test_api_executor():
    """Test the API request executor."""
    
    print("\n🧪 Testing API Request Executor...")
    print("-" * 50)
    
    executor = APIRequestExecutor()
    
    # Test with a simple GET request
    test_request = {
        'url': '/api/employee/',
        'method': 'GET',
        'filters': {},
        'confidence': 0.9
    }
    
    try:
        print(f"  Testing request: {test_request['method']} {test_request['url']}")
        
        start_time = time.time()
        result = executor.execute_request(test_request)
        execution_time = time.time() - start_time
        
        print(f"  ✅ Request executed!")
        print(f"  📊 Success: {result.get('success', False)}")
        print(f"  📊 Status: {result.get('status_code', 'N/A')}")
        print(f"  ⏱️  Time: {execution_time:.3f}s")
        
        if result.get('summary'):
            print(f"  📝 Summary: {result['summary']}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Failed: {str(e)}")
        return False


def test_end_to_end_workflow():
    """Test the complete end-to-end workflow."""
    
    print("\n🧪 Testing End-to-End Workflow...")
    print("-" * 50)
    
    converter = NaturalLanguageAPIConverter()
    executor = APIRequestExecutor()
    
    test_workflows = [
        "Show all employees",
        "List employees from HR department",
        "Export employee data"
    ]
    
    results = []
    
    for i, workflow in enumerate(test_workflows, 1):
        print(f"\n[{i}/{len(test_workflows)}] Testing workflow: '{workflow}'")
        
        try:
            # Step 1: Convert natural language to API request
            print("  🔄 Step 1: Converting natural language...")
            api_request = converter.convert_to_api_request(workflow)
            print(f"    ✅ Converted with confidence: {api_request.get('confidence', 0):.2f}")
            
            # Step 2: Execute API request (if confidence is high enough)
            if api_request.get('confidence', 0) >= 0.7:
                print("  🔄 Step 2: Executing API request...")
                execution_result = executor.execute_request(api_request)
                print(f"    ✅ Executed with success: {execution_result.get('success', False)}")
                
                results.append({
                    'workflow': workflow,
                    'success': True,
                    'api_request': api_request,
                    'execution_result': execution_result
                })
            else:
                print(f"    ⚠️  Skipping execution due to low confidence")
                results.append({
                    'workflow': workflow,
                    'success': False,
                    'reason': 'Low confidence'
                })
                
        except Exception as e:
            print(f"    ❌ Failed: {str(e)}")
            results.append({
                'workflow': workflow,
                'success': False,
                'error': str(e)
            })
    
    # Generate summary
    successful = sum(1 for r in results if r['success'])
    
    print(f"\n📊 End-to-End Workflow Summary:")
    print(f"  Total workflows: {len(test_workflows)}")
    print(f"  Successful: {successful}")
    print(f"  Success rate: {(successful / len(test_workflows)) * 100:.1f}%")
    
    return results


def generate_comprehensive_report(test_results):
    """Generate a comprehensive test report."""
    
    report = {
        'test_execution': {
            'timestamp': datetime.now().isoformat(),
            'total_duration': 0,
            'environment': 'development'
        },
        'results': test_results,
        'summary': {
            'total_tests': 0,
            'successful_tests': 0,
            'failed_tests': 0,
            'overall_success_rate': 0
        },
        'recommendations': []
    }
    
    # Calculate summary statistics
    for category, results in test_results.items():
        if isinstance(results, list):
            total = len(results)
            successful = sum(1 for r in results if r.get('success', False))
            
            report['summary']['total_tests'] += total
            report['summary']['successful_tests'] += successful
            report['summary']['failed_tests'] += (total - successful)
    
    if report['summary']['total_tests'] > 0:
        report['summary']['overall_success_rate'] = (
            report['summary']['successful_tests'] / report['summary']['total_tests']
        ) * 100
    
    # Generate recommendations
    if report['summary']['overall_success_rate'] >= 95:
        report['recommendations'].append("🎉 Excellent! System is ready for production.")
    elif report['summary']['overall_success_rate'] >= 85:
        report['recommendations'].append("👍 Good performance. Consider minor optimizations.")
    else:
        report['recommendations'].append("⚠️  System needs improvement before production deployment.")
    
    # Save report
    with open('complete_ai_system_test_report.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    return report


def main():
    """Run all comprehensive tests."""
    
    print("🚀 Starting Complete AI System Tests")
    print("=" * 70)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    start_time = time.time()
    
    # Run all test categories
    test_results = {}
    
    # 1. Natural Language Converter Tests
    test_results['natural_language_converter'] = test_natural_language_converter()
    
    # 2. API Metadata Extractor Tests
    test_results['api_metadata_extractor'] = test_api_metadata_extractor()
    
    # 3. API Executor Tests
    test_results['api_executor'] = test_api_executor()
    
    # 4. End-to-End Workflow Tests
    test_results['end_to_end_workflows'] = test_end_to_end_workflow()
    
    # 5. Comprehensive Test Suite
    print("\n🧪 Running Comprehensive Test Suite...")
    print("-" * 50)
    try:
        comprehensive_success = run_comprehensive_tests()
        test_results['comprehensive_suite'] = {
            'success': comprehensive_success,
            'message': 'Comprehensive test suite completed'
        }
        print(f"  ✅ Comprehensive tests: {'PASSED' if comprehensive_success else 'FAILED'}")
    except Exception as e:
        test_results['comprehensive_suite'] = {
            'success': False,
            'error': str(e)
        }
        print(f"  ❌ Comprehensive tests failed: {str(e)}")
    
    total_time = time.time() - start_time
    
    # Generate comprehensive report
    report = generate_comprehensive_report(test_results)
    report['test_execution']['total_duration'] = total_time
    
    # Print final summary
    print("\n" + "=" * 70)
    print("🎯 FINAL TEST RESULTS")
    print("=" * 70)
    print(f"Total execution time: {total_time:.2f} seconds")
    print(f"Overall success rate: {report['summary']['overall_success_rate']:.1f}%")
    print(f"Total tests: {report['summary']['total_tests']}")
    print(f"Successful: {report['summary']['successful_tests']}")
    print(f"Failed: {report['summary']['failed_tests']}")
    
    print("\n💡 Recommendations:")
    for rec in report['recommendations']:
        print(f"  {rec}")
    
    print(f"\n📄 Detailed report saved: complete_ai_system_test_report.json")
    
    # Return success status
    return report['summary']['overall_success_rate'] >= 95


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
