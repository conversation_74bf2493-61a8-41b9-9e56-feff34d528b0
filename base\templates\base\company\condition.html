{% extends 'settings.html' %}
{% load i18n %}
{% block settings %}
<div class="oh-inner-sidebar-content">

    <form action="" method="post" class='settings-label mb-1'>
        {% csrf_token %}
        <div class="oh-inner-sidebar-content__header">
            <h2 class="oh-inner-sidebar-content__title">{% trans "Attendance Condition" %}</h2>
        </div>
        <div class="oh-inner-sidebar-content__body">
            <div class="oh-input-group">
                {{form}}
            </div>
        </div>
        <button type='submit' class="oh-btn oh-btn--secondary mt-2 mr-0 oh-btn--w-100-resp">
            {% trans "Save Changes" %}
        </button>
        <div class="oh-inner-sidebar-content__footer">
        </div>
        </div>
        </form>

    {% include 'base/company/condition_view.html' %}
</div>
{% endblock settings %}
