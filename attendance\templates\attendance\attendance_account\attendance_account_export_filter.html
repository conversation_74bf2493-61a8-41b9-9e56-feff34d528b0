{% load static %} {% load i18n %}
<div class="oh-modal__dialog-header pb-0">
    <h2 class="oh-modal__dialog-title" id="hourAccountExportLavel">
        {% trans "Export Hour Accounts" %}
    </h2>
    <button class="oh-modal__close" aria-label="Close">
        <ion-icon name="close-outline"></ion-icon>
    </button>
</div>
<div class="oh-modal__dialog-body" id="hourAccountExportModalBody">
    <form action="{%url 'attendance-account-info-export' %}" method="get"
        onsubmit="event.stopPropagation();$(this).parents().find('.oh-modal--show').last().toggleClass('oh-modal--show');"
        id="hourAccountExportForm" class="oh-profile-section">
        {% csrf_token %}
        <div class="oh-dropdown__filter-body">
            <div class="oh-accordion">
                <div class="oh-accordion-header">{% trans "Excel columns" %}</div>
                <div class="oh-accordion-body">
                    <div class="row">
                        <div class="col-sm-12 col-md-12 col-lg-6">
                            <div class="oh-input-group">
                                <label class="oh-label">
                                    <input type="checkbox" id="select-all-fields" /> {% trans "Select All" %}
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        {% for field in export_fields.selected_fields %}
                        <div class="col-sm-12 col-md-12 col-lg-6">
                            <div class="oh-input-group">
                                <label class="oh-label"> {{ field }} </label>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <div class="oh-accordion">
                <div class="oh-accordion-header">{% trans "Work Info" %}</div>
                <div class="oh-accordion-body">
                    <div class="row">
                        <div class="col-sm-12 col-md-12 col-lg-6">
                            <div class="oh-input-group">
                                <label class="oh-label" for={{export_obj.form.employee_id.id_for_label}}>{% trans "Employee" %}</label>
                                {{export_obj.form.employee_id}}
                            </div>
                            <div class="oh-input-group">
                                <label class="oh-label"
                                    for={{export_obj.form.employee_id__employee_work_info__department_id.id_for_label}}>{% trans "Department" %}</label>
                                {{export_obj.form.employee_id__employee_work_info__department_id}}
                            </div>
                            <div class="oh-input-group">
                                <label class="oh-label"
                                    for={{export_obj.form.employee_id__employee_work_info__shift_id.id_for_label}}>{% trans "Shift" %}</label>
                                {{export_obj.form.employee_id__employee_work_info__shift_id}}
                            </div>
                            <div class="oh-input-group">
                                <label class="oh-label"
                                    for={{export_obj.form.employee_id__employee_work_info__reporting_manager_id.id_for_label}}>{% trans "Reporting Manager" %}</label>
                                {{export_obj.form.employee_id__employee_work_info__reporting_manager_id}}
                            </div>
                        </div>
                        <div class="col-sm-12 col-md-12 col-lg-6">
                            <div class="oh-input-group">
                                <label class="oh-label"
                                    for={{export_obj.form.employee_id__employee_work_info__company_id.id_for_label}}>{% trans "Company" %}</label>
                                {{export_obj.form.employee_id__employee_work_info__company_id}}
                            </div>
                            <div class="oh-input-group">
                                <label class="oh-label"
                                    for={{export_obj.form.employee_id__employee_work_info__job_position_id.id_for_label}}>{% trans "Job Position" %}</label>
                                {{export_obj.form.employee_id__employee_work_info__job_position_id}}
                            </div>
                            <div class="oh-input-group">
                                <label class="oh-label"
                                    for={{export_obj.form.employee_id__employee_work_info__work_type_id.id_for_label}}>{% trans "Work Type" %}</label>
                                {{export_obj.form.employee_id__employee_work_info__work_type_id}}
                            </div>
                            <div class="oh-input-group">
                                <label class="oh-label"
                                    for={{export_obj.form.employee_id__employee_work_info__location.id_for_label}}>{% trans "Work Location" %}</label>
                                {{export_obj.form.employee_id__employee_work_info__location}}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="oh-accordion">
                <div class="oh-accordion-header">{% trans "Worked Hours" %}</div>
                <div class="oh-accordion-body">
                    <div class="row">
                        <div class="col-sm-12 col-md-12 col-lg-6">
                            <div class="oh-input-group">
                                <label class="oh-label" for={{export_obj.form.month.id_for_label}}>{% trans "Month" %}</label>
                                {{export_obj.form.month}}
                            </div>
                            <div class="oh-input-group">
                                <label class="oh-label" for={{export_obj.form.overtime.id_for_label}}>{% trans "Overtime" %}</label>
                                {{export_obj.form.overtime}}
                            </div>
                        </div>
                        <div class="col-sm-12 col-md-12 col-lg-6">
                            <div class="oh-input-group">
                                <label class="oh-label" for={{export_obj.form.year.id_for_label}}>{% trans "Year" %}</label>
                                {{export_obj.form.year}}
                            </div>
                            <div class="oh-input-group">
                                <label class="oh-label" for={{export_obj.form.worked_hours.id_for_label}}>{% trans "Worked Hours" %}</label>
                                {{export_obj.form.worked_hours}}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="oh-accordion">
                <div class="oh-accordion-header">{% trans "Advanced" %}</div>
                <div class="oh-accordion-body">
                    <div class="row">
                        <div class="col-sm-12 col-md-12 col-lg-6">
                            <div class="oh-input-group">
                                <label class="oh-label" for={{export_obj.form.worked_hours__gte.id_for_label}}>{% trans "Worked Hours Greater or Equal" %}</label>
                                {{export_obj.form.worked_hours__gte}}
                            </div>
                            <div class="oh-input-group">
                                <label class="oh-label" for={{export_obj.form.pending_hours__gte.id_for_label}}>{% trans "Pending Hours Greater or Equal" %}</label>
                                {{export_obj.form.pending_hours__gte}}
                            </div>
                            <div class="oh-input-group">
                                <label class="oh-label" for={{export_obj.form.overtime__gte.id_for_label}}>{% trans "OT Account Greater or Equal" %}</label>
                                {{export_obj.form.overtime__gte}}
                            </div>
                        </div>
                        <div class="col-sm-12 col-md-12 col-lg-6">
                            <div class="oh-input-group">
                                <label class="oh-label" for={{export_obj.form.worked_hours__lte.id_for_label}}>{% trans "Worked Hours Less Than or Equal" %}</label>
                                {{export_obj.form.worked_hours__lte}}
                            </div>
                            <div class="oh-input-group">
                                <label class="oh-label" for={{export_obj.form.pending_hours__lte.id_for_label}}>{% trans "Pending Hours Less Than or Equal" %}</label>
                                {{export_obj.form.pending_hours__lte}}
                            </div>
                            <div class="oh-input-group">
                                <label class="oh-label" for={{export_obj.form.overtime__lte.id_for_label}}>{% trans "OT Account Less Than or Equal" %}</label>
                                {{export_obj.form.overtime__lte}}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="oh-modal__dialog-footer p-0 pt-4">
            <button class="oh-btn oh-btn--secondary oh-btn--shadow">
              {% trans "Export" %}
            </button>
        </div>
    </form>
</div>
