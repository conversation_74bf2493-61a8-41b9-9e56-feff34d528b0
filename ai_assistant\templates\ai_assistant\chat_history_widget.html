{% load ai_assistant_tags %}

<div class="ai-chat-history-widget">
    <h6 class="widget-title">
        <i class="fas fa-history"></i>
        Recent AI Conversations
    </h6>
    
    {% if chat_history %}
        <div class="chat-history-list">
            {% for chat in chat_history %}
                <div class="chat-history-item">
                    <div class="chat-message">
                        <i class="{{ chat.intent|ai_intent_icon }} chat-icon"></i>
                        <span class="chat-text">{{ chat.message|truncatechars:50 }}</span>
                    </div>
                    <div class="chat-meta">
                        <span class="chat-time">{{ chat.created_at|timesince }} ago</span>
                        <span class="chat-confidence {{ chat.confidence|ai_confidence_color }}">
                            {{ chat.confidence|floatformat:1 }}
                        </span>
                    </div>
                </div>
            {% endfor %}
        </div>
        
        <div class="widget-footer">
            <a href="{% url 'ai_assistant:chat-history' %}" class="btn btn-sm btn-outline-primary">
                View All History
            </a>
        </div>
    {% else %}
        <div class="no-history">
            <i class="fas fa-comments text-muted"></i>
            <p class="text-muted">No conversations yet. Start chatting with the AI assistant!</p>
        </div>
    {% endif %}
</div>

<style>
.ai-chat-history-widget {
    background: #fff;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.widget-title {
    color: #374151;
    margin-bottom: 15px;
    font-weight: 600;
}

.chat-history-list {
    max-height: 300px;
    overflow-y: auto;
}

.chat-history-item {
    padding: 10px 0;
    border-bottom: 1px solid #f3f4f6;
}

.chat-history-item:last-child {
    border-bottom: none;
}

.chat-message {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 5px;
}

.chat-icon {
    color: #6b7280;
    font-size: 12px;
    width: 16px;
}

.chat-text {
    flex: 1;
    font-size: 14px;
    color: #374151;
}

.chat-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
}

.chat-time {
    color: #9ca3af;
}

.chat-confidence {
    font-weight: 600;
}

.widget-footer {
    margin-top: 15px;
    text-align: center;
}

.no-history {
    text-align: center;
    padding: 20px 0;
}

.no-history i {
    font-size: 24px;
    margin-bottom: 10px;
}
</style>
