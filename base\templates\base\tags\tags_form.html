{% load i18n %}
{% if form.errors %}
<!-- form errors  -->
    <div class="oh-wrapper">
        <div class="oh-alert-container">
            {% for error in form.non_field_errors %}
            <div class="oh-alert oh-alert--animated oh-alert--danger">
                {{ error }}
            </div>
            {% endfor %}
        </div>
    </div>
{% endif %}
<form
    {% if tag_id %}
    hx-post="{% url 'tag-update' tag_id %}"
    {% else	%}
    hx-post="{% url 'tag-create' %}"
    {% endif %}
    hx-target="#tagForm"
    method="post"
    hx-encoding="multipart/form-data">
    {% csrf_token %}
    {{form.as_p}}
</form>
