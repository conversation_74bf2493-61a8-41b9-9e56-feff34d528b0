{% load i18n %} {% load eaglorafilters %}
<div class="oh-sticky-table">
	<div class="oh-sticky-table__table oh-table--sortable">
		<div class="oh-sticky-table__thead">
			<div class="oh-sticky-table__tr">
				<div class="oh-sticky-table__th">{% trans "Title" %}</div>
				<div class="oh-sticky-table__th">{% trans "Type" %}</div>
				<div class="oh-sticky-table__th">{% trans "Login block" %}</div>
				{% if perms.employee.change_actiontype or perms.employee.delete_actiontype %}
					<div class="oh-sticky-table__th">{% trans "Actions" %}</div>
				{% endif %}
			</div>
		</div>
		<div class="oh-sticky-table__tbody">
			{% for act in action_types %}
			<div class="oh-sticky-table__tr" draggable="true" id="actionType{{act.id}}">
				<div class="oh-sticky-table__td">{{act.title}}</div>
				<div class="oh-sticky-table__td">{{act.get_action_type_display}}</div>
				<div class="oh-sticky-table__td">
					{{act.block_option|yes_no}}
				</div>
				{% if perms.employee.change_actiontype or perms.employee.delete_actiontype %}
					<div class="oh-sticky-table__td">
						<div class="oh-btn-group">
							{% if perms.employee.change_actiontype %}
								<a hx-get="{% url 'action-type-update' act.id %}" hx-target="#objectUpdateModalTarget"
									data-toggle="oh-modal-toggle" data-target="#objectUpdateModal" type="button"
									class="oh-btn oh-btn--light-bkg w-100" title="{% trans 'Edit' %}">
									<ion-icon name="create-outline"></ion-icon></a>
							{% endif %}
							{% if perms.employee.delete_actiontype %}
								<form hx-confirm="{% trans 'Are you sure you want to delete this action type?' %}"
									hx-on-htmx-after-request="reloadMessage(this);"
									hx-post="{% url 'action-type-delete' act.id %}" hx-target="#actionType{{act.id}}"
									hx-swap="outerHTML" class="w-100">
									{% csrf_token %}
									<button type="submit" class="oh-btn oh-btn--danger-outline oh-btn--light-bkg w-100"
										title="{% trans 'Remove' %}">
										<ion-icon name="trash-outline"></ion-icon>
									</button>
								</form>
							{% endif %}
						</div>
					</div>
				{% endif %}
			</div>
			{% endfor %}
		</div>
	</div>
</div>

<script>
	function actionChange(selectElement) {
		var selectedActiontype = selectElement.val();
		var parentForm = selectElement.parents().closest("form");
		$.ajax({
			type: "post",
			url: "{% url 'action-type-name' %}",
			data: {
				csrfmiddlewaretoken: getCookie("csrftoken"),
				"action_type": selectedActiontype,
			},
			success: function (response) {

				// Check if the response.action_type is "warning"
				if (response.action_type === "warning") {
					// Hide the 'block_option' field
					parentForm.find('[id=id_block_option]').parent().hide();
				} else {
					// Show the 'block_option' field
					parentForm.find('[id=id_block_option]').parent().show();
				}

			},
		});
	}
</script>
