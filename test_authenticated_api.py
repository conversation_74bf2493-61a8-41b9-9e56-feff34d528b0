#!/usr/bin/env python
"""
Test AI Assistant API with authentication
"""

import requests
import json

def test_authenticated_ai_api():
    """Test the AI assistant API endpoint with authentication."""
    
    # Create a session to maintain cookies
    session = requests.Session()
    
    # Step 1: Get login page to get CSRF token
    print("🔐 Step 1: Getting login page...")
    login_url = "http://127.0.0.1:8000/login/"
    login_page = session.get(login_url)
    
    if login_page.status_code != 200:
        print(f"❌ Failed to get login page: {login_page.status_code}")
        return
    
    # Extract CSRF token from login page
    csrf_token = None
    for line in login_page.text.split('\n'):
        if 'csrfmiddlewaretoken' in line and 'value=' in line:
            start = line.find('value="') + 7
            end = line.find('"', start)
            csrf_token = line[start:end]
            break
    
    if not csrf_token:
        print("❌ Could not find CSRF token")
        return
    
    print(f"✅ Got CSRF token: {csrf_token[:20]}...")
    
    # Step 2: Login with credentials
    print("\n🔑 Step 2: Attempting login...")
    login_data = {
        'username': 'admin',  # Try common admin username
        'password': 'admin',  # Try common admin password
        'csrfmiddlewaretoken': csrf_token
    }
    
    login_response = session.post(login_url, data=login_data)
    
    if login_response.status_code == 200 and 'login' not in login_response.url.lower():
        print("✅ Login successful!")
    else:
        print(f"⚠️ Login may have failed (status: {login_response.status_code})")
        print(f"   Redirected to: {login_response.url}")
        print("   Trying API call anyway...")
    
    # Step 3: Test AI API endpoint
    print("\n🤖 Step 3: Testing AI Assistant API...")
    
    api_url = "http://127.0.0.1:8000/ai/command/"
    payload = {
        "message": "Show all employees",
        "context": {
            "current_page": "/",
            "current_module": ""
        }
    }
    
    headers = {
        "Content-Type": "application/json",
        "X-CSRFToken": csrf_token
    }
    
    print(f"URL: {api_url}")
    print(f"Payload: {json.dumps(payload, indent=2)}")
    print("-" * 50)
    
    try:
        response = session.post(api_url, json=payload, headers=headers, timeout=10)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        # Check if response is JSON
        content_type = response.headers.get('content-type', '')
        if 'application/json' in content_type:
            try:
                response_data = response.json()
                print("\n✅ SUCCESS! JSON Response:")
                print(json.dumps(response_data, indent=2))
            except json.JSONDecodeError:
                print("\n⚠️ Response claims to be JSON but is not valid")
                print(f"Response: {response.text[:500]}...")
        else:
            print(f"\n⚠️ Response is not JSON (Content-Type: {content_type})")
            if 'text/html' in content_type:
                if 'login' in response.text.lower():
                    print("   → Still getting login page")
                else:
                    print("   → Getting HTML response")
                print(f"   Response length: {len(response.text)} characters")
            else:
                print(f"Response: {response.text[:200]}...")
                
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Could not connect to the server")
    except requests.exceptions.Timeout:
        print("❌ Timeout Error: Request took too long")
    except Exception as e:
        print(f"❌ Unexpected Error: {e}")


def test_without_auth():
    """Test the API without authentication to confirm our error handling."""
    print("\n" + "="*60)
    print("🧪 Testing API without authentication...")
    
    url = "http://127.0.0.1:8000/ai/command/"
    payload = {"message": "test"}
    
    try:
        response = requests.post(url, json=payload, timeout=5)
        print(f"Status Code: {response.status_code}")
        
        content_type = response.headers.get('content-type', '')
        if 'application/json' in content_type:
            print("✅ Got JSON response (our error handling works!)")
            print(json.dumps(response.json(), indent=2))
        else:
            print("⚠️ Got HTML response (middleware intercepted)")
            
    except Exception as e:
        print(f"❌ Error: {e}")


if __name__ == "__main__":
    print("🚀 AI Assistant Authentication Test")
    print("=" * 60)
    
    test_authenticated_ai_api()
    test_without_auth()
    
    print("\n" + "="*60)
    print("💡 If you're still getting login pages:")
    print("1. Create a user account in Django admin")
    print("2. Update the username/password in this script")
    print("3. Or test the AI assistant through the web interface")
