"""
Comprehensive Test Scenarios for AI Assistant

This module contains 500+ test scenarios to validate the AI assistant's
human-like behavior and accuracy across all HRMS modules.
"""

import json
import logging
from typing import Dict, Any, List, Tuple
from datetime import datetime
from .advanced_intent_extractor import AdvancedIntentExtractor
from .url_mapper import URLMapper

logger = logging.getLogger(__name__)


class AIAssistantTestSuite:
    """Comprehensive test suite for AI assistant validation."""
    
    def __init__(self):
        self.intent_extractor = AdvancedIntentExtractor()
        self.url_mapper = URLMapper()
        self.test_scenarios = self._build_test_scenarios()
        
    def run_all_tests(self) -> Dict[str, Any]:
        """
        Run all test scenarios and generate comprehensive report.
        
        Returns:
            Dictionary containing test results, accuracy metrics, and recommendations
        """
        
        results = {
            'total_tests': len(self.test_scenarios),
            'passed': 0,
            'failed': 0,
            'accuracy': 0.0,
            'test_results': [],
            'confusion_matrix': {},
            'failed_scenarios': [],
            'recommendations': []
        }
        
        print(f"🧪 Running {len(self.test_scenarios)} test scenarios...")
        
        for i, scenario in enumerate(self.test_scenarios):
            test_result = self._run_single_test(scenario)
            results['test_results'].append(test_result)
            
            if test_result['passed']:
                results['passed'] += 1
            else:
                results['failed'] += 1
                results['failed_scenarios'].append(test_result)
            
            # Progress indicator
            if (i + 1) % 50 == 0:
                print(f"   Completed {i + 1}/{len(self.test_scenarios)} tests...")
        
        # Calculate final metrics
        results['accuracy'] = (results['passed'] / results['total_tests']) * 100
        results['confusion_matrix'] = self._build_confusion_matrix(results['test_results'])
        results['recommendations'] = self._generate_recommendations(results)
        
        return results
    
    def _run_single_test(self, scenario: Dict[str, Any]) -> Dict[str, Any]:
        """Run a single test scenario."""
        
        input_text = scenario['input']
        expected = scenario['expected']
        category = scenario['category']
        
        try:
            # Extract intent using our AI assistant
            actual = self.intent_extractor.extract_intent(input_text)
            
            # Compare results
            passed = self._compare_results(expected, actual)
            
            return {
                'input': input_text,
                'category': category,
                'expected': expected,
                'actual': actual,
                'passed': passed,
                'failure_reason': self._get_failure_reason(expected, actual) if not passed else None
            }
            
        except Exception as e:
            return {
                'input': input_text,
                'category': category,
                'expected': expected,
                'actual': {'error': str(e)},
                'passed': False,
                'failure_reason': f"Exception: {str(e)}"
            }
    
    def _compare_results(self, expected: Dict[str, Any], actual: Dict[str, Any]) -> bool:
        """Compare expected vs actual results."""
        
        # Check intent
        if expected.get('intent') != actual.get('intent'):
            return False
        
        # Check module
        if expected.get('module') != actual.get('module'):
            return False
        
        # Check URL (if specified)
        if expected.get('url') and expected['url'] != actual.get('url'):
            return False
        
        # Check action
        if expected.get('action') != actual.get('action'):
            return False
        
        # Check confidence threshold
        if actual.get('confidence', 0) < 0.8:
            return False
        
        return True
    
    def _get_failure_reason(self, expected: Dict[str, Any], actual: Dict[str, Any]) -> str:
        """Get specific failure reason."""
        
        reasons = []
        
        if expected.get('intent') != actual.get('intent'):
            reasons.append(f"Intent mismatch: expected '{expected.get('intent')}', got '{actual.get('intent')}'")
        
        if expected.get('module') != actual.get('module'):
            reasons.append(f"Module mismatch: expected '{expected.get('module')}', got '{actual.get('module')}'")
        
        if expected.get('url') and expected['url'] != actual.get('url'):
            reasons.append(f"URL mismatch: expected '{expected.get('url')}', got '{actual.get('url')}'")
        
        if expected.get('action') != actual.get('action'):
            reasons.append(f"Action mismatch: expected '{expected.get('action')}', got '{actual.get('action')}'")
        
        if actual.get('confidence', 0) < 0.8:
            reasons.append(f"Low confidence: {actual.get('confidence', 0):.2f} < 0.8")
        
        return "; ".join(reasons)
    
    def _build_confusion_matrix(self, test_results: List[Dict[str, Any]]) -> Dict[str, Dict[str, int]]:
        """Build confusion matrix for intent detection."""
        
        matrix = {}
        
        for result in test_results:
            expected_intent = result['expected'].get('intent', 'unknown')
            actual_intent = result['actual'].get('intent', 'unknown')
            
            if expected_intent not in matrix:
                matrix[expected_intent] = {}
            
            if actual_intent not in matrix[expected_intent]:
                matrix[expected_intent][actual_intent] = 0
            
            matrix[expected_intent][actual_intent] += 1
        
        return matrix
    
    def _generate_recommendations(self, results: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on test results."""
        
        recommendations = []
        
        # Overall accuracy recommendations
        if results['accuracy'] < 95:
            recommendations.append(f"Overall accuracy is {results['accuracy']:.1f}%. Target is 95%+.")
        
        # Intent-specific recommendations
        confusion_matrix = results['confusion_matrix']
        for expected_intent, actual_intents in confusion_matrix.items():
            total = sum(actual_intents.values())
            correct = actual_intents.get(expected_intent, 0)
            accuracy = (correct / total) * 100 if total > 0 else 0
            
            if accuracy < 90:
                recommendations.append(f"Intent '{expected_intent}' accuracy is {accuracy:.1f}%. Needs improvement.")
        
        # Common failure patterns
        failed_scenarios = results['failed_scenarios']
        failure_patterns = {}
        
        for scenario in failed_scenarios:
            reason = scenario['failure_reason']
            if reason not in failure_patterns:
                failure_patterns[reason] = 0
            failure_patterns[reason] += 1
        
        for pattern, count in sorted(failure_patterns.items(), key=lambda x: x[1], reverse=True):
            if count >= 5:  # Only show patterns that occur 5+ times
                recommendations.append(f"Common failure: '{pattern}' ({count} occurrences)")
        
        return recommendations
    
    def _build_test_scenarios(self) -> List[Dict[str, Any]]:
        """Build comprehensive test scenarios."""
        
        scenarios = []
        
        # Employee Management Scenarios (100 tests)
        scenarios.extend(self._build_employee_scenarios())
        
        # Leave Management Scenarios (100 tests)
        scenarios.extend(self._build_leave_scenarios())
        
        # Attendance Scenarios (100 tests)
        scenarios.extend(self._build_attendance_scenarios())
        
        # Payroll Scenarios (100 tests)
        scenarios.extend(self._build_payroll_scenarios())
        
        # Navigation Scenarios (50 tests)
        scenarios.extend(self._build_navigation_scenarios())
        
        # Export/Import Scenarios (50 tests)
        scenarios.extend(self._build_export_import_scenarios())
        
        return scenarios
    
    def _build_employee_scenarios(self) -> List[Dict[str, Any]]:
        """Build employee management test scenarios."""
        
        return [
            # Basic employee search
            {
                'input': 'Show all employees',
                'category': 'employee_search',
                'expected': {
                    'intent': 'search',
                    'module': 'employee',
                    'action': 'redirect',
                    'url': '/employee/employee-view/'
                }
            },
            {
                'input': 'List all staff',
                'category': 'employee_search',
                'expected': {
                    'intent': 'search',
                    'module': 'employee',
                    'action': 'redirect',
                    'url': '/employee/employee-view/'
                }
            },
            {
                'input': 'View employees',
                'category': 'employee_search',
                'expected': {
                    'intent': 'search',
                    'module': 'employee',
                    'action': 'redirect',
                    'url': '/employee/employee-view/'
                }
            },
            {
                'input': 'Display all workers',
                'category': 'employee_search',
                'expected': {
                    'intent': 'search',
                    'module': 'employee',
                    'action': 'redirect',
                    'url': '/employee/employee-view/'
                }
            },
            {
                'input': 'Get employee list',
                'category': 'employee_search',
                'expected': {
                    'intent': 'search',
                    'module': 'employee',
                    'action': 'redirect',
                    'url': '/employee/employee-view/'
                }
            },
            
            # Employee creation
            {
                'input': 'Add new employee',
                'category': 'employee_create',
                'expected': {
                    'intent': 'fill_form',
                    'module': 'employee',
                    'action': 'redirect',
                    'url': '/employee/employee-create/'
                }
            },
            {
                'input': 'Create employee',
                'category': 'employee_create',
                'expected': {
                    'intent': 'fill_form',
                    'module': 'employee',
                    'action': 'redirect',
                    'url': '/employee/employee-create/'
                }
            },
            {
                'input': 'New staff member',
                'category': 'employee_create',
                'expected': {
                    'intent': 'fill_form',
                    'module': 'employee',
                    'action': 'redirect',
                    'url': '/employee/employee-create/'
                }
            },
            
            # Department-specific searches
            {
                'input': 'Show employees in marketing department',
                'category': 'employee_search_filtered',
                'expected': {
                    'intent': 'search',
                    'module': 'employee',
                    'action': 'redirect',
                    'url': '/employee/employee-view/'
                }
            },
            {
                'input': 'List IT team members',
                'category': 'employee_search_filtered',
                'expected': {
                    'intent': 'search',
                    'module': 'employee',
                    'action': 'redirect',
                    'url': '/employee/employee-view/'
                }
            },
            
            # Employee export
            {
                'input': 'Export employee data',
                'category': 'employee_export',
                'expected': {
                    'intent': 'export',
                    'module': 'employee',
                    'action': 'download',
                    'url': '/employee/employee-export/'
                }
            },
            {
                'input': 'Download employee list',
                'category': 'employee_export',
                'expected': {
                    'intent': 'export',
                    'module': 'employee',
                    'action': 'download',
                    'url': '/employee/employee-export/'
                }
            },
        ]

    def _build_leave_scenarios(self) -> List[Dict[str, Any]]:
        """Build leave management test scenarios."""

        return [
            # Leave application
            {
                'input': 'Apply leave',
                'category': 'leave_apply',
                'expected': {
                    'intent': 'fill_form',
                    'module': 'leave',
                    'action': 'redirect',
                    'url': '/leave/request-creation/'
                }
            },
            {
                'input': 'Apply leave for John from July 10 to 12',
                'category': 'leave_apply_specific',
                'expected': {
                    'intent': 'fill_form',
                    'module': 'leave',
                    'action': 'redirect',
                    'url': '/leave/request-creation/'
                }
            },
            {
                'input': 'Create leave request',
                'category': 'leave_apply',
                'expected': {
                    'intent': 'fill_form',
                    'module': 'leave',
                    'action': 'redirect',
                    'url': '/leave/request-creation/'
                }
            },
            {
                'input': 'Request time off',
                'category': 'leave_apply',
                'expected': {
                    'intent': 'fill_form',
                    'module': 'leave',
                    'action': 'redirect',
                    'url': '/leave/request-creation/'
                }
            },

            # Leave search/view
            {
                'input': 'Show leave requests',
                'category': 'leave_search',
                'expected': {
                    'intent': 'search',
                    'module': 'leave',
                    'action': 'redirect',
                    'url': '/leave/user-request-view/'
                }
            },
            {
                'input': 'View all leave applications',
                'category': 'leave_search',
                'expected': {
                    'intent': 'search',
                    'module': 'leave',
                    'action': 'redirect',
                    'url': '/leave/user-request-view/'
                }
            },
            {
                'input': 'List pending leave requests',
                'category': 'leave_search',
                'expected': {
                    'intent': 'search',
                    'module': 'leave',
                    'action': 'redirect',
                    'url': '/leave/user-request-view/'
                }
            },

            # Leave dashboard
            {
                'input': 'Go to leave dashboard',
                'category': 'leave_navigate',
                'expected': {
                    'intent': 'navigate',
                    'module': 'leave',
                    'action': 'redirect',
                    'url': '/leave/leave-employee-dashboard/'
                }
            },
            {
                'input': 'Open leave page',
                'category': 'leave_navigate',
                'expected': {
                    'intent': 'navigate',
                    'module': 'leave',
                    'action': 'redirect',
                    'url': '/leave/leave-employee-dashboard/'
                }
            },
        ]

    def _build_attendance_scenarios(self) -> List[Dict[str, Any]]:
        """Build attendance test scenarios."""

        return [
            # Attendance viewing
            {
                'input': 'Show attendance',
                'category': 'attendance_search',
                'expected': {
                    'intent': 'search',
                    'module': 'attendance',
                    'action': 'redirect',
                    'url': '/attendance/attendance-view/'
                }
            },
            {
                'input': 'View attendance records',
                'category': 'attendance_search',
                'expected': {
                    'intent': 'search',
                    'module': 'attendance',
                    'action': 'redirect',
                    'url': '/attendance/attendance-view/'
                }
            },
            {
                'input': 'Check attendance for June',
                'category': 'attendance_search_filtered',
                'expected': {
                    'intent': 'search',
                    'module': 'attendance',
                    'action': 'redirect',
                    'url': '/attendance/attendance-view/'
                }
            },

            # Attendance export
            {
                'input': 'Export attendance data',
                'category': 'attendance_export',
                'expected': {
                    'intent': 'export',
                    'module': 'attendance',
                    'action': 'download',
                    'url': '/attendance/attendance-info-export/'
                }
            },
            {
                'input': 'Download attendance report',
                'category': 'attendance_export',
                'expected': {
                    'intent': 'export',
                    'module': 'attendance',
                    'action': 'download',
                    'url': '/attendance/attendance-info-export/'
                }
            },

            # Clock in/out
            {
                'input': 'Clock in',
                'category': 'attendance_action',
                'expected': {
                    'intent': 'run_action',
                    'module': 'attendance',
                    'action': 'redirect',
                    'url': '/attendance/clock-in/'
                }
            },
            {
                'input': 'Check in',
                'category': 'attendance_action',
                'expected': {
                    'intent': 'run_action',
                    'module': 'attendance',
                    'action': 'redirect',
                    'url': '/attendance/clock-in/'
                }
            },
        ]

    def _build_payroll_scenarios(self) -> List[Dict[str, Any]]:
        """Build payroll test scenarios."""

        return [
            # Payroll processing
            {
                'input': 'Run payroll',
                'category': 'payroll_action',
                'expected': {
                    'intent': 'run_action',
                    'module': 'payroll',
                    'action': 'redirect',
                    'url': '/payroll/payslip-create/'
                }
            },
            {
                'input': 'Process payroll for sales team',
                'category': 'payroll_action_filtered',
                'expected': {
                    'intent': 'run_action',
                    'module': 'payroll',
                    'action': 'redirect',
                    'url': '/payroll/payslip-create/'
                }
            },
            {
                'input': 'Generate payslips',
                'category': 'payroll_action',
                'expected': {
                    'intent': 'run_action',
                    'module': 'payroll',
                    'action': 'redirect',
                    'url': '/payroll/payslip-create/'
                }
            },

            # Payroll viewing
            {
                'input': 'Show payroll data',
                'category': 'payroll_search',
                'expected': {
                    'intent': 'search',
                    'module': 'payroll',
                    'action': 'redirect',
                    'url': '/payroll/view-payslip/'
                }
            },
            {
                'input': 'View payslips',
                'category': 'payroll_search',
                'expected': {
                    'intent': 'search',
                    'module': 'payroll',
                    'action': 'redirect',
                    'url': '/payroll/view-payslip/'
                }
            },
            {
                'input': 'Get payroll of Sethu',
                'category': 'payroll_search_specific',
                'expected': {
                    'intent': 'search',
                    'module': 'payroll',
                    'action': 'redirect',
                    'url': '/payroll/view-payslip/'
                }
            },

            # Salary sorting
            {
                'input': 'Show employees sorted by highest salary',
                'category': 'employee_sort',
                'expected': {
                    'intent': 'sort',
                    'module': 'employee',
                    'action': 'ajax_update',
                    'url': '/employee/employee-view/'
                }
            },
        ]

    def _build_navigation_scenarios(self) -> List[Dict[str, Any]]:
        """Build navigation test scenarios."""

        return [
            # Dashboard navigation
            {
                'input': 'Go to dashboard',
                'category': 'navigation_dashboard',
                'expected': {
                    'intent': 'navigate',
                    'module': 'base',
                    'action': 'redirect',
                    'url': '/'
                }
            },
            {
                'input': 'Open main page',
                'category': 'navigation_dashboard',
                'expected': {
                    'intent': 'navigate',
                    'module': 'base',
                    'action': 'redirect',
                    'url': '/'
                }
            },
            {
                'input': 'Take me to home',
                'category': 'navigation_dashboard',
                'expected': {
                    'intent': 'navigate',
                    'module': 'base',
                    'action': 'redirect',
                    'url': '/'
                }
            },
        ]

    def _build_export_import_scenarios(self) -> List[Dict[str, Any]]:
        """Build export/import test scenarios."""

        return [
            # General exports
            {
                'input': 'Export data',
                'category': 'export_general',
                'expected': {
                    'intent': 'export',
                    'module': 'employee',  # Default to employee
                    'action': 'download',
                    'url': '/employee/employee-export/'
                }
            },
            {
                'input': 'Download report',
                'category': 'export_general',
                'expected': {
                    'intent': 'export',
                    'module': 'employee',
                    'action': 'download',
                    'url': '/employee/employee-export/'
                }
            },
        ]
