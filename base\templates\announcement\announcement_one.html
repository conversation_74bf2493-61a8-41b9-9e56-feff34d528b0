{% load i18n %}
{% load static %}
{% load basefilters %}
{% if messages %}
    <script>reloadMessage();</script>
    <span hx-get="{% url 'announcement-list' %}" hx-trigger="load" hx-target="#announcementListCard"></span>
{% endif %}
<div class="oh-modal__dialog-header pb-4">

    <div class="oh-main__titlebar oh-main__titlebar--left">
        <span class="oh-main__titlebar-title fw-bold mb-0 text-dark">{% trans "Announcement" %} </span>
    </div>

    <button class="oh-modal__close" aria-label="Close">
        <ion-icon name="close-outline"></ion-icon>
    </button>
</div>
<div class="oh-modal__dialog-body">
    {% if instance_ids %}
        <div class="oh-modal__dialog oh-modal__dialog--navigation m-0 p-0">
            <button
                hx-get="{% url 'announcement-single-view' previous_instance_id %}?instance_ids={{instance_ids}}"
                hx-target="#objectDetailsModalTarget" class="oh-modal__diaglog-nav oh-modal__nav-prev" data-action="previous">
                <ion-icon name="chevron-back-outline" class="md hydrated" role="img"
                    aria-label="chevron back outline"></ion-icon>
            </button>

            <button
                hx-get="{% url 'announcement-single-view' next_instance_id %}?instance_ids={{instance_ids}}"
                hx-target="#objectDetailsModalTarget" class="oh-modal__diaglog-nav oh-modal__nav-next" data-action="next">
                <ion-icon name="chevron-forward-outline" class="md hydrated" role="img"
                    aria-label="chevron forward outline"></ion-icon>
            </button>
        </div>
    {% endif %}
    {% if announcement %}
        <div class="card mx-auto p-4 mb-4" style="max-width: 800px; border:1px solid lightgrey; border-radius:10px; box-shadow: 2px 2px 2px grey;">
            {% if perms.base.change_announcement or perms.base.delete_announcement %}
                <div class="oh-dropdown float-end" x-data="{open: false}">
                    <button class="oh-btn oh-stop-prop oh-btn--transparent oh-accordion-meta__btn" @click="open = !open"
                        @click.outside="open = false" title='{% trans "Actions" %}'>
                        <ion-icon name="ellipsis-vertical"></ion-icon>
                    </button>
                    <div class="oh-dropdown__menu oh-dropdown__menu--right" x-show="open" style="display: none;">
                        <ul class="oh-dropdown__items">
                            {% if perms.base.change_announcement %}
                                <li class="oh-dropdown__item">
                                    <a hx-get="{% url 'update-announcement' announcement.id %}?instance_ids={{instance_ids}}" hx-target='#objectUpdateModalTarget'
                                        data-toggle='oh-modal-toggle' data-target="#objectUpdateModal"
                                        class="oh-dropdown__link" style="cursor:pointer;">{% trans "Edit" %}</a>
                                </li>
                            {% endif %}
                            {% if perms.base.delete_announcement %}
                                <li class="oh-dropdown__item">
                                    <form hx-post="{% url 'delete-announcement' announcement.id %}?instance_ids={{instance_ids}}"
                                        hx-confirm="{% trans 'Are you sure you want to delete this announcement?' %}"
                                        hx-target="#objectDetailsModalTarget">
                                        {% csrf_token %}
                                        <button type="submit" class="oh-dropdown__link oh-dropdown__link--danger" data-action="delete">
                                            {% trans "Delete" %}
                                        </button>
                                    </form>
                                </li>
                            {% endif %}
                        </ul>
                    </div>
                </div>
            {% endif %}
            <div class="card-header mb-3">
                <h5 class="card-title fw-bold">{{ announcement.title }}
                    {% if perms.base.view_announcement %}
                    <div data-toggle="oh-modal-toggle" hx-target="#viewedByBody"
                        hx-get="{% url 'announcement-viewed-by' %}?announcement_id={{announcement.id}}" data-target="#viewedBy"
                        title="{{announcement.get_views|length}} {% trans " Views" %}"
                        class="oh-checkpoint-badge text-success mb-2" id="selectAllInstances" style="cursor: pointer">
                        <span>{{announcement.get_views|length}}</span>
                        <span align="center">
                            <ion-icon name="eye-outline"></ion-icon>
                        </span>
                    </div>
                    {% endif %}
                </h5>
                <span class="text-muted fw-bold"><small>
                        {% trans "Posted on" %} &nbsp<span class="dateformat_changer">{{ announcement.created_at|date:"F j, Y"}}</span> &nbsp
                        {% trans "at" %} &nbsp <span class="timeformat_changer">{{ announcement.created_at|time:"g:i A"}}</span></small>
                </span>
            </div>
            <div class="card-body">
                <p class="card-text">{{ announcement.description|safe }}</p>
            </div>
            <div class="card-footer">
                {% for attachment in announcement.attachments.all %}
                {% if announcement.attachments.all|length > 1 %}
                <hr>
                {% endif %}
                {% if attachment.file.url|slice:"-4:" == '.png' or attachment.file.url|slice:"-4:" == '.jpg' or attachment.file.url|slice:"-5:" == '.jpeg' or attachment.file.url|slice:"-4:" == '.gif' or attachment.file.url|slice:"-4:" == '.bmp' or attachment.file.url|slice:"-5:" == '.webp' or attachment.file.url|slice:"-5:" == '.tiff' or attachment.file.url|slice:"-4:" == '.tif' or attachment.file.url|slice:"-4:" == '.svg' %} <!-- If not a PDF, display the image -->
                    <a href="{{ attachment.file.url }}" target="_blank">
                        <img src="{{ attachment.file.url }}" class="img-fluid rounded" alt="Attachment Image" style="max-width: 100%; max-height: 100%; object-fit: contain;">
                    </a>
                {% else %}
                <!-- If the file is a PDF or something, display a link to download -->
                <i><a href="{{ attachment.file.url }}" class='text-danger fw-bold' target="_blank">
                        <span class="oh-file-icon  oh-file-icon--pdf"></span>{% trans "View Attachment" %}</a></i>
                {% endif %}
                {% endfor %}
            </div>
            <div class="card-footer mt-2">
                <div class="oh-announcement__comment-view">

                    {% if announcement.department.all or announcement.job_position.all %}
                    <button class="oh-btn me-1 oh-announcement-btn" type="button" onmouseover="showDetails()"
                        onmouseout="hideDetails()">
                        <ion-icon name="information"></ion-icon>
                    </button>
                    {% endif %}
                    {% if not announcement.disable_comments %}
                    <button class="oh-btn oh-btn--light oh-activity-sidebar__open" type="button"
                        hx-get="{% url 'announcement-view-comment' announcement.id %}" hx-target="#commentContainer"
                        data-target='#activitySidebar' title="Comments">
                        <ion-icon name="chatbox-outline" style="font-size:18px;" class="md hydrated" role="img"
                            aria-label="chevron back outline"></ion-icon>
                    </button>
                    {% endif %}
                </div>
            </div>
            <div id="infoDiv" style="display:none;">
                {% if announcement.department.all %}
                <div class="oh-announcement-hastag__container" style="z-index:1000">
                    <span class="oh-announcement-hashtags" style="font-size:0.8rem;">{% trans "Department" %}</span>
                    <div class="oh-announcement-hashtags">
                        {% for dep in announcement.department.all %}
                        <span class="oh-announcement__tags">#{{ dep.department }}</span>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
                {% if announcement.job_position.all %}
                <div class="oh-announcement-hastag__container">
                    <span class="oh-announcement-hashtags" style="font-size:0.8rem;">{% trans "Job Position" %}</span>
                    <div class="oh-announcement-hashtags">
                        {% for job in announcement.job_position.all %}
                        <span class="oh-announcement__tags">#{{ job.job_position }}</span>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    {% else %}
        <div class="oh-empty h-100">
            <img src="{% static 'images/ui/search.svg' %}" class="oh-404__image" alt="Page not found. 404." />
            <h1 class="oh-empty__title">{% trans "No Records found." %}</h1>
            <p class="oh-empty__subtitle">{% trans "There are no announcements at the moment." %}</p>
        </div>
    {% endif %}

    <div class="oh-modal" id="viewedBy" role="dialog" aria-hidden="true">
        <div class="oh-modal__dialog" style="max-width: 550px">
            <div class="oh-modal__dialog-header">
                <button class="oh-modal__close--custom"
                    onclick="$(this).closest('.oh-modal--show').removeClass('oh-modal--show');"><ion-icon
                        name="close-outline"></ion-icon></button>
            </div>
            <div class="oh-modal__dialog-body" id="viewedByBody"></div>
        </div>
    </div>
</div>
</div>
