"""
URL Context System for AI Assistant

This module provides the AI assistant with access to all Django URLs
and current page context for better navigation assistance.
"""

import json
import logging
from typing import Dict, List, Any, Optional
from urllib.parse import urlparse
from django.urls import get_resolver, reverse, NoReverseMatch
from django.conf import settings

logger = logging.getLogger(__name__)


class URLContextProvider:
    """Provides URL context and navigation information to the AI assistant."""
    
    def __init__(self):
        self._url_cache = None
        self._url_patterns_cache = None
        self._module_urls_cache = None
    
    def get_all_urls(self) -> Dict[str, Any]:
        """Get all available URLs in the Django application."""
        if self._url_cache is None:
            self._build_url_cache()
        return self._url_cache
    
    def get_url_patterns(self) -> List[str]:
        """Get all URL patterns as strings."""
        if self._url_patterns_cache is None:
            self._build_url_patterns_cache()
        return self._url_patterns_cache
    
    def get_module_urls(self) -> Dict[str, List[Dict[str, str]]]:
        """Get URLs organized by module/app."""
        if self._module_urls_cache is None:
            self._build_module_urls_cache()
        return self._module_urls_cache
    
    def find_url_by_name(self, name: str) -> Optional[str]:
        """Find URL by its name."""
        try:
            return reverse(name)
        except NoReverseMatch:
            return None
    
    def find_urls_by_keyword(self, keyword: str) -> List[Dict[str, str]]:
        """Find URLs that match a keyword."""
        keyword_lower = keyword.lower()
        matching_urls = []
        
        all_urls = self.get_all_urls()
        
        for url_info in all_urls.get('urls', []):
            if (keyword_lower in url_info.get('name', '').lower() or
                keyword_lower in url_info.get('pattern', '').lower() or
                keyword_lower in url_info.get('app', '').lower()):
                matching_urls.append(url_info)
        
        return matching_urls
    
    def get_navigation_suggestions(self, intent: str, module: str) -> List[Dict[str, str]]:
        """Get navigation suggestions based on intent and module."""
        suggestions = []
        
        # Module-specific navigation
        module_mapping = {
            'employee': [
                {'name': 'Employee List', 'url': '/employee/employee-view/', 'description': 'View all employees'},
                {'name': 'Add Employee', 'url': '/employee/employee-create/', 'description': 'Add new employee'},
                {'name': 'Employee Import', 'url': '/employee/employee-import/', 'description': 'Import employee data'},
                {'name': 'Employee Export', 'url': '/employee/employee-export/', 'description': 'Export employee data'},
            ],
            'leave': [
                {'name': 'Leave Requests', 'url': '/leave/leave-request-view/', 'description': 'View leave requests'},
                {'name': 'Apply Leave', 'url': '/leave/leave-request-create/', 'description': 'Create leave request'},
                {'name': 'Leave Types', 'url': '/leave/leave-type-view/', 'description': 'Manage leave types'},
                {'name': 'Leave Calendar', 'url': '/leave/leave-calendar/', 'description': 'View leave calendar'},
            ],
            'attendance': [
                {'name': 'Attendance View', 'url': '/attendance/attendance-view/', 'description': 'View attendance records'},
                {'name': 'Clock In/Out', 'url': '/attendance/clock-in-out/', 'description': 'Employee clock in/out'},
                {'name': 'Attendance Reports', 'url': '/attendance/attendance-report/', 'description': 'Generate attendance reports'},
                {'name': 'Attendance Import', 'url': '/attendance/attendance-import/', 'description': 'Import attendance data'},
            ],
            'payroll': [
                {'name': 'Payslips', 'url': '/payroll/payslip-view/', 'description': 'View payslips'},
                {'name': 'Create Payslip', 'url': '/payroll/payslip-create/', 'description': 'Create new payslip'},
                {'name': 'Payroll Settings', 'url': '/payroll/settings/', 'description': 'Configure payroll settings'},
                {'name': 'Salary Components', 'url': '/payroll/salary-components/', 'description': 'Manage salary components'},
            ],
            'recruitment': [
                {'name': 'Job Positions', 'url': '/recruitment/job-position-view/', 'description': 'View job positions'},
                {'name': 'Candidates', 'url': '/recruitment/candidate-view/', 'description': 'View candidates'},
                {'name': 'Interviews', 'url': '/recruitment/interview-schedule/', 'description': 'Schedule interviews'},
                {'name': 'Recruitment Pipeline', 'url': '/recruitment/pipeline/', 'description': 'View recruitment pipeline'},
            ],
            'general': [
                {'name': 'Dashboard', 'url': '/', 'description': 'Main dashboard'},
                {'name': 'Settings', 'url': '/settings/', 'description': 'System settings'},
                {'name': 'Reports', 'url': '/reports/', 'description': 'Generate reports'},
                {'name': 'Help', 'url': '/help/', 'description': 'Help and documentation'},
            ]
        }
        
        # Get suggestions for the specific module
        if module in module_mapping:
            suggestions.extend(module_mapping[module])
        
        # Add general suggestions if not already included
        if module != 'general':
            suggestions.extend(module_mapping['general'][:2])  # Add dashboard and settings
        
        return suggestions
    
    def get_current_page_context(self, request) -> Dict[str, Any]:
        """Get context information about the current page."""
        if not request:
            return {}
        
        current_path = request.path
        current_module = self._detect_module_from_path(current_path)
        
        context = {
            'current_path': current_path,
            'current_module': current_module,
            'query_params': dict(request.GET),
            'method': request.method,
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            'referer': request.META.get('HTTP_REFERER', ''),
        }
        
        # Add breadcrumb information
        context['breadcrumbs'] = self._generate_breadcrumbs(current_path)
        
        return context
    
    def _build_url_cache(self):
        """Build the URL cache."""
        try:
            resolver = get_resolver()
            urls = []
            
            self._extract_urls_from_resolver(resolver, urls)
            
            self._url_cache = {
                'urls': urls,
                'total_count': len(urls),
                'modules': list(set(url.get('app', 'unknown') for url in urls))
            }
            
        except Exception as e:
            logger.error(f"Error building URL cache: {e}")
            self._url_cache = {'urls': [], 'total_count': 0, 'modules': []}
    
    def _build_url_patterns_cache(self):
        """Build the URL patterns cache."""
        try:
            resolver = get_resolver()
            patterns = []
            
            for pattern in resolver.url_patterns:
                if hasattr(pattern, 'pattern'):
                    patterns.append(str(pattern.pattern))
            
            self._url_patterns_cache = patterns
            
        except Exception as e:
            logger.error(f"Error building URL patterns cache: {e}")
            self._url_patterns_cache = []
    
    def _build_module_urls_cache(self):
        """Build the module URLs cache."""
        all_urls = self.get_all_urls()
        module_urls = {}
        
        for url_info in all_urls.get('urls', []):
            app = url_info.get('app', 'unknown')
            if app not in module_urls:
                module_urls[app] = []
            module_urls[app].append(url_info)
        
        self._module_urls_cache = module_urls
    
    def _extract_urls_from_resolver(self, resolver, urls, namespace='', app_name=''):
        """Recursively extract URLs from resolver."""
        try:
            for pattern in resolver.url_patterns:
                if hasattr(pattern, 'url_patterns'):
                    # This is an include() pattern
                    new_namespace = f"{namespace}:{pattern.namespace}" if pattern.namespace else namespace
                    new_app_name = pattern.app_name or app_name
                    self._extract_urls_from_resolver(pattern, urls, new_namespace, new_app_name)
                else:
                    # This is a regular URL pattern
                    url_info = {
                        'pattern': str(pattern.pattern),
                        'name': pattern.name,
                        'namespace': namespace,
                        'app': app_name or 'main',
                    }
                    
                    # Try to reverse the URL if it has a name
                    if pattern.name:
                        try:
                            full_name = f"{namespace}:{pattern.name}" if namespace else pattern.name
                            url_info['url'] = reverse(full_name)
                        except NoReverseMatch:
                            url_info['url'] = None
                    
                    urls.append(url_info)
                    
        except Exception as e:
            logger.warning(f"Error extracting URLs from resolver: {e}")
    
    def _detect_module_from_path(self, path: str) -> str:
        """Detect the module/app from the URL path."""
        path_parts = path.strip('/').split('/')
        
        if not path_parts or path_parts[0] == '':
            return 'dashboard'
        
        first_part = path_parts[0].lower()
        
        # Map URL prefixes to modules
        module_mapping = {
            'employee': 'employee',
            'leave': 'leave',
            'attendance': 'attendance',
            'payroll': 'payroll',
            'recruitment': 'recruitment',
            'pms': 'pms',
            'onboarding': 'onboarding',
            'offboarding': 'offboarding',
            'asset': 'asset',
            'helpdesk': 'helpdesk',
            'settings': 'settings',
            'admin': 'admin',
            'api': 'api',
            'ai': 'ai_assistant',
        }
        
        return module_mapping.get(first_part, 'general')
    
    def _generate_breadcrumbs(self, path: str) -> List[Dict[str, str]]:
        """Generate breadcrumb navigation for the current path."""
        breadcrumbs = [{'name': 'Home', 'url': '/'}]
        
        path_parts = path.strip('/').split('/')
        current_path = ''
        
        for part in path_parts:
            if part:
                current_path += f'/{part}'
                breadcrumbs.append({
                    'name': part.replace('-', ' ').title(),
                    'url': current_path
                })
        
        return breadcrumbs


class NavigationHelper:
    """Helper class for navigation-related AI assistant functions."""
    
    def __init__(self):
        self.url_provider = URLContextProvider()
    
    def suggest_navigation(self, user_input: str, current_context: Dict[str, Any]) -> Dict[str, Any]:
        """Suggest navigation based on user input and current context."""
        user_input_lower = user_input.lower()
        
        # Extract navigation keywords
        navigation_keywords = {
            'dashboard': ['dashboard', 'home', 'main'],
            'employee': ['employee', 'staff', 'worker', 'people'],
            'leave': ['leave', 'vacation', 'holiday', 'time off'],
            'attendance': ['attendance', 'clock', 'present', 'absent'],
            'payroll': ['payroll', 'salary', 'pay', 'wage'],
            'recruitment': ['recruitment', 'hiring', 'candidate', 'job'],
            'settings': ['settings', 'configuration', 'setup'],
            'reports': ['report', 'analytics', 'statistics'],
        }
        
        # Find matching module
        detected_module = 'general'
        for module, keywords in navigation_keywords.items():
            if any(keyword in user_input_lower for keyword in keywords):
                detected_module = module
                break
        
        # Get navigation suggestions
        suggestions = self.url_provider.get_navigation_suggestions('navigate', detected_module)
        
        return {
            'detected_module': detected_module,
            'suggestions': suggestions,
            'current_context': current_context
        }
    
    def get_contextual_help(self, current_path: str) -> List[str]:
        """Get contextual help based on current page."""
        module = self.url_provider._detect_module_from_path(current_path)
        
        help_text = {
            'employee': [
                "You can search employees by name, department, or location",
                "Use 'Add Employee' to create new employee records",
                "Export employee data for external use",
                "Import bulk employee data from Excel/CSV files"
            ],
            'leave': [
                "Apply for leave by clicking 'Create Leave Request'",
                "View your leave balance and history",
                "Managers can approve/reject leave requests",
                "Check leave calendar for team availability"
            ],
            'attendance': [
                "Clock in/out using the attendance system",
                "View your attendance history and reports",
                "Managers can view team attendance",
                "Generate attendance reports for payroll"
            ],
            'payroll': [
                "Process monthly payroll for employees",
                "View and download payslips",
                "Configure salary components and deductions",
                "Generate payroll reports"
            ],
            'dashboard': [
                "Overview of key metrics and notifications",
                "Quick access to common tasks",
                "Recent activities and updates",
                "System announcements and alerts"
            ]
        }
        
        return help_text.get(module, [
            "Navigate using the menu or ask the AI assistant",
            "Use search functionality to find specific items",
            "Check notifications for important updates",
            "Contact support if you need help"
        ])
