{% load static %}{% load i18n %}
<div class="oh-alert-container">
  {% for message in messages %}
  <div class="oh-alert oh-alert--animated {{ message.tags }}">
    {{ message }}
  </div>
  {% endfor %}
</div>
<main class="oh-auth">
  <div class="oh-auth-card">
    <h1
      class="oh-onboarding-card__title oh-onboarding-card__title--h2 text-center my-3"
    >
      {% trans "Change Password" %}
    </h1>
    <form
      method="post"
      class="oh-form-group"
      hx-post="{% url 'change-password' %}"
      hx-target="#form-container"
    >
      {% csrf_token %}
      <div class="oh-input-group">
        <label class="oh-label" for="password"
          >{% trans "Old Password" %}</label
        >
        <div class="oh-password-input-container">
          {{form.old_password}}
          <button class="oh-btn oh-btn--transparent oh-password-input--toggle">
            <ion-icon
              class="oh-passowrd-input__show-icon"
              title="{% trans 'Show Password' %}"
              name="eye-outline"
            ></ion-icon>
            <ion-icon
              class="oh-passowrd-input__hide-icon d-none"
              title="{% trans 'Hide Password' %}"
              name="eye-off-outline"
            ></ion-icon>
          </button>
        </div>
      </div>
      {{form.old_password.errors}}
      <div class="oh-input-group">
        <label class="oh-label" for="confirmPassword"
          >{% trans "New Password" %}</label
        >
        <div class="oh-password-input-container">
          {{form.new_password}}
          <button class="oh-btn oh-btn--transparent oh-password-input--toggle">
            <ion-icon
              class="oh-passowrd-input__show-icon"
              title="{% trans 'Show Password' %}"
              name="eye-outline"
            ></ion-icon>
            <ion-icon
              class="oh-passowrd-input__hide-icon d-none"
              title="{% trans 'Hide Password' %}"
              name="eye-off-outline"
            ></ion-icon>
          </button>
        </div>
      </div>
      {{form.new_password.errors}}
      <div class="oh-input-group">
        <label class="oh-label" for="confirmPassword"
          >{% trans "Confirm Password" %}</label
        >
        <div class="oh-password-input-container">
          {{form.confirm_password}}
          <button class="oh-btn oh-btn--transparent oh-password-input--toggle">
            <ion-icon
              class="oh-passowrd-input__show-icon"
              title="{% trans 'Show Password' %}"
              name="eye-outline"
            ></ion-icon>
            <ion-icon
              class="oh-passowrd-input__hide-icon d-none"
              title="{% trans 'Hide Password' %}"
              name="eye-off-outline"
            ></ion-icon>
          </button>
        </div>
      </div>
      {{form.confirm_password.errors}}
      <button
        type="submit"
        class="oh-btn oh-onboarding-card__button mt-4 oh-btn--secondary oh-btn--shadow w-100 mb-4"
        role="button"
      >
        <ion-icon class="me-2" name="lock-closed-outline"></ion-icon>
        {% trans "Save Password" %}
      </button>
      <small class="text-center"
        ><a
          href="{% url 'forgot-password' %}"
          class="oh-link oh-link--secondary justify-content-center"
          >{% trans "Forgot password" %}?</a
        ></small
      >
    </form>
  </div>
  <img
    src={% if white_label_company.icon %}"{{white_label_company.icon.url}}" {% else %} "{% static 'images/ui/auth-logo.jpeg' %}" {% endif %}
    alt="Eaglora"
  />
</main>
