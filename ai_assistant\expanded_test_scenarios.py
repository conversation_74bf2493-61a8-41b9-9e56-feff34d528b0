"""
Expanded Test Scenarios for AI Assistant - 500+ Test Cases

This module contains comprehensive test scenarios covering every possible
user interaction with the HRMS AI assistant.
"""

from typing import List, Dict, Any


def get_all_test_scenarios() -> List[Dict[str, Any]]:
    """Get all 500+ test scenarios for comprehensive testing."""
    
    scenarios = []
    
    # Employee Management (150 scenarios)
    scenarios.extend(get_employee_scenarios())
    
    # Leave Management (100 scenarios)
    scenarios.extend(get_leave_scenarios())
    
    # Attendance Management (100 scenarios)
    scenarios.extend(get_attendance_scenarios())
    
    # Payroll Management (80 scenarios)
    scenarios.extend(get_payroll_scenarios())
    
    # Navigation & Dashboard (50 scenarios)
    scenarios.extend(get_navigation_scenarios())
    
    # Export/Import Operations (30 scenarios)
    scenarios.extend(get_export_import_scenarios())
    
    # Sorting & Filtering (40 scenarios)
    scenarios.extend(get_sorting_filtering_scenarios())
    
    return scenarios


def get_employee_scenarios() -> List[Dict[str, Any]]:
    """Employee management test scenarios (150 cases)."""
    
    return [
        # Basic employee search variations (30 cases)
        {'input': 'Show all employees', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'List all employees', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'View employees', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Display employees', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Get employee list', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Show staff', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'List staff members', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'View all staff', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Display all workers', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Get all personnel', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Show team members', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'List workforce', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'View people', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Display team', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Get employee data', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Show employee records', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'List employee information', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'View employee details', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Display staff list', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Get staff data', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Show worker list', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'List all workers', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'View worker data', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Display personnel', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Get team list', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Show all people', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'List everyone', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'View everyone', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Display everyone', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Get everyone', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        
        # Department-specific searches (30 cases)
        {'input': 'Show employees in marketing', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'List marketing team', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'View marketing department', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Show IT team', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'List IT department', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'View IT staff', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Show sales team', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'List sales department', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'View sales staff', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Show HR team', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'List HR department', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'View HR staff', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Show finance team', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'List finance department', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'View finance staff', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Show engineering team', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'List engineering department', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'View engineering staff', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Show operations team', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'List operations department', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'View operations staff', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Show admin team', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'List admin department', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'View admin staff', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Get marketing team members', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Display IT team members', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Find sales team members', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Search HR team members', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Locate finance team members', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Find engineering team members', 'expected': {'intent': 'search', 'module': 'employee', 'url': '/employee/employee-view/'}},
        
        # Employee creation variations (30 cases)
        {'input': 'Add new employee', 'expected': {'intent': 'fill_form', 'module': 'employee', 'url': '/employee/employee-create/'}},
        {'input': 'Create employee', 'expected': {'intent': 'fill_form', 'module': 'employee', 'url': '/employee/employee-create/'}},
        {'input': 'New employee', 'expected': {'intent': 'fill_form', 'module': 'employee', 'url': '/employee/employee-create/'}},
        {'input': 'Add staff member', 'expected': {'intent': 'fill_form', 'module': 'employee', 'url': '/employee/employee-create/'}},
        {'input': 'Create staff', 'expected': {'intent': 'fill_form', 'module': 'employee', 'url': '/employee/employee-create/'}},
        {'input': 'New staff', 'expected': {'intent': 'fill_form', 'module': 'employee', 'url': '/employee/employee-create/'}},
        {'input': 'Add worker', 'expected': {'intent': 'fill_form', 'module': 'employee', 'url': '/employee/employee-create/'}},
        {'input': 'Create worker', 'expected': {'intent': 'fill_form', 'module': 'employee', 'url': '/employee/employee-create/'}},
        {'input': 'New worker', 'expected': {'intent': 'fill_form', 'module': 'employee', 'url': '/employee/employee-create/'}},
        {'input': 'Add team member', 'expected': {'intent': 'fill_form', 'module': 'employee', 'url': '/employee/employee-create/'}},
        {'input': 'Create team member', 'expected': {'intent': 'fill_form', 'module': 'employee', 'url': '/employee/employee-create/'}},
        {'input': 'New team member', 'expected': {'intent': 'fill_form', 'module': 'employee', 'url': '/employee/employee-create/'}},
        {'input': 'Add person', 'expected': {'intent': 'fill_form', 'module': 'employee', 'url': '/employee/employee-create/'}},
        {'input': 'Create person', 'expected': {'intent': 'fill_form', 'module': 'employee', 'url': '/employee/employee-create/'}},
        {'input': 'New person', 'expected': {'intent': 'fill_form', 'module': 'employee', 'url': '/employee/employee-create/'}},
        {'input': 'Register employee', 'expected': {'intent': 'fill_form', 'module': 'employee', 'url': '/employee/employee-create/'}},
        {'input': 'Register staff', 'expected': {'intent': 'fill_form', 'module': 'employee', 'url': '/employee/employee-create/'}},
        {'input': 'Register worker', 'expected': {'intent': 'fill_form', 'module': 'employee', 'url': '/employee/employee-create/'}},
        {'input': 'Hire employee', 'expected': {'intent': 'fill_form', 'module': 'employee', 'url': '/employee/employee-create/'}},
        {'input': 'Hire staff', 'expected': {'intent': 'fill_form', 'module': 'employee', 'url': '/employee/employee-create/'}},
        {'input': 'Hire worker', 'expected': {'intent': 'fill_form', 'module': 'employee', 'url': '/employee/employee-create/'}},
        {'input': 'Onboard employee', 'expected': {'intent': 'fill_form', 'module': 'employee', 'url': '/employee/employee-create/'}},
        {'input': 'Onboard staff', 'expected': {'intent': 'fill_form', 'module': 'employee', 'url': '/employee/employee-create/'}},
        {'input': 'Onboard worker', 'expected': {'intent': 'fill_form', 'module': 'employee', 'url': '/employee/employee-create/'}},
        {'input': 'Open employee form', 'expected': {'intent': 'fill_form', 'module': 'employee', 'url': '/employee/employee-create/'}},
        {'input': 'Open staff form', 'expected': {'intent': 'fill_form', 'module': 'employee', 'url': '/employee/employee-create/'}},
        {'input': 'Open worker form', 'expected': {'intent': 'fill_form', 'module': 'employee', 'url': '/employee/employee-create/'}},
        {'input': 'Fill employee form', 'expected': {'intent': 'fill_form', 'module': 'employee', 'url': '/employee/employee-create/'}},
        {'input': 'Fill staff form', 'expected': {'intent': 'fill_form', 'module': 'employee', 'url': '/employee/employee-create/'}},
        {'input': 'Fill worker form', 'expected': {'intent': 'fill_form', 'module': 'employee', 'url': '/employee/employee-create/'}},
        
        # Employee export variations (30 cases)
        {'input': 'Export employee data', 'expected': {'intent': 'export', 'module': 'employee', 'url': '/employee/employee-export/'}},
        {'input': 'Download employee data', 'expected': {'intent': 'export', 'module': 'employee', 'url': '/employee/employee-export/'}},
        {'input': 'Export employees', 'expected': {'intent': 'export', 'module': 'employee', 'url': '/employee/employee-export/'}},
        {'input': 'Download employees', 'expected': {'intent': 'export', 'module': 'employee', 'url': '/employee/employee-export/'}},
        {'input': 'Export staff data', 'expected': {'intent': 'export', 'module': 'employee', 'url': '/employee/employee-export/'}},
        {'input': 'Download staff data', 'expected': {'intent': 'export', 'module': 'employee', 'url': '/employee/employee-export/'}},
        {'input': 'Export staff', 'expected': {'intent': 'export', 'module': 'employee', 'url': '/employee/employee-export/'}},
        {'input': 'Download staff', 'expected': {'intent': 'export', 'module': 'employee', 'url': '/employee/employee-export/'}},
        {'input': 'Export employee list', 'expected': {'intent': 'export', 'module': 'employee', 'url': '/employee/employee-export/'}},
        {'input': 'Download employee list', 'expected': {'intent': 'export', 'module': 'employee', 'url': '/employee/employee-export/'}},
        {'input': 'Export staff list', 'expected': {'intent': 'export', 'module': 'employee', 'url': '/employee/employee-export/'}},
        {'input': 'Download staff list', 'expected': {'intent': 'export', 'module': 'employee', 'url': '/employee/employee-export/'}},
        {'input': 'Save employee data', 'expected': {'intent': 'export', 'module': 'employee', 'url': '/employee/employee-export/'}},
        {'input': 'Save staff data', 'expected': {'intent': 'export', 'module': 'employee', 'url': '/employee/employee-export/'}},
        {'input': 'Generate employee report', 'expected': {'intent': 'export', 'module': 'employee', 'url': '/employee/employee-export/'}},
        {'input': 'Generate staff report', 'expected': {'intent': 'export', 'module': 'employee', 'url': '/employee/employee-export/'}},
        {'input': 'Create employee report', 'expected': {'intent': 'export', 'module': 'employee', 'url': '/employee/employee-export/'}},
        {'input': 'Create staff report', 'expected': {'intent': 'export', 'module': 'employee', 'url': '/employee/employee-export/'}},
        {'input': 'Export to Excel', 'expected': {'intent': 'export', 'module': 'employee', 'url': '/employee/employee-export/'}},
        {'input': 'Download Excel', 'expected': {'intent': 'export', 'module': 'employee', 'url': '/employee/employee-export/'}},
        {'input': 'Export to CSV', 'expected': {'intent': 'export', 'module': 'employee', 'url': '/employee/employee-export/'}},
        {'input': 'Download CSV', 'expected': {'intent': 'export', 'module': 'employee', 'url': '/employee/employee-export/'}},
        {'input': 'Export employee Excel', 'expected': {'intent': 'export', 'module': 'employee', 'url': '/employee/employee-export/'}},
        {'input': 'Download employee Excel', 'expected': {'intent': 'export', 'module': 'employee', 'url': '/employee/employee-export/'}},
        {'input': 'Export employee CSV', 'expected': {'intent': 'export', 'module': 'employee', 'url': '/employee/employee-export/'}},
        {'input': 'Download employee CSV', 'expected': {'intent': 'export', 'module': 'employee', 'url': '/employee/employee-export/'}},
        {'input': 'Get employee file', 'expected': {'intent': 'export', 'module': 'employee', 'url': '/employee/employee-export/'}},
        {'input': 'Get staff file', 'expected': {'intent': 'export', 'module': 'employee', 'url': '/employee/employee-export/'}},
        {'input': 'Download employee file', 'expected': {'intent': 'export', 'module': 'employee', 'url': '/employee/employee-export/'}},
        {'input': 'Download staff file', 'expected': {'intent': 'export', 'module': 'employee', 'url': '/employee/employee-export/'}},
        
        # Navigation to employee pages (30 cases)
        {'input': 'Go to employees', 'expected': {'intent': 'navigate', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Navigate to employees', 'expected': {'intent': 'navigate', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Open employees', 'expected': {'intent': 'navigate', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Go to staff', 'expected': {'intent': 'navigate', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Navigate to staff', 'expected': {'intent': 'navigate', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Open staff', 'expected': {'intent': 'navigate', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Go to employee page', 'expected': {'intent': 'navigate', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Navigate to employee page', 'expected': {'intent': 'navigate', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Open employee page', 'expected': {'intent': 'navigate', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Go to staff page', 'expected': {'intent': 'navigate', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Navigate to staff page', 'expected': {'intent': 'navigate', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Open staff page', 'expected': {'intent': 'navigate', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Take me to employees', 'expected': {'intent': 'navigate', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Take me to staff', 'expected': {'intent': 'navigate', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Show me employees', 'expected': {'intent': 'navigate', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Show me staff', 'expected': {'intent': 'navigate', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Direct me to employees', 'expected': {'intent': 'navigate', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Direct me to staff', 'expected': {'intent': 'navigate', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Go to employee section', 'expected': {'intent': 'navigate', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Navigate to employee section', 'expected': {'intent': 'navigate', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Open employee section', 'expected': {'intent': 'navigate', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Go to staff section', 'expected': {'intent': 'navigate', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Navigate to staff section', 'expected': {'intent': 'navigate', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Open staff section', 'expected': {'intent': 'navigate', 'module': 'employee', 'url': '/employee/employee-view/'}},
        {'input': 'Go to employee dashboard', 'expected': {'intent': 'navigate', 'module': 'employee', 'url': '/employee/dashboard/'}},
        {'input': 'Navigate to employee dashboard', 'expected': {'intent': 'navigate', 'module': 'employee', 'url': '/employee/dashboard/'}},
        {'input': 'Open employee dashboard', 'expected': {'intent': 'navigate', 'module': 'employee', 'url': '/employee/dashboard/'}},
        {'input': 'Go to staff dashboard', 'expected': {'intent': 'navigate', 'module': 'employee', 'url': '/employee/dashboard/'}},
        {'input': 'Navigate to staff dashboard', 'expected': {'intent': 'navigate', 'module': 'employee', 'url': '/employee/dashboard/'}},
        {'input': 'Open staff dashboard', 'expected': {'intent': 'navigate', 'module': 'employee', 'url': '/employee/dashboard/'}},
    ]


def get_leave_scenarios() -> List[Dict[str, Any]]:
    """Leave management test scenarios (100 cases)."""

    return [
        # Leave application variations (25 cases)
        {'input': 'Apply leave', 'expected': {'intent': 'fill_form', 'module': 'leave', 'url': '/leave/request-creation/'}},
        {'input': 'Apply for leave', 'expected': {'intent': 'fill_form', 'module': 'leave', 'url': '/leave/request-creation/'}},
        {'input': 'Request leave', 'expected': {'intent': 'fill_form', 'module': 'leave', 'url': '/leave/request-creation/'}},
        {'input': 'Create leave request', 'expected': {'intent': 'fill_form', 'module': 'leave', 'url': '/leave/request-creation/'}},
        {'input': 'New leave request', 'expected': {'intent': 'fill_form', 'module': 'leave', 'url': '/leave/request-creation/'}},
        {'input': 'Apply leave for John', 'expected': {'intent': 'fill_form', 'module': 'leave', 'url': '/leave/request-creation/'}},
        {'input': 'Apply leave for John from July 10 to 12', 'expected': {'intent': 'fill_form', 'module': 'leave', 'url': '/leave/request-creation/'}},
        {'input': 'Request time off', 'expected': {'intent': 'fill_form', 'module': 'leave', 'url': '/leave/request-creation/'}},
        {'input': 'Take leave', 'expected': {'intent': 'fill_form', 'module': 'leave', 'url': '/leave/request-creation/'}},
        {'input': 'Book leave', 'expected': {'intent': 'fill_form', 'module': 'leave', 'url': '/leave/request-creation/'}},
        {'input': 'Schedule leave', 'expected': {'intent': 'fill_form', 'module': 'leave', 'url': '/leave/request-creation/'}},
        {'input': 'Plan leave', 'expected': {'intent': 'fill_form', 'module': 'leave', 'url': '/leave/request-creation/'}},
        {'input': 'Submit leave request', 'expected': {'intent': 'fill_form', 'module': 'leave', 'url': '/leave/request-creation/'}},
        {'input': 'File leave request', 'expected': {'intent': 'fill_form', 'module': 'leave', 'url': '/leave/request-creation/'}},
        {'input': 'Open leave form', 'expected': {'intent': 'fill_form', 'module': 'leave', 'url': '/leave/request-creation/'}},
        {'input': 'Fill leave form', 'expected': {'intent': 'fill_form', 'module': 'leave', 'url': '/leave/request-creation/'}},
        {'input': 'Complete leave form', 'expected': {'intent': 'fill_form', 'module': 'leave', 'url': '/leave/request-creation/'}},
        {'input': 'Start leave application', 'expected': {'intent': 'fill_form', 'module': 'leave', 'url': '/leave/request-creation/'}},
        {'input': 'Begin leave request', 'expected': {'intent': 'fill_form', 'module': 'leave', 'url': '/leave/request-creation/'}},
        {'input': 'Initiate leave request', 'expected': {'intent': 'fill_form', 'module': 'leave', 'url': '/leave/request-creation/'}},
        {'input': 'Apply for vacation', 'expected': {'intent': 'fill_form', 'module': 'leave', 'url': '/leave/request-creation/'}},
        {'input': 'Request vacation', 'expected': {'intent': 'fill_form', 'module': 'leave', 'url': '/leave/request-creation/'}},
        {'input': 'Apply for holiday', 'expected': {'intent': 'fill_form', 'module': 'leave', 'url': '/leave/request-creation/'}},
        {'input': 'Request holiday', 'expected': {'intent': 'fill_form', 'module': 'leave', 'url': '/leave/request-creation/'}},
        {'input': 'Apply for time off', 'expected': {'intent': 'fill_form', 'module': 'leave', 'url': '/leave/request-creation/'}},

        # Leave viewing variations (25 cases)
        {'input': 'Show leave requests', 'expected': {'intent': 'search', 'module': 'leave', 'url': '/leave/user-request-view/'}},
        {'input': 'List leave requests', 'expected': {'intent': 'search', 'module': 'leave', 'url': '/leave/user-request-view/'}},
        {'input': 'View leave requests', 'expected': {'intent': 'search', 'module': 'leave', 'url': '/leave/user-request-view/'}},
        {'input': 'Display leave requests', 'expected': {'intent': 'search', 'module': 'leave', 'url': '/leave/user-request-view/'}},
        {'input': 'Get leave requests', 'expected': {'intent': 'search', 'module': 'leave', 'url': '/leave/user-request-view/'}},
        {'input': 'Show all leave applications', 'expected': {'intent': 'search', 'module': 'leave', 'url': '/leave/user-request-view/'}},
        {'input': 'List all leave applications', 'expected': {'intent': 'search', 'module': 'leave', 'url': '/leave/user-request-view/'}},
        {'input': 'View all leave applications', 'expected': {'intent': 'search', 'module': 'leave', 'url': '/leave/user-request-view/'}},
        {'input': 'Display all leave applications', 'expected': {'intent': 'search', 'module': 'leave', 'url': '/leave/user-request-view/'}},
        {'input': 'Get all leave applications', 'expected': {'intent': 'search', 'module': 'leave', 'url': '/leave/user-request-view/'}},
        {'input': 'Show pending leave requests', 'expected': {'intent': 'search', 'module': 'leave', 'url': '/leave/user-request-view/'}},
        {'input': 'List pending leave requests', 'expected': {'intent': 'search', 'module': 'leave', 'url': '/leave/user-request-view/'}},
        {'input': 'View pending leave requests', 'expected': {'intent': 'search', 'module': 'leave', 'url': '/leave/user-request-view/'}},
        {'input': 'Display pending leave requests', 'expected': {'intent': 'search', 'module': 'leave', 'url': '/leave/user-request-view/'}},
        {'input': 'Get pending leave requests', 'expected': {'intent': 'search', 'module': 'leave', 'url': '/leave/user-request-view/'}},
        {'input': 'Show approved leave requests', 'expected': {'intent': 'search', 'module': 'leave', 'url': '/leave/user-request-view/'}},
        {'input': 'List approved leave requests', 'expected': {'intent': 'search', 'module': 'leave', 'url': '/leave/user-request-view/'}},
        {'input': 'View approved leave requests', 'expected': {'intent': 'search', 'module': 'leave', 'url': '/leave/user-request-view/'}},
        {'input': 'Display approved leave requests', 'expected': {'intent': 'search', 'module': 'leave', 'url': '/leave/user-request-view/'}},
        {'input': 'Get approved leave requests', 'expected': {'intent': 'search', 'module': 'leave', 'url': '/leave/user-request-view/'}},
        {'input': 'Check leave balance', 'expected': {'intent': 'search', 'module': 'leave', 'url': '/leave/user-request-view/'}},
        {'input': 'View leave balance', 'expected': {'intent': 'search', 'module': 'leave', 'url': '/leave/user-request-view/'}},
        {'input': 'Show leave balance', 'expected': {'intent': 'search', 'module': 'leave', 'url': '/leave/user-request-view/'}},
        {'input': 'Get leave balance', 'expected': {'intent': 'search', 'module': 'leave', 'url': '/leave/user-request-view/'}},
        {'input': 'Display leave balance', 'expected': {'intent': 'search', 'module': 'leave', 'url': '/leave/user-request-view/'}},

        # Leave navigation variations (25 cases)
        {'input': 'Go to leave dashboard', 'expected': {'intent': 'navigate', 'module': 'leave', 'url': '/leave/leave-employee-dashboard/'}},
        {'input': 'Navigate to leave dashboard', 'expected': {'intent': 'navigate', 'module': 'leave', 'url': '/leave/leave-employee-dashboard/'}},
        {'input': 'Open leave dashboard', 'expected': {'intent': 'navigate', 'module': 'leave', 'url': '/leave/leave-employee-dashboard/'}},
        {'input': 'Go to leave page', 'expected': {'intent': 'navigate', 'module': 'leave', 'url': '/leave/leave-employee-dashboard/'}},
        {'input': 'Navigate to leave page', 'expected': {'intent': 'navigate', 'module': 'leave', 'url': '/leave/leave-employee-dashboard/'}},
        {'input': 'Open leave page', 'expected': {'intent': 'navigate', 'module': 'leave', 'url': '/leave/leave-employee-dashboard/'}},
        {'input': 'Go to leave section', 'expected': {'intent': 'navigate', 'module': 'leave', 'url': '/leave/leave-employee-dashboard/'}},
        {'input': 'Navigate to leave section', 'expected': {'intent': 'navigate', 'module': 'leave', 'url': '/leave/leave-employee-dashboard/'}},
        {'input': 'Open leave section', 'expected': {'intent': 'navigate', 'module': 'leave', 'url': '/leave/leave-employee-dashboard/'}},
        {'input': 'Take me to leave', 'expected': {'intent': 'navigate', 'module': 'leave', 'url': '/leave/leave-employee-dashboard/'}},
        {'input': 'Show me leave', 'expected': {'intent': 'navigate', 'module': 'leave', 'url': '/leave/leave-employee-dashboard/'}},
        {'input': 'Direct me to leave', 'expected': {'intent': 'navigate', 'module': 'leave', 'url': '/leave/leave-employee-dashboard/'}},
        {'input': 'Go to leave management', 'expected': {'intent': 'navigate', 'module': 'leave', 'url': '/leave/leave-employee-dashboard/'}},
        {'input': 'Navigate to leave management', 'expected': {'intent': 'navigate', 'module': 'leave', 'url': '/leave/leave-employee-dashboard/'}},
        {'input': 'Open leave management', 'expected': {'intent': 'navigate', 'module': 'leave', 'url': '/leave/leave-employee-dashboard/'}},
        {'input': 'Go to vacation', 'expected': {'intent': 'navigate', 'module': 'leave', 'url': '/leave/leave-employee-dashboard/'}},
        {'input': 'Navigate to vacation', 'expected': {'intent': 'navigate', 'module': 'leave', 'url': '/leave/leave-employee-dashboard/'}},
        {'input': 'Open vacation', 'expected': {'intent': 'navigate', 'module': 'leave', 'url': '/leave/leave-employee-dashboard/'}},
        {'input': 'Go to holiday', 'expected': {'intent': 'navigate', 'module': 'leave', 'url': '/leave/leave-employee-dashboard/'}},
        {'input': 'Navigate to holiday', 'expected': {'intent': 'navigate', 'module': 'leave', 'url': '/leave/leave-employee-dashboard/'}},
        {'input': 'Open holiday', 'expected': {'intent': 'navigate', 'module': 'leave', 'url': '/leave/leave-employee-dashboard/'}},
        {'input': 'Go to time off', 'expected': {'intent': 'navigate', 'module': 'leave', 'url': '/leave/leave-employee-dashboard/'}},
        {'input': 'Navigate to time off', 'expected': {'intent': 'navigate', 'module': 'leave', 'url': '/leave/leave-employee-dashboard/'}},
        {'input': 'Open time off', 'expected': {'intent': 'navigate', 'module': 'leave', 'url': '/leave/leave-employee-dashboard/'}},
        {'input': 'View leave calendar', 'expected': {'intent': 'navigate', 'module': 'leave', 'url': '/leave/leave-calendar/'}},

        # Leave export variations (25 cases)
        {'input': 'Export leave data', 'expected': {'intent': 'export', 'module': 'leave', 'url': '/leave/leave-export/'}},
        {'input': 'Download leave data', 'expected': {'intent': 'export', 'module': 'leave', 'url': '/leave/leave-export/'}},
        {'input': 'Export leave requests', 'expected': {'intent': 'export', 'module': 'leave', 'url': '/leave/leave-export/'}},
        {'input': 'Download leave requests', 'expected': {'intent': 'export', 'module': 'leave', 'url': '/leave/leave-export/'}},
        {'input': 'Export leave report', 'expected': {'intent': 'export', 'module': 'leave', 'url': '/leave/leave-export/'}},
        {'input': 'Download leave report', 'expected': {'intent': 'export', 'module': 'leave', 'url': '/leave/leave-export/'}},
        {'input': 'Generate leave report', 'expected': {'intent': 'export', 'module': 'leave', 'url': '/leave/leave-export/'}},
        {'input': 'Create leave report', 'expected': {'intent': 'export', 'module': 'leave', 'url': '/leave/leave-export/'}},
        {'input': 'Save leave data', 'expected': {'intent': 'export', 'module': 'leave', 'url': '/leave/leave-export/'}},
        {'input': 'Export vacation data', 'expected': {'intent': 'export', 'module': 'leave', 'url': '/leave/leave-export/'}},
        {'input': 'Download vacation data', 'expected': {'intent': 'export', 'module': 'leave', 'url': '/leave/leave-export/'}},
        {'input': 'Export holiday data', 'expected': {'intent': 'export', 'module': 'leave', 'url': '/leave/leave-export/'}},
        {'input': 'Download holiday data', 'expected': {'intent': 'export', 'module': 'leave', 'url': '/leave/leave-export/'}},
        {'input': 'Export time off data', 'expected': {'intent': 'export', 'module': 'leave', 'url': '/leave/leave-export/'}},
        {'input': 'Download time off data', 'expected': {'intent': 'export', 'module': 'leave', 'url': '/leave/leave-export/'}},
        {'input': 'Export leave Excel', 'expected': {'intent': 'export', 'module': 'leave', 'url': '/leave/leave-export/'}},
        {'input': 'Download leave Excel', 'expected': {'intent': 'export', 'module': 'leave', 'url': '/leave/leave-export/'}},
        {'input': 'Export leave CSV', 'expected': {'intent': 'export', 'module': 'leave', 'url': '/leave/leave-export/'}},
        {'input': 'Download leave CSV', 'expected': {'intent': 'export', 'module': 'leave', 'url': '/leave/leave-export/'}},
        {'input': 'Get leave file', 'expected': {'intent': 'export', 'module': 'leave', 'url': '/leave/leave-export/'}},
        {'input': 'Download leave file', 'expected': {'intent': 'export', 'module': 'leave', 'url': '/leave/leave-export/'}},
        {'input': 'Export leave list', 'expected': {'intent': 'export', 'module': 'leave', 'url': '/leave/leave-export/'}},
        {'input': 'Download leave list', 'expected': {'intent': 'export', 'module': 'leave', 'url': '/leave/leave-export/'}},
        {'input': 'Save leave report', 'expected': {'intent': 'export', 'module': 'leave', 'url': '/leave/leave-export/'}},
        {'input': 'Export leave summary', 'expected': {'intent': 'export', 'module': 'leave', 'url': '/leave/leave-export/'}},
    ]
