"""
Lang<PERSON>hain Agent for AI Assistant

This module implements the Lang<PERSON>hain agent with local LLM integration via Ollama.
"""

import json
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from django.contrib.auth.models import User
from django.urls import get_resolver

# Lang<PERSON>hain imports with fallback
try:
    from langchain.agents import Agent<PERSON>ype, initialize_agent, Tool
    from langchain.memory import ConversationBufferWindowMemory
    from langchain.schema import BaseMessage, HumanMessage, AIMessage
    from langchain.llms import Ollama
    from langchain.callbacks.manager import CallbackManagerForLLMRun
    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False
    # Create dummy classes for when <PERSON><PERSON><PERSON><PERSON> is not available
    class Tool:
        def __init__(self, name, func, description):
            self.name = name
            self.func = func
            self.description = description

from .models import AIAssistantSettings, ConversationMemory
from .django_tools import DjangoToolsRegistry

logger = logging.getLogger(__name__)


class EagloraAIAgent:
    """
    Main AI Agent class that uses <PERSON><PERSON><PERSON><PERSON> with Ollama for human-like reasoning.
    """
    
    def __init__(self, user: User):
        self.user = user
        self.settings = AIAssistantSettings.get_settings()
        self.tools_registry = DjangoToolsRegistry(user)
        
        # Initialize LangChain components if available
        if LANGCHAIN_AVAILABLE and self.settings.enable_langchain:
            self._initialize_langchain_agent()
        else:
            self.agent = None
            self.memory = None
    
    def _initialize_langchain_agent(self):
        """Initialize the LangChain agent with Ollama LLM."""
        try:
            # Initialize Ollama LLM
            self.llm = Ollama(
                model=self.settings.ollama_model,
                base_url=self.settings.ollama_base_url,
                temperature=self.settings.temperature,
                num_predict=self.settings.max_tokens,
            )
            
            # Initialize memory
            self.memory = ConversationBufferWindowMemory(
                memory_key="chat_history",
                return_messages=True,
                k=10  # Keep last 10 exchanges
            )
            
            # Get tools from registry
            tools = self._get_langchain_tools()
            
            # Initialize agent
            self.agent = initialize_agent(
                tools=tools,
                llm=self.llm,
                agent=AgentType.CONVERSATIONAL_REACT_DESCRIPTION,
                memory=self.memory,
                verbose=True,
                handle_parsing_errors=True,
                max_iterations=3,
                early_stopping_method="generate"
            )
            
            logger.info(f"LangChain agent initialized with {len(tools)} tools")
            
        except Exception as e:
            logger.error(f"Failed to initialize LangChain agent: {e}")
            self.agent = None
            self.memory = None
    
    def _get_langchain_tools(self) -> List[Tool]:
        """Get LangChain tools from the Django tools registry."""
        tools = []
        
        # Get all available Django tools
        django_tools = self.tools_registry.get_all_tools()
        
        for tool_name, tool_info in django_tools.items():
            langchain_tool = Tool(
                name=tool_name,
                func=tool_info['function'],
                description=tool_info['description']
            )
            tools.append(langchain_tool)
        
        # Add URL context tool
        tools.append(Tool(
            name="get_available_urls",
            func=self._get_available_urls,
            description="Get all available URLs in the HRMS system for navigation"
        ))
        
        # Add current context tool
        tools.append(Tool(
            name="get_user_context",
            func=self._get_user_context,
            description="Get current user context including role, permissions, and current page"
        ))
        
        return tools
    
    def process_message(self, message: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Process a user message and return a structured response.
        
        Args:
            message: User's natural language message
            context: Optional context information (current page, etc.)
            
        Returns:
            Structured response dictionary
        """
        try:
            if self.agent and LANGCHAIN_AVAILABLE:
                print("inside if  >>>>>>>")
                return self._process_with_langchain(message, context)
            else:
                print("inside else >>>>>>>")
                return self._process_with_fallback(message, context)
                
        except Exception as e:
            logger.error(f"Error processing message: {e}")
            return {
                'action': 'reply',
                'message': 'Sorry, I encountered an error processing your request.',
                'intent': 'error',
                'confidence': 0.0
            }
    
    def _process_with_langchain(self, message: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Process message using LangChain agent."""
        try:
            # Prepare the prompt with system context
            system_prompt = self._build_system_prompt(context)
            full_message = f"{system_prompt}\n\nUser: {message}"
            
            # Run the agent
            response = self.agent.run(full_message)
            
            # Parse the response
            return self._parse_agent_response(response)
            
        except Exception as e:
            logger.error(f"LangChain processing error: {e}")
            return self._process_with_fallback(message, context)
    
    def _process_with_fallback(self, message: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Fallback processing when LangChain is not available."""
        from utils.intent_extractor import extract_command_intent
        return extract_command_intent(message)
    
    def _build_system_prompt(self, context: Optional[Dict[str, Any]] = None) -> str:
        """Build the system prompt for the LangChain agent."""
        current_date = datetime.now().strftime("%Y-%m-%d")
        current_time = datetime.now().strftime("%H:%M")
        
        prompt = f"""You are an intelligent AI assistant inside an HRMS system. You are NOT a chatbot.

You can perform real actions on behalf of the user by calling backend tools (functions), navigating to pages, filling forms, or running reports.

Current Context:
- Date: {current_date}
- Time: {current_time}
- User: {self.user.username}
- User Role: {self._get_user_role()}
- User Permissions: {self._get_user_permissions()}
"""
        
        if context:
            if context.get('current_page'):
                prompt += f"- Current Page: {context['current_page']}\n"
            if context.get('current_module'):
                prompt += f"- Current Module: {context['current_module']}\n"
        
        prompt += """
You must:
- Think like a human assistant
- Understand natural language from users
- Analyze vague inputs and figure out their goal
- Decide what needs to happen next
- Return a structured JSON plan

You have access to:
- URLs: Django route patterns for navigation
- Tools: search_employees, get_payroll, export_attendance, etc.
- User context: current page, logged-in role, current date/month

Always respond with a JSON object in this format:
{
  "action": "redirect" | "download" | "ajax_update" | "reply",
  "url": "/target/page",
  "filters": { "key": "value" },
  "sort_by": "field",
  "order": "asc" | "desc",
  "prefill_data": {},
  "message": "Short response for UI",
  "intent": "detected_intent",
  "confidence": 0.8
}

Examples:
- "Get payroll of Sethu" → Find employee → open/export their payroll
- "Show all employees in Marketing" → Find people in that department → display in table
- "Apply leave for Arjun from July 10 to 12" → Go to form → prefill employee and date
- "Export attendance for June" → Trigger export → prepare download
- "Sort employees by highest salary" → Apply sorting on frontend table
"""
        
        return prompt
    
    def _parse_agent_response(self, response: str) -> Dict[str, Any]:
        """Parse the agent's response into a structured format."""
        try:
            # Try to extract JSON from the response
            if '{' in response and '}' in response:
                start = response.find('{')
                end = response.rfind('}') + 1
                json_str = response[start:end]
                return json.loads(json_str)
            else:
                # If no JSON found, create a reply response
                return {
                    'action': 'reply',
                    'message': response,
                    'intent': 'reply',
                    'confidence': 0.7
                }
        except json.JSONDecodeError:
            return {
                'action': 'reply',
                'message': response,
                'intent': 'reply',
                'confidence': 0.5
            }
    
    def _get_available_urls(self, query: str = "") -> str:
        """Get available URLs in the system."""
        try:
            resolver = get_resolver()
            url_patterns = []
            
            for pattern in resolver.url_patterns:
                if hasattr(pattern, 'pattern'):
                    url_patterns.append(str(pattern.pattern))
            
            return json.dumps({
                'urls': url_patterns[:20],  # Limit to first 20
                'total_count': len(url_patterns)
            })
        except Exception as e:
            return f"Error getting URLs: {e}"
    
    def _get_user_context(self, query: str = "") -> str:
        """Get current user context."""
        try:
            context = {
                'username': self.user.username,
                'is_staff': self.user.is_staff,
                'is_superuser': self.user.is_superuser,
                'role': self._get_user_role(),
                'permissions': self._get_user_permissions()[:10],  # Limit permissions
                'groups': [group.name for group in self.user.groups.all()]
            }
            return json.dumps(context)
        except Exception as e:
            return f"Error getting user context: {e}"
    
    def _get_user_role(self) -> str:
        """Get user's primary role."""
        if self.user.is_superuser:
            return "Administrator"
        elif self.user.is_staff:
            return "Staff"
        elif self.user.groups.exists():
            return self.user.groups.first().name
        else:
            return "Employee"
    
    def _get_user_permissions(self) -> List[str]:
        """Get user's permissions."""
        return [perm.codename for perm in self.user.user_permissions.all()]
