{% load basefilters i18n %}
{% for employee in employees %}
  <div class="oh-user_permission-list_item perm-accordion exclude-accordion-style" onclick="$(this).next().toggle();$(this).toggleClass('perm-accordion-active');">
    <div class="oh-user_permission-list_profile">
      <div class="oh-navbar__user-photo oh-user_permission--profile">
        <img src="{{ employee.get_avatar }}" class="oh-navbar__user-image" loading="lazy" />
      </div>
      <div class="oh-feedback-card__name-container ms-1">
        <span class="oh-card__title oh-card__title--sm fw-bold me-1">{{ employee }}</span>
        <span class="oh-user_permission_list-text oh-text--light">{{ employee.employee_work_info.job_role_id.job_role }} | {{ employee.employee_work_info.job_position_id }} | {{ employee.employee_work_info.department_id }}</span>
      </div>
    </div>
    <button class="oh-accordion-meta__btn oh-user_permssion-dropdownbtn"><ion-icon class="ms-2 oh-accordion-meta__btn-icon md hydrated" name="caret-down-outline" role="img" aria-label="caret down outline"></ion-icon></button>
  </div>
  <div class="panel view-employees" id="panel{{ employee.id }}" data-user-id="{{ employee.id }}">
    <div class="oh-general__tab-target oh-profile-section">
      <script>
        $(document).ready(function () {
          checkSelected(`{{employee.employee_user_id.user_permissions.all|user_perms|safe}}`, '#panel{{employee.id}}', true)
        })
      </script>
      {% if perms.auth.add_permission or perms.auth.change_permission or perms.auth.delete_permission %}
        {% include 'base/auth/permission_table.html' %}
      {% endif %}
    </div>
  </div>
{% endfor %}
{% if employees.has_previous or employees.has_next %}
<div class="oh-wrapper w-100">
  <div class="oh-pagination">
    <span class="oh-pagination__page" data-toggle="modal" data-target="#addEmployeeModal">{% trans 'Page' %} {{ employees.number }} {% trans 'of' %} {{ employees.paginator.num_pages }}.</span>

    <nav class="oh-pagination__nav">
      <div class="oh-pagination__input-container me-3">
        <span class="oh-pagination__label me-1">{% trans 'Page' %}</span>

        <input type="number" name="page" class="oh-pagination__input" value="{{ employees.number }}" hx-get="{% url 'permission-search' %}?{{ pd }}" hx-target="#permissionContainer" min="1" />
        <span class="oh-pagination__label">{% trans 'of' %} {{ employees.paginator.num_pages }}</span>
      </div>

      <ul class="oh-pagination__items">
        {% if employees.has_previous %}
          <li class="oh-pagination__item oh-pagination__item--wide">
            <a hx-target="#permissionContainer" hx-get="{% url 'permission-search' %}?{{ pd }}&page=1" class="oh-pagination__link">{% trans 'First' %}</a>
          </li>
          <li class="oh-pagination__item oh-pagination__item--wide">
            <a hx-target="#permissionContainer" hx-get="{% url 'permission-search' %}?{{ pd }}&page={{ employees.previous_page_number }}" class="oh-pagination__link">{% trans 'Previous' %}</a>
          </li>
        {% endif %}
        {% if employees.has_next %}
          <li class="oh-pagination__item oh-pagination__item--wide">
            <a hx-target="#permissionContainer" hx-get="{% url 'permission-search' %}?{{ pd }}&page={{ employees.next_page_number }}" class="oh-pagination__link">{% trans 'Next' %}</a>
          </li>
          <li class="oh-pagination__item oh-pagination__item--wide">
            <a hx-target="#permissionContainer" hx-get="{% url 'permission-search' %}?{{ pd }}&page={{ employees.paginator.num_pages }}" class="oh-pagination__link">{% trans 'Last' %}</a>
          </li>
        {% endif %}
      </ul>
    </nav>
  </div>
</div>
{% endif %}

<script>
  function selectAllPermissions(elem) {
      $(elem).closest('.oh-sticky-table__thead').siblings('.oh-sticky-table__tbody').find('.row-permission').prop('checked',$(elem).is(':checked')).change()
  }
	$(document).ready(function () {
		updateBadge();
		$("[type=checkbox]").change(function (e) {
			e.preventDefault();
		});
	});
	var timeout

	function updatePermissions(elem){
		var permissions = [];
		var panelId =
		"#" +
		$(elem).closest(".panel.view-employees").closest(".panel").attr("id");
		var userId = $(panelId).attr("data-user-id");

		$(panelId + " [name=permissions]").each(function () {
			if ($(this).is(":checked")) {
				var permissionValue = $(this).val();
				permissions.push(permissionValue);
			}
		});

		$.ajax({
			type: "post",
			url: '{% url "update-user-permission" %}',
			traditional: true,
			data: {
				csrfmiddlewaretoken: getCookie("csrftoken"),
				permissions: permissions,
				employee: userId,
			},
			success: function (response) {
				$("#messages").html(
					$(`
						<div class="oh-alert oh-alert--animated oh-alert--${response.type}">
							${response.message}.
						</div>
					`)
				);
			},
			error: function (response) {
				$("#messages").html(
					$(`
						<div class="oh-alert oh-alert--animated oh-alert--danger">
							Sever error.
						</div>
					`)
				);
			},
		});
	}

	$(".view-employees [name=permissions]").on("change",function (e) {
		e.preventDefault();
		clearTimeout(timeout);
		timeout = setTimeout(function() {
      updateBadge($(this));
			updatePermissions(e.target);
		}, 100);
	});

</script>
