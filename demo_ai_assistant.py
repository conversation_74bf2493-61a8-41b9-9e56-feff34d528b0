#!/usr/bin/env python
"""
AI Assistant <PERSON><PERSON>

This script demonstrates the AI assistant functionality with various commands.
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'eaglora.settings')
django.setup()

from utils.intent_extractor import extract_command_intent
from ai_assistant.models import AIAssistantSettings


def demo_ai_assistant():
    """Demonstrate AI assistant capabilities."""
    
    print("🤖 AI Assistant for HRMS - Live Demo")
    print("=" * 60)
    
    # Show AI settings
    settings = AIAssistantSettings.get_settings()
    print(f"🔧 Configuration:")
    print(f"   Model: {settings.ollama_model}")
    print(f"   LangChain: {'Enabled' if settings.enable_langchain else 'Disabled'}")
    print(f"   Status: {'Active' if settings.is_active else 'Inactive'}")
    print()
    
    # Demo commands
    demo_commands = [
        {
            "category": "👥 Employee Management",
            "commands": [
                "Show all employees",
                "Show employees in Marketing department",
                "Find employee named <PERSON>",
                "Add new employee",
                "Export employee data",
            ]
        },
        {
            "category": "🏖️ Leave Management", 
            "commands": [
                "Apply leave for <PERSON> from Dec 25 to Jan 2",
                "Show pending leave requests",
                "Check my leave balance",
                "Open leave request form",
            ]
        },
        {
            "category": "⏰ Attendance Tracking",
            "commands": [
                "Show attendance for November",
                "Export attendance data for June",
                "Check who is absent today",
                "View my attendance summary",
            ]
        },
        {
            "category": "💰 Payroll Processing",
            "commands": [
                "Run payroll for this month",
                "Get payroll of Sethu",
                "Show highest paid employees",
                "Generate payroll report",
            ]
        },
        {
            "category": "🧭 Navigation & Actions",
            "commands": [
                "Go to dashboard",
                "Open settings",
                "Sort employees by salary descending",
                "Navigate to reports section",
            ]
        }
    ]
    
    for category_data in demo_commands:
        print(f"\n{category_data['category']}")
        print("-" * 40)
        
        for command in category_data['commands']:
            result = extract_command_intent(command)
            
            # Determine action icon
            action_icons = {
                'search': '🔍',
                'fill_form': '📝', 
                'run_action': '⚡',
                'export': '📊',
                'navigate': '🧭',
                'sort': '📈',
                'reply': '💬'
            }
            
            icon = action_icons.get(result['intent'], '❓')
            confidence_bar = "█" * int(result['confidence'] * 10)
            
            print(f"  {icon} \"{command}\"")
            print(f"     → {result['message']}")
            print(f"     → Intent: {result['intent']} | Module: {result['module']}")
            print(f"     → Confidence: {confidence_bar} {result['confidence']:.1f}")
            
            if result['filters']:
                print(f"     → Filters: {result['filters']}")
            
            print()


def interactive_demo():
    """Interactive demo where user can type commands."""
    
    print("\n🎮 Interactive Demo")
    print("=" * 40)
    print("Type your commands below (or 'quit' to exit):")
    print("Examples:")
    print("  - Show all employees in IT department")
    print("  - Apply leave for John next week") 
    print("  - Export payroll data")
    print("  - Run attendance report")
    print()
    
    while True:
        try:
            user_input = input("💬 You: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
            
            if not user_input:
                continue
            
            result = extract_command_intent(user_input)
            
            print(f"🤖 AI: {result['message']}")
            print(f"   📊 Analysis: {result['intent']} → {result['module']} (confidence: {result['confidence']:.1f})")
            
            if result['filters']:
                print(f"   🔍 Detected filters: {result['filters']}")
            
            if result.get('sort_by'):
                print(f"   📈 Sorting: {result['sort_by']} ({result.get('order', 'asc')})")
            
            print()
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")


def main():
    """Main demo function."""
    
    try:
        demo_ai_assistant()
        
        print("\n" + "=" * 60)
        print("✅ AI Assistant is working perfectly!")
        print("\n🌐 To use in your browser:")
        print("1. Make sure Django server is running: python manage.py runserver")
        print("2. Visit: http://127.0.0.1:8000/")
        print("3. Login with your HRMS credentials")
        print("4. Look for the floating AI robot button 🤖")
        print("5. Click it and start chatting!")
        
        print("\n🔧 Advanced Setup (Optional):")
        print("1. Install Ollama: https://ollama.ai/")
        print("2. Pull a model: ollama pull mistral")
        print("3. The AI will automatically use LangChain for better responses")
        
        # Ask if user wants interactive demo
        response = input("\n🎮 Would you like to try the interactive demo? (y/n): ").lower()
        if response in ['y', 'yes']:
            interactive_demo()
            
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
