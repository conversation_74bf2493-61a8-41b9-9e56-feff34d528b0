{% load i18n %} {% load basefilters %} {{ form.non_field_errors }}
{% for field in form.visible_fields %}
{% if field.name|startswith:"work_type" and not field.field.required %}
    <div
      class="oh-input__group pt-3"
      style="display: flex"
      id="moreWorkTypeContainer_{{ forloop.counter }}"
        >
      {{ field }}
      <a
        hx-get="{% url 'add-remove-work-type-fields' %}"
        class="oh-btn oh-btn--danger-outline oh-btn--light-bkg"
        hx-target="#moreWorkTypeContainer_{{ forloop.counter }}"
        hx-swap="outerHTML"
        id="delete-link"
      >
        <ion-icon name="trash-outline"></ion-icon>
      </a>
    </div>
{% else %}
    <div class="oh-input__group">
      <label
        class="oh-label {% if field.field.required %}required-star{% endif %}"
        for="id_{{ field.name }}"
        title="{{ field.help_text|safe }}"
      >
        {% trans field.label %}
      </label>
      {{field}}
    </div>
{% endif %} {% endfor %}
<div id="moreWorkTypeContainer_{{form.work_type_counts|add:2}}" class="pt-2" style="text-align: end">
  <a
    hx-target="#moreWorkTypeContainer_{{form.work_type_counts|add:2}}"
    hx-swap="outerHTML"
    hx-post="{% url 'add-remove-work-type-fields' %}"
    role="button"
    style="color: green"
  >
    {% trans "Add more work types.." %}
  </a>
</div>
