{% load i18n %}
{% load widget_tweaks %}
<div class="oh-modal__dialog">
    <div class="oh-modal__dialog-header">
        <h2 class="oh-modal__dialog-title" id="ModalTitle">
            {% if grace_id %}
                {% trans 'Update grace time' %}
            {% else %}
                {% trans 'Create grace time' %}
            {% endif %}
        </h2>
        <button class="oh-modal__close" aria-label="Close" ><ion-icon name="close-outline"></ion-icon></button>
    </div>
    <div class="oh-modal__dialog-body" id="">
        {% if form.errors %}
        <!-- form errors -->
        <div class="oh-wrapper">
            <div class="oh-alert-container">
                {% for error in form.non_field_errors %}
                <div class="oh-alert oh-alert--animated oh-alert--danger">{{ error }}</div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
        <form class="oh-profile-section" hx-encoding="multipart/form-data"
            {% if grace_id %}
                hx-post="{% url 'update-grace-time' grace_id %}"
                hx-target="#objectUpdateModalTarget"
            {% else %}
                {% if is_default %}
                    hx-post="{% url 'create-grace-time' %}?default=True"
                    hx-target="#objectCreateModalTarget"
                {% else %}
                    hx-post="{% url 'create-grace-time' %}?default=False"
                    hx-target="#objectCreateModalTarget"
                {% endif %}
            {% endif %}
            >
            {% csrf_token %}
            <div class="row">
                <div class="col-12">
                    <div class="oh-input__group">
                        <label class="oh-input__label" for="{{ form.allowed_time.id_for_label }}">{{ form.allowed_time.label }}</label>
                        {{ form.allowed_time }}
                        {{ form.allowed_time.errors }}
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-12 col-sm-12 col-md-6 col-lg-6">
                    <div class="oh-input__group">
                        <div style="display: flex;">
                            <label class="oh-input__label" for="{{ form.allowed_clock_in.id_for_label }}">{{ form.allowed_clock_in.label }}</label>
                            <span class="oh-info mt-2" title="{{form.allowed_clock_in.help_text|safe }}"></span>
                        </div>
                        <div class="oh-switch p-2 pt-0">
                            {{ form.allowed_clock_in|add_class:"oh-switch__checkbox" }}
                        </div>
                        {{ form.allowed_clock_in.errors }}
                    </div>
                </div>
                <div class="col-12 col-sm-12 col-md-6 col-lg-6">
                    <div class="oh-input__group">
                        <div style="display: flex;">
                            <label class="oh-input__label" for="{{ form.allowed_clock_out.id_for_label }}">{{ form.allowed_clock_out.label }}</label>
                            <span class="oh-info mt-2" title="{{form.allowed_clock_out.help_text|safe }}"></span>
                        </div>
                        <div class="oh-switch p-2 pt-0">
                            {{ form.allowed_clock_out }}
                        </div>
                        {{ form.allowed_clock_out.errors }}
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-12 col-sm-12 col-md-6 col-lg-6">
                    <div class="oh-input__group">
                        <div style="display: flex;">
                            <label class="oh-input__label" for="{{ form.shifts.id_for_label }}">{% trans 'Shifts' %}</label>
                            <span class="oh-info mt-2" title="{{ form.shifts.help_text|safe }}"></span>
                        </div>
                        {{ form.shifts }}
                        {{ form.shifts.errors }}
                    </div>
                </div>
                <div class="col-12 col-sm-12 col-md-6 col-lg-6">
                    <div class="oh-input__group">
                        <label class="oh-input__label" for="{{ form.company_id.id_for_label }}">{% trans 'Company' %}</label>
                        {{ form.company_id }}
                        {{ form.company_id.errors }}
                    </div>
                </div>

            </div>
            {{form.is_default}}
            <div class="oh-modal__dialog-footer p-0 mt-3">
                <button type="submit" class="oh-btn oh-btn--secondary oh-btn--shadow">
                    {% trans "Save" %}
                </button>
            </div>
        </form>
    </div>
</div>
