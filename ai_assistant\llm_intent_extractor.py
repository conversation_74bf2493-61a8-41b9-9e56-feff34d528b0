"""
LLM-Enhanced Intent Extractor

This module provides advanced intent extraction using LLM reasoning
instead of just pattern matching.
"""

import json
import logging
from typing import Dict, Any, Optional
from datetime import datetime

# Ollama imports with fallback
try:
    from langchain.llms import Ollama
    from langchain.prompts import PromptTemplate
    from langchain.schema import BaseOutputParser
    LANGCHAIN_AVAILABLE = True
except ImportError:
    LANGCHAIN_AVAILABLE = False

from .models import AIAssistantSettings

logger = logging.getLogger(__name__)


class IntentOutputParser(BaseOutputParser):
    """Parse LLM output into structured intent format."""
    
    def parse(self, text: str) -> Dict[str, Any]:
        """Parse the LLM output into a structured dictionary."""
        try:
            # Try to extract JSON from the response
            if '{' in text and '}' in text:
                start = text.find('{')
                end = text.rfind('}') + 1
                json_str = text[start:end]
                return json.loads(json_str)
            else:
                # If no JSON found, create a basic structure
                return {
                    'intent': 'reply',
                    'module': 'general',
                    'filters': {},
                    'message': text,
                    'confidence': 0.5
                }
        except json.JSONDecodeError:
            return {
                'intent': 'reply',
                'module': 'general',
                'filters': {},
                'message': text,
                'confidence': 0.3
            }


class LLMIntentExtractor:
    """Enhanced intent extractor using LLM reasoning."""
    
    def __init__(self):
        self.settings = AIAssistantSettings.get_settings()
        self.llm = None
        self.parser = IntentOutputParser()
        
        if LANGCHAIN_AVAILABLE and self.settings.enable_langchain:
            self._initialize_llm()
    
    def _initialize_llm(self):
        """Initialize the LLM for intent extraction."""
        try:
            self.llm = Ollama(
                model=self.settings.ollama_model,
                base_url=self.settings.ollama_base_url,
                temperature=0.3,  # Lower temperature for more consistent intent extraction
                num_predict=500,  # Shorter responses for intent extraction
            )
            logger.info("LLM initialized for intent extraction")
        except Exception as e:
            logger.error(f"Failed to initialize LLM: {e}")
            self.llm = None
    
    def extract_intent(self, text: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Extract intent from user text using LLM reasoning.
        
        Args:
            text: User's natural language input
            context: Optional context information
            
        Returns:
            Structured intent dictionary
        """
        if self.llm and LANGCHAIN_AVAILABLE:
            return self._extract_with_llm(text, context)
        else:
            # Fallback to rule-based extraction
            from utils.intent_extractor import extract_command_intent
            return extract_command_intent(text)
    
    def _extract_with_llm(self, text: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Extract intent using LLM reasoning."""
        try:
            prompt = self._build_intent_prompt(text, context)
            response = self.llm(prompt)
            return self.parser.parse(response)
        except Exception as e:
            logger.error(f"LLM intent extraction failed: {e}")
            # Fallback to rule-based extraction
            from utils.intent_extractor import extract_command_intent
            return extract_command_intent(text)
    
    def _build_intent_prompt(self, text: str, context: Optional[Dict[str, Any]] = None) -> str:
        """Build the prompt for intent extraction."""
        current_date = datetime.now().strftime("%Y-%m-%d")
        
        prompt = f"""You are an expert at understanding user intents in an HRMS (Human Resource Management System).

Current Date: {current_date}
"""
        
        if context:
            prompt += f"Current Context: {json.dumps(context, indent=2)}\n"
        
        prompt += f"""
User Input: "{text}"

Analyze the user's intent and extract the following information:

1. **Intent**: What does the user want to do?
   - search: Find/view data (employees, leave, attendance, payroll)
   - fill_form: Create/fill a form (leave request, employee form)
   - run_action: Execute an action (run payroll, process data)
   - export: Download/export data
   - import: Upload/import data
   - navigate: Go to a specific page/section
   - sort: Sort existing data
   - reply: General conversation/help

2. **Module**: Which HRMS module is involved?
   - employee: Employee management
   - leave: Leave management
   - attendance: Attendance tracking
   - payroll: Payroll processing
   - recruitment: Hiring and recruitment
   - general: General/unclear

3. **Filters**: What specific criteria or filters are mentioned?
   - Extract names, departments, dates, locations, etc.

4. **Actions**: What specific actions should be taken?
   - URL to redirect to
   - Data to prefill in forms
   - Sorting preferences

5. **Confidence**: How confident are you in this interpretation? (0.0 to 1.0)

Examples:
- "Show all employees in Marketing" → search intent, employee module, department filter
- "Apply leave for John from July 10 to 12" → fill_form intent, leave module, prefill data
- "Export attendance for June" → export intent, attendance module, month filter
- "Run payroll for sales team" → run_action intent, payroll module, department filter

Respond with a JSON object in this exact format:
{{
  "intent": "search|fill_form|run_action|export|import|navigate|sort|reply",
  "module": "employee|leave|attendance|payroll|recruitment|general",
  "filters": {{
    "name": "extracted name if any",
    "department": "extracted department if any",
    "location": "extracted location if any",
    "date": "extracted date if any",
    "month": "extracted month if any",
    "status": "extracted status if any"
  }},
  "sort_by": "field to sort by if mentioned",
  "order": "asc|desc",
  "redirect_url": "suggested URL path if applicable",
  "prefill_data": {{
    "field_name": "value to prefill"
  }},
  "message": "Brief explanation of what will be done",
  "confidence": 0.8
}}

Only include filters, sort_by, redirect_url, and prefill_data if they are relevant to the user's request.
"""
        
        return prompt


class HybridIntentExtractor:
    """
    Hybrid intent extractor that combines LLM reasoning with rule-based fallback.
    """
    
    def __init__(self):
        self.llm_extractor = LLMIntentExtractor()
        
    def extract_intent(self, text: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Extract intent using hybrid approach.
        
        Args:
            text: User's natural language input
            context: Optional context information
            
        Returns:
            Structured intent dictionary
        """
        try:
            # First try LLM extraction
            result = self.llm_extractor.extract_intent(text, context)
            
            # Validate and enhance the result
            result = self._validate_and_enhance(result, text)
            
            return result
            
        except Exception as e:
            logger.error(f"Hybrid intent extraction failed: {e}")
            # Final fallback to rule-based extraction
            from utils.intent_extractor import extract_command_intent
            return extract_command_intent(text)
    
    def _validate_and_enhance(self, result: Dict[str, Any], original_text: str) -> Dict[str, Any]:
        """Validate and enhance the LLM result."""
        # Ensure required fields exist
        if 'intent' not in result:
            result['intent'] = 'reply'
        
        if 'module' not in result:
            result['module'] = 'general'
        
        if 'filters' not in result:
            result['filters'] = {}
        
        if 'confidence' not in result:
            result['confidence'] = 0.5
        
        if 'message' not in result:
            result['message'] = "I understand your request."
        
        # Enhance with rule-based patterns for better accuracy
        result = self._enhance_with_patterns(result, original_text)
        
        return result
    
    def _enhance_with_patterns(self, result: Dict[str, Any], text: str) -> Dict[str, Any]:
        """Enhance LLM result with rule-based patterns."""
        text_lower = text.lower()
        
        # Enhance confidence based on pattern matches
        confidence_boost = 0.0
        
        # Check for specific keywords that boost confidence
        if any(word in text_lower for word in ['show', 'list', 'find', 'search', 'get']):
            if result['intent'] == 'search':
                confidence_boost += 0.1
        
        if any(word in text_lower for word in ['create', 'apply', 'fill', 'open']):
            if result['intent'] == 'fill_form':
                confidence_boost += 0.1
        
        if any(word in text_lower for word in ['export', 'download', 'save']):
            if result['intent'] == 'export':
                confidence_boost += 0.1
        
        if any(word in text_lower for word in ['run', 'process', 'execute']):
            if result['intent'] == 'run_action':
                confidence_boost += 0.1
        
        # Apply confidence boost
        result['confidence'] = min(1.0, result['confidence'] + confidence_boost)
        
        return result


# Main function for backward compatibility
def extract_enhanced_intent(text: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Main function to extract intent using enhanced LLM reasoning.
    
    Args:
        text: User's natural language input
        context: Optional context information
        
    Returns:
        Structured intent dictionary
    """
    extractor = HybridIntentExtractor()
    return extractor.extract_intent(text, context)
