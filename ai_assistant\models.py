"""
AI Assistant Models

This module contains Django models for the AI Assistant functionality.
"""

import json
from django.db import models
from django.contrib.auth.models import User
from django.utils.translation import gettext_lazy as _
from django.utils import timezone

from base.eaglora_company_manager import EagloraCompanyManager
from eaglora.models import EagloraModel


class ChatHistory(EagloraModel):
    """Store chat history between users and AI assistant."""
    
    user = models.ForeignKey(
        User, 
        on_delete=models.CASCADE, 
        related_name='ai_chat_history',
        verbose_name=_("User")
    )
    message = models.TextField(verbose_name=_("User Message"))
    response = models.JSONField(verbose_name=_("AI Response"))
    intent = models.CharField(
        max_length=50, 
        blank=True, 
        null=True,
        verbose_name=_("Detected Intent")
    )
    confidence = models.FloatField(
        default=0.0,
        verbose_name=_("Confidence Score")
    )
    processing_time = models.FloatField(
        default=0.0,
        verbose_name=_("Processing Time (seconds)")
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name=_("Created At")
    )
    
    class Meta:
        ordering = ['-created_at']
        verbose_name = _("Chat History")
        verbose_name_plural = _("Chat Histories")
    
    def __str__(self):
        return f"{self.user.username} - {self.intent} - {self.created_at}"
    
    def get_response_message(self):
        """Get the message from the response JSON."""
        try:
            if isinstance(self.response, str):
                response_data = json.loads(self.response)
            else:
                response_data = self.response
            return response_data.get('message', 'No message')
        except (json.JSONDecodeError, AttributeError):
            return 'Invalid response format'


class AIAssistantSettings(EagloraModel):
    """Global settings for AI Assistant."""
    
    # LLM Settings
    ollama_model = models.CharField(
        max_length=100,
        default='mistral',
        verbose_name=_("Ollama Model"),
        help_text=_("The Ollama model to use (mistral, llama3, phi3, gemma)")
    )
    ollama_base_url = models.URLField(
        default='http://localhost:11434',
        verbose_name=_("Ollama Base URL"),
        help_text=_("Base URL for Ollama API")
    )
    
    # Assistant Behavior
    max_tokens = models.IntegerField(
        default=1000,
        verbose_name=_("Max Tokens"),
        help_text=_("Maximum tokens for LLM response")
    )
    temperature = models.FloatField(
        default=0.7,
        verbose_name=_("Temperature"),
        help_text=_("LLM temperature (0.0-1.0)")
    )
    
    # Security Settings
    enable_security_validation = models.BooleanField(
        default=True,
        verbose_name=_("Enable Security Validation")
    )
    max_chat_history = models.IntegerField(
        default=100,
        verbose_name=_("Max Chat History"),
        help_text=_("Maximum chat history entries per user")
    )
    
    # Feature Flags
    enable_langchain = models.BooleanField(
        default=True,
        verbose_name=_("Enable LangChain"),
        help_text=_("Use LangChain for advanced reasoning")
    )
    enable_memory = models.BooleanField(
        default=True,
        verbose_name=_("Enable Memory"),
        help_text=_("Enable conversation memory")
    )
    
    is_active = models.BooleanField(
        default=True,
        verbose_name=_("Is Active")
    )
    
    class Meta:
        verbose_name = _("AI Assistant Settings")
        verbose_name_plural = _("AI Assistant Settings")
    
    def __str__(self):
        return f"AI Assistant Settings - {self.ollama_model}"
    
    @classmethod
    def get_settings(cls):
        """Get the active AI assistant settings."""
        return cls.objects.filter(is_active=True).first() or cls.objects.create()


class ConversationMemory(EagloraModel):
    """Store conversation memory for context-aware responses."""
    
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='ai_conversation_memory',
        verbose_name=_("User")
    )
    session_id = models.CharField(
        max_length=100,
        verbose_name=_("Session ID"),
        help_text=_("Unique session identifier")
    )
    context_data = models.JSONField(
        default=dict,
        verbose_name=_("Context Data"),
        help_text=_("Stored conversation context")
    )
    last_accessed = models.DateTimeField(
        auto_now=True,
        verbose_name=_("Last Accessed")
    )
    expires_at = models.DateTimeField(
        verbose_name=_("Expires At"),
        help_text=_("When this memory expires")
    )
    
    class Meta:
        unique_together = ['user', 'session_id']
        ordering = ['-last_accessed']
        verbose_name = _("Conversation Memory")
        verbose_name_plural = _("Conversation Memories")
    
    def __str__(self):
        return f"{self.user.username} - {self.session_id}"
    
    def is_expired(self):
        """Check if the memory has expired."""
        return timezone.now() > self.expires_at
    
    def update_context(self, new_context):
        """Update the context data."""
        self.context_data.update(new_context)
        self.last_accessed = timezone.now()
        self.save()
