{% load static %}

<!-- AI Assistant Chatbox -->
<div id="ai-chatbox" class="ai-chatbox" style="display: none;">
    <div class="ai-chatbox-header">
        <div class="ai-chatbox-title">
            <i class="fas fa-robot"></i>
            <span>AI Assistant</span>
            <span class="ai-status-indicator" id="ai-status"></span>
        </div>
        <div class="ai-chatbox-controls">
            <button type="button" class="btn btn-sm btn-outline-light" id="ai-minimize-btn">
                <i class="fas fa-minus"></i>
            </button>
            <button type="button" class="btn btn-sm btn-outline-light" id="ai-close-btn">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>
    
    <div class="ai-chatbox-body" id="ai-chatbox-body">
        <div class="ai-chat-messages" id="ai-chat-messages">
            <div class="ai-message ai-assistant-message">
                <div class="ai-message-content">
                    <i class="fas fa-robot ai-avatar"></i>
                    <div class="ai-message-text">
                        Hello! I'm your AI assistant. I can help you with:
                        <ul>
                            <li>🔍 Search employees, leave, attendance, payroll</li>
                            <li>📝 Fill forms and create requests</li>
                            <li>📊 Export and import data</li>
                            <li>🧭 Navigate to different sections</li>
                            <li>💰 Process payroll and run reports</li>
                        </ul>
                        Try asking me something like "Show all employees in Marketing" or "Apply leave for John"!
                    </div>
                </div>
                <div class="ai-message-time">{{ "now"|date:"H:i" }}</div>
            </div>
        </div>
        
        <div class="ai-typing-indicator" id="ai-typing-indicator" style="display: none;">
            <div class="ai-typing-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
            <span class="ai-typing-text">AI is thinking...</span>
        </div>
    </div>
    
    <div class="ai-chatbox-footer">
        <div class="ai-input-container">
            <input type="text" 
                   class="form-control ai-message-input" 
                   id="ai-message-input" 
                   placeholder="Type your message here..."
                   autocomplete="off">
            <button type="button" class="btn btn-primary ai-send-btn" id="ai-send-btn">
                <i class="fas fa-paper-plane"></i>
            </button>
        </div>
        <div class="ai-quick-actions" id="ai-quick-actions">
            <button type="button" class="btn btn-sm btn-outline-secondary ai-quick-btn" data-message="Show all employees">
                👥 All Employees
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary ai-quick-btn" data-message="Open leave form">
                📝 Leave Form
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary ai-quick-btn" data-message="Export attendance">
                📊 Export Data
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary ai-quick-btn" data-message="Go to dashboard">
                🏠 Dashboard
            </button>
        </div>
    </div>
</div>

<!-- AI Assistant Floating Button -->
<div class="ai-floating-btn" id="ai-floating-btn">
    <i class="fas fa-robot"></i>
    <span class="ai-notification-badge" id="ai-notification-badge" style="display: none;">1</span>
</div>

<!-- AI Assistant Styles -->
<style>
.ai-chatbox {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 380px;
    height: 600px;
    background: #fff;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    z-index: 9999;
    display: flex;
    flex-direction: column;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.ai-chatbox-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 20px;
    border-radius: 15px 15px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.ai-chatbox-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
}

.ai-status-indicator {
    width: 8px;
    height: 8px;
    background: #4ade80;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.ai-chatbox-controls {
    display: flex;
    gap: 5px;
}

.ai-chatbox-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.ai-chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    max-height: 400px;
}

.ai-message {
    margin-bottom: 20px;
}

.ai-message-content {
    display: flex;
    align-items: flex-start;
    gap: 10px;
}

.ai-avatar {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    flex-shrink: 0;
}

.ai-user-message .ai-avatar {
    background: #e5e7eb;
    color: #374151;
}

.ai-message-text {
    background: #f3f4f6;
    padding: 12px 16px;
    border-radius: 18px;
    max-width: 280px;
    word-wrap: break-word;
    line-height: 1.4;
}

.ai-assistant-message .ai-message-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.ai-message-time {
    font-size: 11px;
    color: #9ca3af;
    margin-top: 5px;
    margin-left: 42px;
}

.ai-typing-indicator {
    padding: 10px 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.ai-typing-dots {
    display: flex;
    gap: 4px;
}

.ai-typing-dots span {
    width: 6px;
    height: 6px;
    background: #9ca3af;
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
}

.ai-typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.ai-typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% { transform: scale(0); }
    40% { transform: scale(1); }
}

.ai-chatbox-footer {
    padding: 20px;
    border-top: 1px solid #e5e7eb;
}

.ai-input-container {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
}

.ai-message-input {
    flex: 1;
    border-radius: 25px;
    border: 1px solid #d1d5db;
    padding: 10px 15px;
}

.ai-send-btn {
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.ai-quick-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.ai-quick-btn {
    font-size: 11px;
    padding: 4px 8px;
    border-radius: 12px;
}

.ai-floating-btn {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    z-index: 9998;
    transition: all 0.3s ease;
    font-size: 24px;
}

.ai-floating-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.3);
}

.ai-notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #ef4444;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    font-weight: bold;
}

.ai-chatbox.minimized .ai-chatbox-body,
.ai-chatbox.minimized .ai-chatbox-footer {
    display: none;
}

.ai-chatbox.minimized {
    height: auto;
}

/* Responsive design */
@media (max-width: 768px) {
    .ai-chatbox {
        width: calc(100vw - 40px);
        height: calc(100vh - 40px);
        bottom: 20px;
        right: 20px;
        left: 20px;
    }
    
    .ai-floating-btn {
        bottom: 20px;
        right: 20px;
    }
}
</style>

<!-- AI Assistant JavaScript -->
<script>
// AI Assistant Configuration
const AI_CONFIG = {
    commandUrl: '{{ ai_command_url }}',
    currentPage: '{{ current_page }}',
    currentModule: '{{ current_module }}',
    isAuthenticated: {{ is_authenticated|yesno:"true,false" }},
    csrfToken: document.querySelector('[name=csrfmiddlewaretoken]')?.value || ''
};

// AI Assistant Class
class AIAssistant {
    constructor() {
        this.isOpen = false;
        this.isMinimized = false;
        this.messageHistory = [];
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadChatHistory();
    }
    
    bindEvents() {
        // Floating button click
        document.getElementById('ai-floating-btn').addEventListener('click', () => {
            this.toggleChatbox();
        });
        
        // Close button
        document.getElementById('ai-close-btn').addEventListener('click', () => {
            this.closeChatbox();
        });
        
        // Minimize button
        document.getElementById('ai-minimize-btn').addEventListener('click', () => {
            this.toggleMinimize();
        });
        
        // Send button
        document.getElementById('ai-send-btn').addEventListener('click', () => {
            this.sendMessage();
        });
        
        // Enter key in input
        document.getElementById('ai-message-input').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.sendMessage();
            }
        });
        
        // Quick action buttons
        document.querySelectorAll('.ai-quick-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const message = e.target.dataset.message;
                this.sendQuickMessage(message);
            });
        });
    }
    
    toggleChatbox() {
        const chatbox = document.getElementById('ai-chatbox');
        const floatingBtn = document.getElementById('ai-floating-btn');
        
        if (this.isOpen) {
            this.closeChatbox();
        } else {
            chatbox.style.display = 'flex';
            floatingBtn.style.display = 'none';
            this.isOpen = true;
            this.focusInput();
        }
    }
    
    closeChatbox() {
        const chatbox = document.getElementById('ai-chatbox');
        const floatingBtn = document.getElementById('ai-floating-btn');
        
        chatbox.style.display = 'none';
        floatingBtn.style.display = 'flex';
        this.isOpen = false;
        this.isMinimized = false;
        chatbox.classList.remove('minimized');
    }
    
    toggleMinimize() {
        const chatbox = document.getElementById('ai-chatbox');
        
        if (this.isMinimized) {
            chatbox.classList.remove('minimized');
            this.isMinimized = false;
        } else {
            chatbox.classList.add('minimized');
            this.isMinimized = true;
        }
    }
    
    focusInput() {
        setTimeout(() => {
            document.getElementById('ai-message-input').focus();
        }, 100);
    }
    
    sendMessage() {
        const input = document.getElementById('ai-message-input');
        const message = input.value.trim();
        
        if (!message) return;
        
        this.addUserMessage(message);
        input.value = '';
        this.showTypingIndicator();
        
        this.processMessage(message);
    }
    
    sendQuickMessage(message) {
        this.addUserMessage(message);
        this.showTypingIndicator();
        this.processMessage(message);
    }
    
    addUserMessage(message) {
        const messagesContainer = document.getElementById('ai-chat-messages');
        const messageElement = this.createMessageElement(message, 'user');
        messagesContainer.appendChild(messageElement);
        this.scrollToBottom();
    }
    
    addAssistantMessage(message, intent = 'reply') {
        const messagesContainer = document.getElementById('ai-chat-messages');
        const messageElement = this.createMessageElement(message, 'assistant', intent);
        messagesContainer.appendChild(messageElement);
        this.scrollToBottom();
    }
    
    createMessageElement(message, sender, intent = 'reply') {
        const messageDiv = document.createElement('div');
        messageDiv.className = `ai-message ai-${sender}-message`;
        
        const now = new Date();
        const timeString = now.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
        
        const iconClass = sender === 'assistant' ? 'fas fa-robot' : 'fas fa-user';
        
        messageDiv.innerHTML = `
            <div class="ai-message-content">
                <i class="${iconClass} ai-avatar"></i>
                <div class="ai-message-text">${this.formatMessage(message)}</div>
            </div>
            <div class="ai-message-time">${timeString}</div>
        `;
        
        return messageDiv;
    }
    
    formatMessage(message) {
        // Basic HTML formatting
        return message
            .replace(/\n/g, '<br>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>');
    }
    
    showTypingIndicator() {
        document.getElementById('ai-typing-indicator').style.display = 'flex';
        this.scrollToBottom();
    }
    
    hideTypingIndicator() {
        document.getElementById('ai-typing-indicator').style.display = 'none';
    }
    
    scrollToBottom() {
        const messagesContainer = document.getElementById('ai-chat-messages');
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }
    
    async processMessage(message) {
        try {
            const response = await fetch(AI_CONFIG.commandUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': AI_CONFIG.csrfToken
                },
                body: JSON.stringify({
                    message: message,
                    context: {
                        current_page: AI_CONFIG.currentPage,
                        current_module: AI_CONFIG.currentModule
                    }
                })
            });
            
            const data = await response.json();
            this.hideTypingIndicator();
            
            if (response.ok) {
                this.handleResponse(data);
            } else {
                this.addAssistantMessage('Sorry, I encountered an error processing your request.', 'error');
            }
            
        } catch (error) {
            this.hideTypingIndicator();
            this.addAssistantMessage('Sorry, I\'m having trouble connecting. Please try again.', 'error');
            console.error('AI Assistant Error:', error);
        }
    }
    
    handleResponse(data) {
        // Add assistant message
        this.addAssistantMessage(data.message, data.intent);
        
        // Handle different actions
        switch (data.action) {
            case 'redirect':
                if (data.url) {
                    setTimeout(() => {
                        window.location.href = data.url;
                    }, 1000);
                }
                break;
                
            case 'download':
                if (data.url) {
                    const link = document.createElement('a');
                    link.href = data.url;
                    link.download = '';
                    link.click();
                }
                break;
                
            case 'ajax_update':
                // Handle AJAX updates (could be implemented for dynamic content)
                console.log('AJAX update requested:', data);
                break;
        }
    }
    
    loadChatHistory() {
        // This could load recent chat history from the server
        // For now, we'll just show the welcome message
    }
}

// Initialize AI Assistant when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    if (AI_CONFIG.isAuthenticated) {
        window.aiAssistant = new AIAssistant();
    }
});
</script>
