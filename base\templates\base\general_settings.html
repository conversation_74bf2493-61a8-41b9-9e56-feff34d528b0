{% extends 'settings.html' %} {% block settings %}{% load i18n %} {% load eaglorafilters %}

{% if perms.base.change_announcementexpire %}
{% include "announcement/expiry_day.html" %}
{% endif %}

{% if perms.base.view_dynamicpagination %}
    {% include "base/dynamic_pagination/pagination_settings.html" %}
{% endif %}

{% if perms.eaglora_audit.view_accountblockunblock %}
    {% include "base/audit_tag/employee_account_block_unblock.html" %}
{% endif %}


{% if "offboarding"|app_installed and perms.offboarding.change_offboardinggeneralsetting %}
    {% include "offboarding/settings/settings.html" %}
{% endif %}

{% if "attendance"|app_installed and perms.attendance.change_attendancegeneralsetting %}
    {% include "attendance/settings/settings.html" %}
{% endif %}

{% if "payroll"|app_installed and perms.payroll.change_payrollgeneralsetting %}
    {% include "payroll/settings/settings.html" %}
{% endif %}

{% if perms.employee.change_employeegeneralsetting %}
    {% include "settings/settings.html" %}
{% endif %}


{% if "payroll"|app_installed %}
    {% if perms.payroll.change_encashmentgeneralsetting %}
        {% include "settings/encashment_settings.html" %}
    {% endif %}
{% endif %}

{% if perms.base.view_historytrackingfields %}
    {% include "base/audit_tag/history_tracking_fields.html" %}
{% endif %}

{% if "payroll"|app_installed and perms.payroll.view_payrollsettings %}
    {% include "payroll/settings/payroll_settings.html" %}
{% endif %}


{% endblock settings %}
