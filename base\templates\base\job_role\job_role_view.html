{% load i18n %}
<div class="row">
	<div class="col-12 col-sm-12 col-md-12 col-lg-12">
		<div class="">
			<div class="oh-card__body">
				<div class="oh-sticky-table oh-sticky-table--no-highlight">
					<div class="oh-sticky-table__table">
						<div class="oh-sticky-table__thead">
							<div class="oh-sticky-table__tr">
								<div class="oh-sticky-table__th">
									{% trans "Job Position" %}
								</div>
								<div class="oh-sticky-table__th">{% trans "Job Role" %}</div>
							</div>
						</div>

						<div class="oh-sticky-table__tbody">
							{% for job in job_positions %}
								<div class="oh-sticky-table__tr oh-permission-table__tr oh-permission-table--collapsed"
									data-count="{{job.jobrole_set.all|length}}" data-label="Job Role">
									<div class="oh-sticky-table__sd oh-permission-table--toggle">
										<div class="d-flex align-items-center">
											<button class="oh-permission-table__collapse me-2" onclick="updateUserPanelCount(this);">
												<ion-icon class="oh-permission-table__collapse-down"
													title="{% trans 'Reveal' %}" name="chevron-down-outline"></ion-icon>
												<ion-icon class="oh-permission-table__collapse-up"
													title="{% trans 'Collapse' %}" name="chevron-up-outline"></ion-icon>
											</button>
											<span class="oh-permission-table__user">{{job}}</span>
										</div>
									</div>
									<div class="oh-sticky-table__td">
										<span class="oh-permission-count">
											{{job.jobrole_set.all|length}}
											{% trans "Job Roles" %}
										</span>
										{% for role in job.jobrole_set.all %}
											<div id="jobRoleDiv{{role.id}}">
												<span class="oh-user-panel oh-collapse-panel">
													<div class="oh-profile oh-profile--md">
														<div class="oh-profile__avatar mr-1">
															<img src="https://ui-avatars.com/api/?name={{role}}&background=random"
																class="oh-profile__image" alt="Baby C." />
														</div>
														<span class="oh-profile__name oh-text--dark">{{role|capfirst}}</span>
													</div>
													<div>
														<div class="d-flex">
															{% if perms.base.change_jobrole %}
																<div class="m-2">
																	<a hx-get="{% url 'job-role-update' role.id %}"
																		hx-target="#jobRoleForm" data-toggle="oh-modal-toggle"
																		data-target="#jobRoleModal" class="mr-3 float-left"
																		title="{% trans 'Edit' %}">
																		<ion-icon name="pencil-outline"></ion-icon>
																	</a>
																</div>
															{% endif %}
															{% if perms.base.delete_jobrole %}
																<div class="m-2">
																	<form
																		hx-confirm="{% trans 'Are you sure you want to delete this job role?' %}"
																		hx-on-htmx-after-request="reloadMessage(this);"
																		hx-post="{% url 'job-role-delete' role.id %}"
																		hx-target="#jobRoleDiv{{role.id}}"
																		hx-swap="outerHTML"
																		class="mr-3 float-left">
																		<button role="submit" style="background: none; color: inherit; border: none; padding: 0; font: inherit; cursor: pointer; outline: none;" title="{% trans 'Remove' %}">
																			<ion-icon name="close-outline" role="img" class="md hydrated" aria-label="close outline"></ion-icon>
																		</button>
																		{% csrf_token %}
																	</form>
																</div>
															{% endif %}
														</div>
													</div>
												</span>
											</div>
										{% endfor %}
									</div>
								</div>
							{% endfor %}
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
